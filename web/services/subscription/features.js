const features = {
    "basic_customer_segments_wo_export": {
        new_vs_returning: true,
        basic_segments: true,
        // transaction_frequency and transaction_frequency_breakdowns are not separated right now
        transaction_frequency: true
    },
    "all_customer_segments": {
        rfm_segments: true,
        rfm_segments_tagging: true,
        new_vs_returning: true,
        basic_segments: true,
        transaction_frequency: true
    },
    "all_product_analysis": {
        basket_analysis: true,
        basket_analysis_export: true,
        repurchase_rate: true,
        repurchase_rate_export: true,
    },
    "all_integrations": {
        facebook_ads_overview: true,
        google_ads_overview: true,
        google_analytics_overview: true,
        blended_ads_overview: true
    },
    "product_performance": {
        product_performance: true
    },
    "ai_agents": {
        prashna_ai: true,
        auto_insights: true
    },
    "all_features" : {
        ltv_cohorts: true,
        ltv_cohorts_export : true,
        product_cohorts: true,
        product_cohorts_export: true,
        location_cohorts: true,
        location_cohorts_export: true,
        custom_cohorts: true,
        custom_cohorts_export: true,
        cohort_filters : true,
        rfm_segments: true,
        rfm_segments_export: true,
        rfm_segments_tagging: true,
        new_vs_returning: true,
        new_vs_returning_export: true,
        basic_segments: true,
        basic_segments_export: true,
        transaction_frequency: true,
        transaction_frequency_export: true,
        repurchase_rate: true,
        repurchase_rate_export: true,
        basket_analysis: true,
        basket_analysis_export: true,
        industry_benchmarks: true,
        facebook_ads_overview: true,
        google_ads_overview: true,
        google_analytics_overview: true,
        blended_ads_overview: true,
        scheduled_reports: true,
        connect_multiple_accounts: true
    }
}

const limits = {
    "data_time_limit_3_months": {
        data_time_limit: "3_months"
    },
    "data_time_limit_1_year": {
        data_time_limit: "1_year"
    },
    "data_time_limit_unlimited": {
        data_time_limit: "unlimited"
    },
    // "shop_limit_1": {
    //     shop_limit: 1
    // },
    // "shop_limit_unlimited" : {
    //     shop_limit: "unlimited"
    // },
    "order_limit_500": {
        order_limit: 500
    },
    "order_limit_2000": {
        order_limit: 2000
    },
    "order_limit_unlimited": {
        order_limit: "unlimited"
    },
    "all_unlimited" : {
        data_time_limit: "unlimited",
        // shop_limit: "unlimited",
        order_limit: "unlimited"
    }
}

export { features, limits }