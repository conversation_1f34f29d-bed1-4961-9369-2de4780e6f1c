import { cubeAdminApiRequest } from "./api.js";
import logger from '../logger.js';
import dayjs from "dayjs";
import quarterOfYear from "dayjs/plugin/quarterOfYear";
import isoWeek from "dayjs/plugin/isoWeek";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(quarterOfYear);
dayjs.extend(isoWeek)
import util from '../common/util.js';
import numeral from 'numeral';
import shop from '../shop.js';
import { cache } from '../redis.js';

const MAX_LIMIT_CUBEJS = 500000; 
const CUBE_CACHE_TTL = 60 * 60 * 2; // 2 hours

async function rfmSegmentExport(shop_id, segment, period = "all_time") {
  const filters = [
    {
      member: "RfmSegments.period",
      operator: "equals",
      values: [period]
    },
    {
      member: "RfmSegments.shopId",
      operator: "equals",
      values: [shop_id],
    }
  ];

  if (segment && segment !== "all") {
    filters.push({
      member: "RfmSegments.rfmSegment",
      operator: "equals",
      values: [segment]
    });
  }

  const query = {
      "limit": MAX_LIMIT_CUBEJS,
      "offset": 0,
      "dimensions": [
          "RfmSegments.customerId",
          "RfmSegments.rfmScore",
          "RfmSegments.rfmSegment",
          "RfmSegments.customerFirstName",
          "RfmSegments.customerEmail",
          "RfmSegments.customerPhone",
          "RfmSegments.customerLastName"
      ],
      "filters": filters,
      "measures": [
          "RfmSegments.lastOrderSince",
          "RfmSegments.totalOrders",
          "RfmSegments.totalSpend",
          "RfmSegments.lastOrderCreatedAt"
      ],
      "order": {
          "RfmSegments.lastOrderCreatedAt": "desc"
      }
  }

    try {
      const useMigrationDataset = await shop.shouldUseMigrationDataset(shop_id);
      const dataResultSet = await cubeAdminApiRequest(query, useMigrationDataset);
      const dataResult = dataResultSet.data || [];

      const formattedResponse = dataResult.map((row) => {
      return {
        customer_id: row["RfmSegments.customerId"],
        rfm_score: row["RfmSegments.rfmScore"],
        email:  row["RfmSegments.customerEmail"],
        segment: row["RfmSegments.rfmSegment"],
        first_name: row["RfmSegments.customerFirstName"],
        last_name: row["RfmSegments.customerLastName"],
        phone: row["RfmSegments.customerPhone"],
        last_order_since: row["RfmSegments.lastOrderSince"],
        total_orders: row["RfmSegments.totalOrders"],
        total_spend: row["RfmSegments.totalSpend"],
        last_order_at: row["RfmSegments.lastOrderCreatedAt"]
      }
    });

    return {
      data : formattedResponse,
      fields: ["customer_id", "rfm_score", "email", "segment", "first_name", "last_name", "phone", "last_order_since", "total_orders", "total_spend", "last_order_at"]
    };
  } catch (error) {
    logger.error("Error fetching data:", {
      error: error,
      query: query
    });
    return {error: "Error fetching RFM Segment Customers"};
  }
}

async function fetchRFMSegmentCustomers(shop_id, segment, period = "all_time") {
  const filters = [
    {
      member: "RfmSegments.period",
      operator: "equals",
      values: [period]
    },
    {
      member: "RfmSegments.shopId",
      operator: "equals",
      values: [shop_id],
    },
    {
      member: "RfmSegments.customerEmail",
      operator: "notEquals",
      values: [""]
    }
  ];

  if (segment && segment !== "all") {
    filters.push({
      member: "RfmSegments.rfmSegment",
      operator: "equals",
      values: [segment]
    });
  }

  const parameters = {
    limit: MAX_LIMIT_CUBEJS,
    offset: 0,
    dimensions: ["RfmSegments.customerEmail", "RfmSegments.rfmSegment"],
    filters: filters,
    order: {
      "RfmSegments.lastOrderCreatedAt": "desc"
    }
  };

    try {
      const useMigrationDataset = await shop.shouldUseMigrationDataset(shop_id);
      const dataResultSet = await cubeAdminApiRequest(parameters, useMigrationDataset);
      const dataResult = dataResultSet.data || [];
      const segmentEmailObject = {};

      dataResult.forEach((row) => {
      const email = row["RfmSegments.customerEmail"];
      const segment = row["RfmSegments.rfmSegment"];

      if (!segmentEmailObject[segment]) {
        segmentEmailObject[segment] = [];
      }
    
      segmentEmailObject[segment].push(email);
    });
    return segmentEmailObject;
  } catch (error) {
    logger.error("Error fetching data:", {
      error: error,
      query: parameters
    });
    return {};
  }
}

async function facebookMetrics(integration_ids, start_date, end_date, useFBOSSDataset = false) {

  if (!integration_ids || integration_ids.length == 0) {
      return []
  }

  const query = {
    "dimensions": [
      "meta_ads_insights.integration_id",
      "meta_ads_insights.account_currency",
    ],
    "filters": [
        {
            "member": "meta_ads_insights.integration_id",
            "operator": "equals",
            "values": integration_ids
        }
    ],
    "measures": [
      "meta_ads_insights.spend",
    ],
    "timeDimensions": [
      {
        "dimension": "meta_ads_insights.partition_dt",
        "dateRange": [
          start_date,
          end_date
        ]
      }
    ]
  }

  try {
      const dataResultSet = await cubeAdminApiRequest(query, useFBOSSDataset);
      const dataResult = dataResultSet.data || [];
      return dataResult;
  } catch (error) {
    logger.error("Error in fetching facebook metrics", {
      error: error,
      query: query
    })
      return []
  }
}

async function shopMetrics(shop_ids, start_date, end_date, useMigrationDataset = false) {

  if (shop_ids.length == 0) {
      return []
  }

  const query = {
    "dimensions": [
      "shop_metrics.shop_id",
      "shop_metrics.shop_currency"
    ],
    "measures": [
      "shop_metrics.order_count",
      "shop_metrics.total_price",
      "shop_metrics.aov",
      "shop_metrics.new_cust_count",
      "shop_metrics.returning_cust_count"
    ],
    "timeDimensions": [{
      "dimension": "shop_metrics.date",
      "dateRange": [
        start_date,
        end_date
      ]
    }],
    "filters": [{
      "member": "shop_metrics.shop_currency",
      "operator": "set"
    }, {
      "member": "shop_metrics.shop_id",
      "operator": "contains",
      "values": shop_ids
    }]
  }

  try {
      const dataResultSet = await cubeAdminApiRequest(query, useMigrationDataset);
      const dataResult = dataResultSet.data || [];
      return dataResult;
  } catch (error) {
    logger.error("Error in fetching shop metrics", {
      error: error,
      query: query
    });
      return [];
  }
}

function parsedFacebookMetrics(rows) {
  let rowMap = {}
  for (var k in rows) {
      let r  = rows[k]
      rowMap[r["meta_ads_insights.integration_id"]] = {
          fb_spend : Math.round(parseFloat(r["meta_ads_insights.spend"] ?? 0)),
          fb_spend_formatted : util.moneyFormatter(r["meta_ads_insights.account_currency"])(Math.round(parseFloat(r["meta_ads_insights.spend"] ?? 0))),
      }
  }

  return rowMap
}

function parsedShopMetrics(rows) {
  let rowMap = {}

  for (var k in rows) {
      let r = rows[k]
      let formatter = util.moneyFormatter(r["shop_metrics.shop_currency"] ?? "")

      rowMap[r["shop_metrics.shop_id"]] = {
          shop_id : r["shop_metrics.shop_id"] ?? "",
          shop_currency : r["shop_metrics.shop_currency"] ?? "",
          total_price : Math.round(parseFloat(r["shop_metrics.total_price"] ?? 0)),
          total_price_formatted : formatter(Math.round(parseFloat(r["shop_metrics.total_price"] ?? 0))),
          order_count : parseInt(r["shop_metrics.order_count"] ?? 0),
          order_count_formatted : numeral(r["shop_metrics.order_count"] ?? "0").format("0,0"),
          new_cust_count : parseInt(r["shop_metrics.new_cust_count"] ?? 0),
          new_cust_count_formatted : numeral(r["shop_metrics.new_cust_count"] ?? "0").format("0,0"),
          returning_cust_count : parseInt(r["shop_metrics.returning_cust_count"] ?? 0),
          returning_cust_count_formatted : numeral(r["shop_metrics.returning_cust_count"] ?? "0").format("0,0"),
          aov : Math.round(parseFloat(r["shop_metrics.aov"] ?? 0)),
          aov_formatted : formatter(Math.round(parseFloat(r["shop_metrics.aov"] ?? 0)))
      }
  }
  return rowMap
}

async function prepareWeeklyReport(recipients) {
  // getting week as per IST time
  let curr_start_date = dayjs.utc().utcOffset(330).subtract(1, "week").startOf("isoWeek").toDate();
  let curr_end_date =  dayjs.utc().utcOffset(330).subtract(1, "week").endOf("isoWeek").toDate();
  let prev_start_date = dayjs.utc().utcOffset(330).subtract(2, "week").startOf("isoWeek").toDate();
  let prev_end_date =  dayjs.utc().utcOffset(330).subtract(2, "week").endOf("isoWeek").toDate();

  let shopIds = recipients.map(r => (r.shop_id + ""));

  // Split shopIds into two batches - 1. ShopIds with useMigrationDataset true 2. ShopIds with useMigrationDataset false
  let shopIdsOSS = [];
  let shopIdsNonOSS = [];
  for (let i = 0; i < shopIds.length; i++) {
      let shopId = shopIds[i];
      let useMigrationDataset = await shop.shouldUseMigrationDataset(shopId);
      if (useMigrationDataset) {
        shopIdsOSS.push(shopId);
      } else {
        shopIdsNonOSS.push(shopId);
      }
  }

  let integration_ids_cloud = [];
  let integration_ids_oss = [];
  for (let r of recipients) {
    // Only add non-empty integration IDs
    if (r.fb_integration_id) {
      if (r.useFBOSSDataset) {
        integration_ids_oss.push(r.fb_integration_id);
      } else {
        integration_ids_cloud.push(r.fb_integration_id);
      }
    }
  }

  // Make shopMetrics calls concurrent
  let [currDataNonOSS, prevDataNonOSS, currDataOSS, prevDataOSS, currDataFB, prevDataFB, currDataFBOss, prevDataFBOss] = await Promise.all([
      shopMetrics(shopIdsNonOSS, curr_start_date, curr_end_date, false),
      shopMetrics(shopIdsNonOSS, prev_start_date, prev_end_date, false),
      shopMetrics(shopIdsOSS, curr_start_date, curr_end_date, true),
      shopMetrics(shopIdsOSS, prev_start_date, prev_end_date, true),
      facebookMetrics(integration_ids_cloud, curr_start_date, curr_end_date, false),
      facebookMetrics(integration_ids_cloud, prev_start_date, prev_end_date, false),
      facebookMetrics(integration_ids_oss, curr_start_date, curr_end_date, true),
      facebookMetrics(integration_ids_oss, prev_start_date, prev_end_date, true)
  ]);

  currDataNonOSS = parsedShopMetrics(currDataNonOSS);
  prevDataNonOSS = parsedShopMetrics(prevDataNonOSS);
  currDataOSS = parsedShopMetrics(currDataOSS);
  prevDataOSS = parsedShopMetrics(prevDataOSS);
  currDataFB = parsedFacebookMetrics(currDataFB);
  prevDataFB = parsedFacebookMetrics(prevDataFB);
  currDataFBOss = parsedFacebookMetrics(currDataFBOss);
  prevDataFBOss = parsedFacebookMetrics(prevDataFBOss);

  let currData = {...currDataNonOSS, ...currDataOSS};
  let prevData = {...prevDataNonOSS, ...prevDataOSS};
  currDataFB = { ...currDataFB, ...currDataFBOss };
  prevDataFB = { ...prevDataFB, ...prevDataFBOss };

  let content = {}
  for (var k in recipients) {
      let shop_id = recipients[k].shop_id
      if (!currData[shop_id] || !prevData[shop_id]) {
          logger.error("Data missing for shop", recipients[k])
          continue;
      }

      let fbData = {
          fb_spend_formatted : "Not connected",
          fb_spend_diff : 0
      }

      let fb_integration_id = recipients[k].fb_integration_id
      if (!!fb_integration_id && currDataFB[fb_integration_id]) {
          fbData = {
              fb_spend_formatted : currDataFB[fb_integration_id].fb_spend_formatted,
              fb_spend_diff : prevDataFB[fb_integration_id] && prevDataFB[fb_integration_id].fb_spend > 0
                  ? Math.round((currDataFB[fb_integration_id].fb_spend - prevDataFB[fb_integration_id].fb_spend) * 100 / prevDataFB[fb_integration_id].fb_spend)
                  : 0
          }
      }

      content[shop_id] = {
          week_start: dayjs(curr_start_date).format("MMM DD, YYYY"),
          week_end: dayjs(curr_end_date).format("MMM DD, YYYY"),
          week_str_short : dayjs(curr_start_date).format("MMM DD") + " - " + dayjs(curr_end_date).format("MMM DD, YYYY"),
          prev_week_start: dayjs(prev_start_date).format("MMM DD, YYYY"),
          prev_week_end: dayjs(prev_end_date).format("MMM DD, YYYY"),
          ...fbData,
          ...recipients[k],
          ...currData[shop_id],
          // find diff in percentages
          total_price_diff : prevData[shop_id].total_price > 0 ? Math.round((currData[shop_id].total_price - prevData[shop_id].total_price) * 100 / prevData[shop_id].total_price) : 0,
          new_cust_count_diff : prevData[shop_id].new_cust_count > 0 ? Math.round((currData[shop_id].new_cust_count - prevData[shop_id].new_cust_count) * 100 / prevData[shop_id].new_cust_count) : 0,
          returning_cust_count_diff : prevData[shop_id].returning_cust_count > 0 ? Math.round((currData[shop_id].returning_cust_count - prevData[shop_id].returning_cust_count) * 100 / prevData[shop_id].returning_cust_count) : 0,
          order_count_diff : prevData[shop_id].order_count > 0 ? Math.round((currData[shop_id].order_count - prevData[shop_id].order_count) * 100 / prevData[shop_id].order_count) : 0,
          aov_diff : prevData[shop_id].aov > 0 ? Math.round((currData[shop_id].aov - prevData[shop_id].aov) * 100 / prevData[shop_id].aov) : 0
      }
  }

  return content;
}

async function getCubeData(shop_id, type, query_data) {

    function getInvalidRequestResponse() {
        return {
            error: "Invalid request",
            status: false
        }
    }

    if (!type || !["rfm_segment_wise_count", "rfm_segments_table", "benchmark_shops", "benchmark_industry"].includes(type)) {
        return getInvalidRequestResponse();
    }

    let data = {};
    const { period = "all_time" } = query_data || {};
    switch (type) {
        case "rfm_segment_wise_count":
            data = await getRfmSegmentWiseCount(shop_id, period);
            break;

        case "rfm_segments_table":
            const { selectedSegment } = query_data || {};
            if (!selectedSegment) return getInvalidRequestResponse();
            data = await getRfmSegmentsTable(shop_id, period, selectedSegment);
            break;

        case "benchmark_shops":
            data = await getBenchmarkShops(shop_id);
            break;

        case "benchmark_industry":
            const { industry, currency } = query_data || {};
            if (!industry || !currency) return getInvalidRequestResponse();
            data = await getBenchmarkIndustry(shop_id, industry, currency);
            break;

        default:
            data = {};
    }

    return {
        data: data,
        status: true
    }
}

async function clearCubeCacheOnShopifySyncCompletion(shop_id) {
    const shopObj = await shop.getShopById(shop_id);
    if (!shopObj) {
        logger.error("clearCubeCacheOnShopifySyncCompletion: Shop not found", { shop_id });
        return;
    }

    let c1 = await cache.del(getBenchmarkShopsCacheKey(shop_id));
    let c2 = await cache.del(getBenchmarkIndustryCacheKey(shopObj.onboard_industry, shopObj.currency));
    return c1 && c2;
}

function getRfmSegmentWiseCountCacheKey(shop_id, period) {
    return `rfm_segment_wise_count_${shop_id}_${period}`;
}

async function getRfmSegmentWiseCount(shop_id, period) {
    const cacheKey = getRfmSegmentWiseCountCacheKey(shop_id, period);
    const cachedData = await cache.get(cacheKey);
    if (cachedData) return cachedData;

    const query = {
        limit: 5000,
        dimensions: [
            "RfmSegmentsAgg.shopId",
            "RfmSegmentsAgg.rfmSegment"
        ],
        measures: [
            "RfmSegmentsAgg.customerCount",
            "RfmSegmentsAgg.percentage"
        ],
        filters: [
            {
                member: "RfmSegmentsAgg.period",
                operator: "equals",
                values: [period]
            },
            {
                member: "RfmSegmentsAgg.shopId",
                operator: "equals",
                values: [shop_id]
            }
        ],
        order: {
            "RfmSegmentsAgg.customerCount": "desc"
        }
    }

    const result = await executeCubeQueryAndPivot(shop_id, query);
    if (result && Array.isArray(result) && result.length > 0) {
        await cache.set(cacheKey, result, CUBE_CACHE_TTL);
    }
    return result;
}

function getRfmSegmentsTableCacheKey(shop_id, period, selectedSegment, limit, offset) {
    return `rfm_segments_table_${shop_id}_${period}_${selectedSegment}_${limit}_${offset}`;
}

async function getRfmSegmentsTable(shop_id, period, selectedSegment, limit = 100, offset = 0) {
    const cacheKey = getRfmSegmentsTableCacheKey(shop_id, period, selectedSegment, limit, offset);
    const cachedData = await cache.get(cacheKey);
    if (cachedData) return cachedData;

    let filters = [
        {
            "member": "RfmSegments.period",
            "operator": "equals",
            "values": [period]
        },
        {
            "member": "RfmSegments.shopId",
            "operator": "equals",
            "values": [shop_id]
        }
    ];

    if (!!selectedSegment) {
        filters.push({
            "member": "RfmSegments.rfmSegment",
            "operator": "equals",
            "values": [selectedSegment]
        })
    }

    const query = {
        limit: limit,
        offset: offset,
        dimensions: [
            "RfmSegments.customerId",
            "RfmSegments.rfmScore",
            "RfmSegments.rfmSegment",
            "RfmSegments.customerFirstName",
            "RfmSegments.customerEmail",
            "RfmSegments.customerPhone",
            "RfmSegments.customerLastName"
        ],
        measures: [
            "RfmSegments.lastOrderSince",
            "RfmSegments.totalOrders",
            "RfmSegments.totalSpend",
            "RfmSegments.lastOrderCreatedAt"
        ],
        filters: filters,
        order: {
            "RfmSegments.lastOrderCreatedAt": "desc"
        }
    }

    const result = await executeCubeQueryAndPivot(shop_id, query);
    if (result && Array.isArray(result) && result.length > 0) {
        await cache.set(cacheKey, result, CUBE_CACHE_TTL);
    }
    return result;
}

function getBenchmarkShopsCacheKey(shop_id) {
    return `benchmark_shops_${shop_id}`;
}

async function getBenchmarkShops(shop_id) {
    const cacheKey = getBenchmarkShopsCacheKey(shop_id);
    const cachedData = await cache.get(cacheKey);
    if (cachedData) return cachedData;

    const measures = [
        "BenchmarkShops.primaryCurrency",
        "BenchmarkShops.shopCurrency",
        "BenchmarkShops.totalCustomers",
        "BenchmarkShops.totalOrders",
        "BenchmarkShops.oneTimers",
        "BenchmarkShops.twoTimers",
        "BenchmarkShops.threeTimers",
        "BenchmarkShops.fourToFiveTimers",
        "BenchmarkShops.fivePlusTimers",
        "BenchmarkShops.ltv1mLocal",
        "BenchmarkShops.ltv3mLocal",
        "BenchmarkShops.ltv6mLocal",
        "BenchmarkShops.ltv1yLocal",
        "BenchmarkShops.return1m",
        "BenchmarkShops.return3m",
        "BenchmarkShops.return6m",
        "BenchmarkShops.return1y",
        "BenchmarkShops.arpuLocal",
        "BenchmarkShops.aovLocal",
        "BenchmarkShops.totalRevenueLocal",
        "BenchmarkShops.revenueGrowth12m",
        "BenchmarkShops.newCustomerGrowth12m",
        "BenchmarkShops.orderGrowth12m",
        "BenchmarkShops.growthMonth12m",
        "BenchmarkShops.revenueGrowthLatest",
        "BenchmarkShops.newCustomerGrowthLatest",
        "BenchmarkShops.orderGrowthLatest",
        "BenchmarkShops.growthMonthLatest",
        "BenchmarkShops.championPercentage",
        "BenchmarkShops.loyalPercentage",
        "BenchmarkShops.promisingPercentage",
        "BenchmarkShops.newCustomersPercentage",
        "BenchmarkShops.needAttentionPercentage",
        "BenchmarkShops.shouldNotLoosePercentage",
        "BenchmarkShops.sleepersPercentage",
        "BenchmarkShops.lostPercentage"
    ];

    const query = {
        limit: 5000,
        dimensions: [
            "BenchmarkShops.shopId"
        ],
        measures: measures,
        filters: [
            {
                "member": "BenchmarkShops.shopId",
                "operator": "equals",
                "values": [shop_id]
            }
        ]
    }

    const result = await executeCubeQueryAndPivot(shop_id, query);
    if (result && Array.isArray(result) && result.length > 0) {
        await cache.set(cacheKey, result, CUBE_CACHE_TTL);
    }
    return result;
}

function getBenchmarkIndustryCacheKey(industry, currency) {
    return `benchmark_industry_${industry}_${currency}`;
}

async function getBenchmarkIndustry(shop_id, industry, currency) {
    const cacheKey = getBenchmarkIndustryCacheKey(industry, currency);
    const cachedData = await cache.get(cacheKey);
    if (cachedData) return cachedData;

    const measures = [
        "BenchmarkIndustry.shopCount",
        "BenchmarkIndustry.totalOrders",
        "BenchmarkIndustry.totalCustomers",
        "BenchmarkIndustry.totalRevenue",
        "BenchmarkIndustry.championPercentage",
        "BenchmarkIndustry.loyalPercentage",
        "BenchmarkIndustry.promisingPercentage",
        "BenchmarkIndustry.newCustomersPercentage",
        "BenchmarkIndustry.needAttentionPercentage",
        "BenchmarkIndustry.shouldNotLoosePercentage",
        "BenchmarkIndustry.sleepersPercentage",
        "BenchmarkIndustry.lostPercentage",
        "BenchmarkIndustry.championPercentageP25",
        "BenchmarkIndustry.loyalPercentageP25",
        "BenchmarkIndustry.promisingPercentageP25",
        "BenchmarkIndustry.newCustomersPercentageP25",
        "BenchmarkIndustry.needAttentionPercentageP25",
        "BenchmarkIndustry.shouldNotLoosePercentageP25",
        "BenchmarkIndustry.sleepersPercentageP25",
        "BenchmarkIndustry.lostPercentageP25",
        "BenchmarkIndustry.championPercentageP75",
        "BenchmarkIndustry.loyalPercentageP75",
        "BenchmarkIndustry.promisingPercentageP75",
        "BenchmarkIndustry.newCustomersPercentageP75",
        "BenchmarkIndustry.needAttentionPercentageP75",
        "BenchmarkIndustry.shouldNotLoosePercentageP75",
        "BenchmarkIndustry.sleepersPercentageP75",
        "BenchmarkIndustry.lostPercentageP75",
        "BenchmarkIndustry.ltv1mP5",
        "BenchmarkIndustry.ltv1mP25",
        "BenchmarkIndustry.ltv1mP50",
        "BenchmarkIndustry.ltv1mP75",
        "BenchmarkIndustry.ltv1mP95",
        "BenchmarkIndustry.ltv3mP5",
        "BenchmarkIndustry.ltv3mP25",
        "BenchmarkIndustry.ltv3mP50",
        "BenchmarkIndustry.ltv3mP75",
        "BenchmarkIndustry.ltv3mP95",
        "BenchmarkIndustry.ltv6mP5",
        "BenchmarkIndustry.ltv6mP25",
        "BenchmarkIndustry.ltv6mP50",
        "BenchmarkIndustry.ltv6mP75",
        "BenchmarkIndustry.ltv6mP95",
        "BenchmarkIndustry.ltv1yP5",
        "BenchmarkIndustry.ltv1yP25",
        "BenchmarkIndustry.ltv1yP50",
        "BenchmarkIndustry.ltv1yP75",
        "BenchmarkIndustry.ltv1yP95",
        "BenchmarkIndustry.return1mP5",
        "BenchmarkIndustry.return1mP25",
        "BenchmarkIndustry.return1mP50",
        "BenchmarkIndustry.return1mP75",
        "BenchmarkIndustry.return1mP95",
        "BenchmarkIndustry.return3mP5",
        "BenchmarkIndustry.return3mP25",
        "BenchmarkIndustry.return3mP50",
        "BenchmarkIndustry.return3mP75",
        "BenchmarkIndustry.return3mP95",
        "BenchmarkIndustry.return6mP5",
        "BenchmarkIndustry.return6mP25",
        "BenchmarkIndustry.return6mP50",
        "BenchmarkIndustry.return6mP75",
        "BenchmarkIndustry.return6mP95",
        "BenchmarkIndustry.return1yP5",
        "BenchmarkIndustry.return1yP25",
        "BenchmarkIndustry.return1yP50",
        "BenchmarkIndustry.return1yP75",
        "BenchmarkIndustry.return1yP95",
        "BenchmarkIndustry.arpuP5",
        "BenchmarkIndustry.arpuP25",
        "BenchmarkIndustry.arpuP50",
        "BenchmarkIndustry.arpuP75",
        "BenchmarkIndustry.arpuP95",
        "BenchmarkIndustry.aovP5",
        "BenchmarkIndustry.aovP25",
        "BenchmarkIndustry.aovP50",
        "BenchmarkIndustry.aovP75",
        "BenchmarkIndustry.aovP95",
        "BenchmarkIndustry.oneTimers",
        "BenchmarkIndustry.twoTimers",
        "BenchmarkIndustry.threeTimers",
        "BenchmarkIndustry.fourToFiveTimers",
        "BenchmarkIndustry.fivePlusTimers",
        "BenchmarkIndustry.oneTimersP25",
        "BenchmarkIndustry.twoTimersP25",
        "BenchmarkIndustry.threeTimersP25",
        "BenchmarkIndustry.fourToFiveTimersP25",
        "BenchmarkIndustry.fivePlusTimersP25",
        "BenchmarkIndustry.oneTimersP75",
        "BenchmarkIndustry.twoTimersP75",
        "BenchmarkIndustry.threeTimersP75",
        "BenchmarkIndustry.fourToFiveTimersP75",
        "BenchmarkIndustry.fivePlusTimersP75",
        "BenchmarkIndustry.revenueGrowthLatest",
        "BenchmarkIndustry.newCustomerGrowthLatest",
        "BenchmarkIndustry.orderGrowthLatest",
        "BenchmarkIndustry.growthMonthLatest",
        "BenchmarkIndustry.revenueGrowthLatestP25",
        "BenchmarkIndustry.newCustomerGrowthLatestP25",
        "BenchmarkIndustry.orderGrowthLatestP25",
        "BenchmarkIndustry.revenueGrowthLatestP75",
        "BenchmarkIndustry.newCustomerGrowthLatestP75",
        "BenchmarkIndustry.orderGrowthLatestP75",
    ];

    const query = {
        "filters": [
            {
                "member": "BenchmarkIndustry.onboardIndustry",
                "operator": "equals",
                "values": [industry]
            },
            {
                "member": "BenchmarkIndustry.shopCurrency",
                "operator": "equals",
                "values": [currency]
            }
        ],
        "measures": measures
    }

    const result = await executeCubeQueryAndPivot(shop_id, query);
    if (result && Array.isArray(result) && result.length > 0) {
        await cache.set(cacheKey, result, CUBE_CACHE_TTL);
    }
    return result;
}

async function executeCubeQueryAndPivot(shop_id, query) {
    const shouldUseMigrationDataset = await shop.shouldUseMigrationDataset(shop_id);
    try {
        const resultSet = await cubeAdminApiRequest(query, shouldUseMigrationDataset);
        const result = resultSet.data || [];
        return result;
    } catch (error) {
      logger.error("executeCubeQuery - Error fetching data for shop", {
          error: error.message,
          errorStack: error.stack,
        query: query
      });
        return {};
    }
}

export { fetchRFMSegmentCustomers, prepareWeeklyReport, rfmSegmentExport, getCubeData, clearCubeCacheOnShopifySyncCompletion };
