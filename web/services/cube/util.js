import { DBT_PROD_DATASET_OSS } from '../dbt/index.js';
import { createRequire } from "module";

const require = createRequire(import.meta.url);
const jwt = require('jsonwebtoken');

export const getCubeAdminToken = (useMigrationDataset = false) => {
    let payload = { role: "admin" };
    if (useMigrationDataset) {
        payload.overrideDataset = DBT_PROD_DATASET_OSS;
    }
    return jwt.sign(payload, process.env.CUBEJS_API_SECRET, { expiresIn: '1d' });
}

export const getCubeToken = (shop_id, cube, useMigrationDataset = false) => {
    let payload = { shop_id, cube };
    if (useMigrationDataset) {
        payload.overrideDataset = DBT_PROD_DATASET_OSS;
    }
    return jwt.sign(payload, process.env.CUBEJS_API_SECRET, { expiresIn: '1d' });
}
