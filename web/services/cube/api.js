import { getCubeAdminToken, getCubeToken } from './util.js';
import { CUBE_PLATFORM_OSS_VM } from './constants.js';
import { getCachedIapToken } from '../gcp/iap.js';
import { logCurlFromApiInterceptorConfig } from '../util.js';
import axios from 'axios';
import logger from '../logger.js';
import { CUBE_VM_API_URL } from './constants.js';

const apiClient = axios.create({
    headers: {
        'Content-Type': 'application/json',
    },
});

apiClient.interceptors.request.use(async (config) => {
    config.baseURL = CUBE_VM_API_URL;

    const endpoint = config.url.replace(config.baseURL, '');
    const fullUrl = `${config.baseURL}${endpoint}`;

    try {
        const token = await getCachedIapToken(CUBE_PLATFORM_OSS_VM, fullUrl);

        // IAP token
        config.headers['Authorization'] = `Bearer ${token}`;

        // JWT token containing security context for cube
        if (config.isAdminRequest) {
            config.headers['x-cube-context'] = getCubeAdminToken(config.useMigrationDataset);
        } else if (config.shop_id && config.cube) {
            config.headers['x-cube-context'] = getCubeToken(config.shop_id, config.cube, config.useMigrationDataset);
        } else {
            throw new Error('Invalid cube vm request');
        }
    } catch (error) {
        logger.error('Cube VM API token error:', { error: error.message, errorStack: error.stack, config });
        throw error;
    }

    if (!!config.logCurl) {
        logCurlFromApiInterceptorConfig(config);
    }

    return config;
}, (error) => {
    logger.error('Cube VM API request interceptor error:', { error: error.message, errorStack: error.stack, config });
    return Promise.reject(error);
});

// Add response interceptor for debugging
apiClient.interceptors.response.use((response) => {
    return response;
}, (error) => {
    if (error.response && error.response.data) {
        logger.error('Cube VM API error response:', {
            status: error.response.status,
            headers: error.response.headers,
            data: error.response.data,
        });
    } else {
        logger.error('Cube VM API error:', { error: error.message, errorStack: error.stack });
    }
    return Promise.reject(error);
});

export async function cubeAdminApiRequest(query, useMigrationDataset = false, queryType = null) {
    if (!query) {
        throw new Error("Query is required");
    }

    let body = { query };
    if (queryType) body.queryType = queryType;

    try {
        const res = await apiClient.post(`/load`, body, {
            logCurl: false,
            isAdminRequest: true,
            useMigrationDataset
        });

        const result = res.data;
        return result;
    } catch (error) {
        logger.error('Cube VM API request failed:', {
            error: error.message,
            errorStack: error.stack,
            params: { query, useMigrationDataset, queryType }
        });
        throw error;
    }
}

export async function cubeApiRequest(shop_id, cube, query, useMigrationDataset = false, queryType = null) {
    if (!query) {
        throw new Error("Query is required");
    }

    let body = { query };
    if (queryType) body.queryType = queryType;

    try {
        const res = await apiClient.post(`/load`, body, {
            logCurl: false,
            isAdminRequest: false,
            shop_id,
            cube,
            useMigrationDataset
        });

        const result = res.data;
        return result;
    } catch (error) {
        logger.error('Cube VM API request failed:', {
            error: error.message,
            errorStack: error.stack,
            params: { shop_id, cube, query, useMigrationDataset, queryType }
        });
        throw error;
    }
}
