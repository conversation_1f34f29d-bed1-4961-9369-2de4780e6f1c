import { cubeAdminApiRequest } from '../../../cube/api.js';
import logger from '../../../logger.js'

const buildCubeFilters = (applied_filters) => {
	const filters = []
	
	const getOperator = (op, isNumeric = false) => {
		if (isNumeric) {
			return op === 'op_is_greater_than' ? 'gt' :
				   op === 'op_is_less_than' ? 'lt' :
				   op === 'op_is_equal_to' ? 'equals' :
				   op === 'op_is_not_equal_to' ? 'notEquals' : 'gt'
		}
		return op === 'op_is' ? 'equals' : 
			   op === 'op_is_not' ? 'notEquals' :
			   op === 'op_contains' ? 'contains' :
			   op === 'op_contains_not' ? 'notContains' : 'equals'
	}
	
	const addFilters = (filterArray, member, isNumeric = false) => {
		if (!filterArray?.length) return
		filterArray.forEach(filter => {
			if (filter.values?.length) {
				const values = isNumeric ? filter.values.map(val => isNumeric === 'int' ? parseInt(val) : parseFloat(val)) : filter.values
				filters.push({ member, operator: getOperator(filter.op, isNumeric), values })
			}
		})
	}
	
	const stringFilters = [
		['product_title', 'ProductPerformance.product_title'],
		['product_type', 'ProductPerformance.product_type'],
		['product_vendor', 'ProductPerformance.vendor'],
		['product_tags', 'ProductPerformance.product_tags']
	]
	
	const numericFilters = [
		['total_price', 'ProductPerformance.net_revenue', 'float'],
		['min_order_count', 'ProductPerformance.order_count', 'int'],
		['min_revenue', 'ProductPerformance.net_revenue', 'float'],
		['min_units_sold', 'ProductPerformance.units_sold', 'int']
	]
	
	stringFilters.forEach(([key, member]) => addFilters(applied_filters[key], member))
	numericFilters.forEach(([key, member, type]) => addFilters(applied_filters[key], member, type))
	
	return filters
}

const processInlineFilters = (inline_filters) => {
	const filters = []
	
	// Map of column IDs to cube.js member names
	const columnToCubeMember = {
		'order_count': 'ProductPerformance.order_count',
		'customer_count': 'ProductPerformance.customer_count',
		'units_sold': 'ProductPerformance.units_sold',
		'net_revenue': 'ProductPerformance.net_revenue',
		'avg_selling_price': 'ProductPerformance.avg_selling_price',
		'meta_ads_spend': 'ProductPerformance.meta_ads_spend',
		'meta_ads_cpc': 'ProductPerformance.meta_ads_cpc',
		'meta_ads_cpm': 'ProductPerformance.meta_ads_cpm',
		'meta_ads_ctr': 'ProductPerformance.meta_ads_ctr',
		'meta_ads_clicks': 'ProductPerformance.meta_ads_clicks',
		'meta_ads_impressions': 'ProductPerformance.meta_ads_impressions',
		'gads_spend': 'ProductPerformance.gads_spend',
		'gads_cpc': 'ProductPerformance.gads_cpc',
		'gads_cpm': 'ProductPerformance.gads_cpm',
		'gads_ctr': 'ProductPerformance.gads_ctr',
		'gads_clicks': 'ProductPerformance.gads_clicks',
		'gads_impressions': 'ProductPerformance.gads_impressions',
		'gads_conversions': 'ProductPerformance.gads_conversions',
		'gads_conversions_value': 'ProductPerformance.gads_conversions_value'
	}
	
	// Process each inline filter (skip blended_spend and blended_roas as they will be handled in post-processing)
	Object.keys(inline_filters).forEach(columnId => {
		const filter = inline_filters[columnId]
		
		// Skip blended_spend and blended_roas for now - we'll handle them in post-processing
		if (columnId === 'blended_spend' || columnId === 'blended_roas') {
			return
		}
		
		const cubeMember = columnToCubeMember[columnId]
		
		if (cubeMember && filter && filter.operator && ((filter.operator === 'range' && filter.minValue !== undefined && filter.maxValue !== undefined && filter.minValue !== '' && filter.maxValue !== '') || (filter.operator !== 'range' && filter.value !== undefined && filter.value !== ''))) {
			if (filter.operator === 'range') {
				// For range operator, we need to add two filters: gte minValue and lte maxValue
				filters.push({
					member: cubeMember,
					operator: 'gte',
					values: [filter.minValue]
				})
				filters.push({
					member: cubeMember,
					operator: 'lte',
					values: [filter.maxValue]
				})
			} else {
				filters.push({
					member: cubeMember,
					operator: filter.operator,
					values: [filter.value]
				})
			}
		}
	})
	
	return filters
}

// Helper function to check filter condition (optimized)
const checkFilterCondition = (value, filter) => {
	if (!filter || !filter.operator) return true;
	
	if (filter.operator === 'range') {
		if (filter.minValue === undefined || filter.maxValue === undefined || filter.minValue === '' || filter.maxValue === '') {
			return true;
		}
		const minValue = parseFloat(filter.minValue);
		const maxValue = parseFloat(filter.maxValue);
		return value >= minValue && value <= maxValue;
	}
	
	if (filter.value === undefined || filter.value === '') return true;
	
	const filterValue = parseFloat(filter.value);
	switch (filter.operator) {
		case 'gte': return value >= filterValue;
		case 'lte': return value <= filterValue;
		case 'equals': return Math.abs(value - filterValue) < 0.01;
		case 'gt': return value > filterValue;
		case 'lt': return value < filterValue;
		default: return true;
	}
};

// Helper function to check ROAS filter condition (optimized)
const checkRoasFilterCondition = (roas, isNonAdDriven, filter) => {
	if (!filter || !filter.operator) return true;
	
	if (filter.operator === 'range') {
		if (filter.minValue === undefined || filter.maxValue === undefined || filter.minValue === '' || filter.maxValue === '') {
			return true;
		}
		const minValue = parseFloat(filter.minValue);
		const maxValue = parseFloat(filter.maxValue);
		
		// Non-ad-driven revenue (infinite ROAS) passes range filter if maxValue is very high
		if (isNonAdDriven) {
			return maxValue >= 1000; // Arbitrary high threshold for infinite ROAS
		}
		
		return roas >= minValue && roas <= maxValue;
	}
	
	if (filter.value === undefined || filter.value === '') return true;
	
	const filterValue = parseFloat(filter.value);
	switch (filter.operator) {
		case 'gte':
		case 'gt':
			// Non-ad-driven revenue should pass any "greater than" filter since it's infinite ROAS
			return isNonAdDriven || (filter.operator === 'gte' ? roas >= filterValue : roas > filterValue);
		case 'lte':
		case 'lt':
			// Non-ad-driven revenue fails "less than" filters since it's infinite ROAS
			return !isNonAdDriven && (filter.operator === 'lte' ? roas <= filterValue : roas < filterValue);
		case 'equals':
			// Non-ad-driven revenue only matches if specifically filtering for a very high number
			return !isNonAdDriven && Math.abs(roas - filterValue) < 0.01;
		default:
			return true;
	}
};

// Helper function to apply blended spend filter in post-processing (DEPRECATED - kept for compatibility)
const applyBlendedSpendFilter = (data, filter) => {
	if (!filter || !filter.operator) {
		return data
	}
	
	// Handle range operator
	if (filter.operator === 'range') {
		if (filter.minValue === undefined || filter.maxValue === undefined || filter.minValue === '' || filter.maxValue === '') {
			return data
		}
		
		const minValue = parseFloat(filter.minValue)
		const maxValue = parseFloat(filter.maxValue)
		
		return data.filter(row => {
			const metaSpend = parseFloat(row.meta_ads_spend) || 0
			const gadsSpend = parseFloat(row.gads_spend) || 0
			const blendedSpend = metaSpend + gadsSpend
			
			return blendedSpend >= minValue && blendedSpend <= maxValue
		})
	}
	
	// Handle single value operators
	if (filter.value === undefined || filter.value === '') {
		return data
	}
	
	const filterValue = parseFloat(filter.value)
	
	return data.filter(row => {
		const metaSpend = parseFloat(row.meta_ads_spend) || 0
		const gadsSpend = parseFloat(row.gads_spend) || 0
		const blendedSpend = metaSpend + gadsSpend
		
		switch (filter.operator) {
			case 'gte':
				return blendedSpend >= filterValue
			case 'lte':
				return blendedSpend <= filterValue
			case 'equals':
				return Math.abs(blendedSpend - filterValue) < 0.01 // Allow small floating point differences
			case 'gt':
				return blendedSpend > filterValue
			case 'lt':
				return blendedSpend < filterValue
			default:
				return true
		}
	})
}

// Helper function to apply blended ROAS filter in post-processing
const applyBlendedRoasFilter = (data, filter) => {
	if (!filter || !filter.operator) {
		return data
	}
	
	// Handle range operator
	if (filter.operator === 'range') {
		if (filter.minValue === undefined || filter.maxValue === undefined || filter.minValue === '' || filter.maxValue === '') {
			return data
		}
		
		const minValue = parseFloat(filter.minValue)
		const maxValue = parseFloat(filter.maxValue)
		
		return data.filter(row => {
			const metaSpend = parseFloat(row.meta_ads_spend) || 0
			const gadsSpend = parseFloat(row.gads_spend) || 0
			const blendedSpend = metaSpend + gadsSpend
			const netRevenue = parseFloat(row.net_revenue) || 0
			
			// Handle special case: non-ad-driven revenue (infinite ROAS)
			const isNonAdDriven = blendedSpend === 0 && netRevenue > 0
			
			// Calculate blended ROAS for regular cases
			let blendedRoas = 0
			if (blendedSpend > 0) {
				blendedRoas = netRevenue / blendedSpend
			}
			
			// Non-ad-driven revenue (infinite ROAS) passes range filter if maxValue is very high
			if (isNonAdDriven) {
				return maxValue >= 1000 // Arbitrary high threshold for infinite ROAS
			}
			
			return blendedRoas >= minValue && blendedRoas <= maxValue
		})
	}
	
	// Handle single value operators
	if (filter.value === undefined || filter.value === '') {
		return data
	}
	
	const filterValue = parseFloat(filter.value)
	
	return data.filter(row => {
		const metaSpend = parseFloat(row.meta_ads_spend) || 0
		const gadsSpend = parseFloat(row.gads_spend) || 0
		const blendedSpend = metaSpend + gadsSpend
		const netRevenue = parseFloat(row.net_revenue) || 0
		
		// Handle special case: non-ad-driven revenue (infinite ROAS)
		const isNonAdDriven = blendedSpend === 0 && netRevenue > 0
		
		// Calculate blended ROAS for regular cases
		let blendedRoas = 0
		if (blendedSpend > 0) {
			blendedRoas = netRevenue / blendedSpend
		}
		
		switch (filter.operator) {
			case 'gte':
				// Non-ad-driven revenue should pass any "greater than" filter since it's infinite ROAS
				return isNonAdDriven || blendedRoas >= filterValue
			case 'lte':
				// Non-ad-driven revenue fails "less than" filters since it's infinite ROAS
				return !isNonAdDriven && blendedRoas <= filterValue
			case 'equals':
				// Non-ad-driven revenue only matches if specifically filtering for a very high number
				return !isNonAdDriven && Math.abs(blendedRoas - filterValue) < 0.01
			case 'gt':
				// Non-ad-driven revenue should pass any "greater than" filter since it's infinite ROAS
				return isNonAdDriven || blendedRoas > filterValue
			case 'lt':
				// Non-ad-driven revenue fails "less than" filters since it's infinite ROAS
				return !isNonAdDriven && blendedRoas < filterValue
			default:
				return true
		}
	})
}

// parse result to remove prefix from keys and format for frontend
const parseResult = result =>
	result.map(row =>
		Object.keys(row).reduce((acc, key) => {
			let newKey = key.replace('ProductPerformance.', '')
			acc[newKey] = row[key] ?? 0
			return acc
		}, {})
	)


export const queryProductPerformance = async (
	shop_id,
	start_date,
	end_date,
	breakdown = 'product_name',
	applied_filters = {},
	inline_filters = {}
) => {
	const baseFilters = [
		{
			member: 'ProductPerformance.shop_id',
			operator: 'equals',
			values: [shop_id.toString()]
		}
	]

	// Add applied filters
	const additionalFilters = buildCubeFilters(applied_filters)
	
	// Add inline filters
	const inlineFilters = processInlineFilters(inline_filters)
	
	// Add default filter for rows with meaningful data (net_revenue ≠ 0 OR gads_spend ≠ 0 OR meta_ads_spend ≠ 0)
	const defaultFilters = [
		{
			or: [
				{
					member: 'ProductPerformance.net_revenue',
					operator: 'gt',
					values: ['0']
				},
				{
					member: 'ProductPerformance.gads_spend',
					operator: 'gt',
					values: ['0']
				},
				{
					member: 'ProductPerformance.meta_ads_spend',
					operator: 'gt',
					values: ['0']
				}
			]
		}
	]
	
	const allFilters = [...baseFilters, ...additionalFilters, ...inlineFilters, ...defaultFilters]


	let dimensions = [
		'ProductPerformance.product_id',
		'ProductPerformance.product_title',
		'ProductPerformance.featured_image_url'
	]

	if (breakdown == 'product_type') {
		dimensions = [
			'ProductPerformance.product_type'
		]
	} else if (breakdown == 'vendor') {
		dimensions = [
			'ProductPerformance.vendor'
		]
	}

	const query = {
		limit: 5000,
		dimensions: dimensions,
		measures: [
			'ProductPerformance.net_revenue',
			'ProductPerformance.units_sold',
			'ProductPerformance.order_count',
			'ProductPerformance.customer_count',
			'ProductPerformance.avg_selling_price',
			'ProductPerformance.meta_ads_spend',
			'ProductPerformance.meta_ads_clicks',
			'ProductPerformance.meta_ads_impressions',
			'ProductPerformance.meta_ads_cpc',
			'ProductPerformance.meta_ads_cpm',
            'ProductPerformance.meta_ads_ctr',
			'ProductPerformance.gads_spend',
			'ProductPerformance.gads_clicks',
			'ProductPerformance.gads_impressions',
			'ProductPerformance.gads_cpc',
			'ProductPerformance.gads_cpm',
			'ProductPerformance.gads_ctr',
			'ProductPerformance.gads_conversions',
			'ProductPerformance.gads_conversions_value',
		],
		timeDimensions: [
			{
				dimension: 'ProductPerformance.date',
				dateRange: [start_date, end_date]
			}
		],
		filters: allFilters,
		order: {
			'ProductPerformance.net_revenue': 'desc'
		}
	}

	try {
		const dataResultSet = await cubeAdminApiRequest(query)
		const dataResult = dataResultSet.data || []
		let parsedResult = parseResult(dataResult)

		// Apply blended filters in post-processing if they exist (optimized)
		const hasBlendedSpendFilter = inline_filters?.blended_spend;
		const hasBlendedRoasFilter = inline_filters?.blended_roas;
		
		if (hasBlendedSpendFilter || hasBlendedRoasFilter) {
			// Apply both filters in a single pass to avoid multiple iterations
			parsedResult = parsedResult.filter(row => {
				let passesSpendFilter = true;
				let passesRoasFilter = true;
				
				if (hasBlendedSpendFilter) {
					const metaSpend = parseFloat(row.meta_ads_spend) || 0;
					const gadsSpend = parseFloat(row.gads_spend) || 0;
					const blendedSpend = metaSpend + gadsSpend;
					passesSpendFilter = checkFilterCondition(blendedSpend, inline_filters.blended_spend);
				}
				
				if (hasBlendedRoasFilter) {
					const metaSpend = parseFloat(row.meta_ads_spend) || 0;
					const gadsSpend = parseFloat(row.gads_spend) || 0;
					const blendedSpend = metaSpend + gadsSpend;
					const netRevenue = parseFloat(row.net_revenue) || 0;
					
					const isNonAdDriven = blendedSpend === 0 && netRevenue > 0;
					const blendedRoas = blendedSpend > 0 ? netRevenue / blendedSpend : 0;
					
					passesRoasFilter = checkRoasFilterCondition(blendedRoas, isNonAdDriven, inline_filters.blended_roas);
				}
				
				return passesSpendFilter && passesRoasFilter;
			});
		}

		return {
			data: parsedResult
		}
	} catch (error) {
        logger.error('Error in fetching product performance data', {
            error: error,
            query: query
        })
		throw error
	}
}
