import * as data from './data.js'
import logger from '../../../logger.js'
import util from '../../../common/util.js'
import { getIntegrationsFromDB } from '../../../airbyte/integrations.js';
import { SOURCE_FB, SOURCE_GOOGLE_ADS } from '../../../airbyte/constants.js';

const mean = arr => arr.length ? arr.reduce((sum, v) => sum + v, 0) / arr.length : 0;

const productPerformance = async ({ shop, start_date, end_date, breakdown = 'product_name', applied_filters = {}, inline_filters = {} }) => {
	const { is_valid, errors } = util.validate(shop.shop_id, start_date, end_date)
	if (!is_valid) {
		return { errors }
	}

	let response = {
		data: {
			items: [],
			count: 0,
			show_blended_spend: false,
			has_meta_connection: false,
			has_google_ads_connection: false
		}
	}

	try {
		const integrations = await getIntegrationsFromDB(shop.shop_id)
		const googleAdsConnected = integrations.some(integration => integration.source_type === SOURCE_GOOGLE_ADS)
		const facebookMarketingConnected = integrations.some(integration => integration.source_type === SOURCE_FB)
		response.data.show_blended_spend = googleAdsConnected && facebookMarketingConnected
		response.data.has_meta_connection = facebookMarketingConnected
		response.data.has_google_ads_connection = googleAdsConnected

		// Prepare money formatter for later formatting (used in filtered products + insights)
		const money = util.moneyFormatter(shop.currency);

		// Single query to get both all products (for averages) and filtered products
		const [allProductsResult, filteredProductsResult] = await Promise.all([
			// 1. Fetch all products for averages (no filters)
			data.queryProductPerformance(
				shop.shop_id,
				start_date,
				end_date,
				breakdown,
				{}, // no applied_filters
				{}  // no inline_filters
			),
			// 2. Fetch filtered products for display
			data.queryProductPerformance(
				shop.shop_id,
				start_date,
				end_date,
				breakdown,
				applied_filters,
				inline_filters
			)
		]);

		const allProducts = allProductsResult.data;
		const filteredProducts = filteredProductsResult.data;

		// 3. Calculate avg_roas and avg_ad_spend on allProducts (optimized)
		let totalRevenue = 0;
		let totalAdSpend = 0;
		let adSpendValues = [];
		
		// Pre-calculate spend calculation function to avoid repeated conditionals
		const calculateProductSpend = (facebookMarketingConnected && googleAdsConnected)
			? (metaSpend, gadsSpend) => metaSpend + gadsSpend
			: facebookMarketingConnected 
				? (metaSpend, gadsSpend) => metaSpend
				: (metaSpend, gadsSpend) => gadsSpend;

		for (let k in allProducts) {
			const meta_ads_spend_raw = Number(allProducts[k].meta_ads_spend) || 0;
			const gads_spend_raw = Number(allProducts[k].gads_spend) || 0;
			const net_revenue_raw = Number(allProducts[k].net_revenue) || 0;
			
			const productSpend = calculateProductSpend(meta_ads_spend_raw, gads_spend_raw);
			if (productSpend > 0) {
				totalAdSpend += productSpend;
				totalRevenue += net_revenue_raw;
				adSpendValues.push(productSpend);
			}
		}
		
		const avgRoas = totalAdSpend > 0 ? totalRevenue / totalAdSpend : 0;
		const avgAdSpend = mean(adSpendValues);

		// Placeholders for insight buckets (will be derived from filtered products after applying filters)
		let top_hero_products = [];
		let top_potential_products = [];
		let top_budget_wasting_products = [];

		// 4. Format filtered products for frontend (optimized processing)
		for (let k in filteredProducts) {
			const product = filteredProducts[k];
			
			// Set product name based on breakdown type
			if (breakdown == 'product_name') {
				product.product_id = (product.product_id ?? '').toString()
				product.name = (product.product_title ?? '').toString()
				product.featured_image_url = (product.featured_image_url ?? '').toString()
			} else if (breakdown == 'product_type') {
				product.product_id = (product.product_type ?? '').toString()
				product.name = (product.product_type ?? '').toString()
			} else if (breakdown == 'vendor') {
				product.product_id = (product.vendor ?? '').toString()
				product.name = (product.vendor ?? '').toString()
			}

		// Parse all numeric fields as numbers for raw values (batch conversion)
		// Use parseFloat and ensure finite numbers to avoid Dinero.js errors
		const parseNumeric = (value) => {
			const num = parseFloat(value);
			return Number.isFinite(num) ? num : 0;
		};
		
		const meta_ads_spend_raw = parseNumeric(product.meta_ads_spend);
		const gads_spend_raw = parseNumeric(product.gads_spend);
		const net_revenue_raw = parseNumeric(product.net_revenue);
		const avg_selling_price_raw = parseNumeric(product.avg_selling_price);
		const meta_ads_cpc_raw = parseNumeric(product.meta_ads_cpc);
		const meta_ads_cpm_raw = parseNumeric(product.meta_ads_cpm);
		const gads_cpc_raw = parseNumeric(product.gads_cpc);
		const gads_cpm_raw = parseNumeric(product.gads_cpm);
		const gads_conversions_value_raw = parseNumeric(product.gads_conversions_value);
		const meta_ads_ctr_raw = parseNumeric(product.meta_ads_ctr);
		const gads_ctr_raw = parseNumeric(product.gads_ctr);

		// Catalog Blended Spend (only meaningful if BOTH integrations connected)
		const blended_catalog_spend_raw = (facebookMarketingConnected && googleAdsConnected) ? (meta_ads_spend_raw + gads_spend_raw) : 0;
		product.blended_catalog_spend_raw = (facebookMarketingConnected && googleAdsConnected) ? blended_catalog_spend_raw : null;
		product.blended_catalog_spend = (facebookMarketingConnected && googleAdsConnected && blended_catalog_spend_raw > 0) ? money(blended_catalog_spend_raw) : "-";

		// Catalog Blended ROAS (or single-platform fallback ROAS) - EXACT ORIGINAL LOGIC
		let blended_catalog_roas_raw = null;
		let blended_catalog_roas_display = "-";
		if (facebookMarketingConnected && googleAdsConnected) {
			// True blended case
			if (blended_catalog_spend_raw > 0) {
				blended_catalog_roas_raw = net_revenue_raw / blended_catalog_spend_raw;
				blended_catalog_roas_display = blended_catalog_roas_raw.toFixed(2);
			} else if (net_revenue_raw > 0) {
				blended_catalog_roas_display = "non-ad-driven";
				blended_catalog_roas_raw = null;
			} else if (net_revenue_raw === 0) {
				blended_catalog_roas_display = "0.00";
				blended_catalog_roas_raw = 0;
			}
		} else if (facebookMarketingConnected || googleAdsConnected) {
			// Single-platform fallback: compute ROAS from whichever spend source exists so UI isn't stuck at "-".
			const single_spend_raw = facebookMarketingConnected ? meta_ads_spend_raw : gads_spend_raw;
			if (single_spend_raw > 0) {
				blended_catalog_roas_raw = net_revenue_raw / single_spend_raw;
				blended_catalog_roas_display = blended_catalog_roas_raw.toFixed(2);
			} else if (net_revenue_raw > 0) {
				blended_catalog_roas_display = "non-ad-driven";
				blended_catalog_roas_raw = null;
			} else {
				blended_catalog_roas_display = "0.00";
				blended_catalog_roas_raw = 0;
			}
		}
		product.blended_catalog_roas_raw = blended_catalog_roas_raw;
		product.blended_catalog_roas = blended_catalog_roas_display;

		// Batch assign all fields to avoid repeated object access
		Object.assign(product, {
			// Currency fields: add both raw and formatted
			currency: shop.currency,
			net_revenue_raw,
			net_revenue: money(net_revenue_raw),
			avg_selling_price_raw,
			avg_selling_price: money(avg_selling_price_raw),
			
			// Meta ads fields
			meta_ads_spend_raw,
			meta_ads_spend: facebookMarketingConnected ? money(meta_ads_spend_raw) : "-",
			meta_ads_cpc_raw,
			meta_ads_cpc: facebookMarketingConnected ? money(meta_ads_cpc_raw) : "-",
			meta_ads_cpm_raw,
			meta_ads_cpm: facebookMarketingConnected ? money(meta_ads_cpm_raw) : "-",
			meta_ads_ctr_raw,
			meta_ads_ctr: facebookMarketingConnected ? `${meta_ads_ctr_raw.toFixed(2)}%` : "-",
			meta_ads_clicks: facebookMarketingConnected ? product.meta_ads_clicks.toString() : "-",
			meta_ads_impressions: facebookMarketingConnected ? product.meta_ads_impressions.toString() : "-",
			
			// Google ads fields
			gads_spend_raw,
			gads_spend: googleAdsConnected ? money(gads_spend_raw) : "-",
			gads_cpc_raw,
			gads_cpc: googleAdsConnected ? money(gads_cpc_raw) : "-",
			gads_cpm_raw,
			gads_cpm: googleAdsConnected ? money(gads_cpm_raw) : "-",
			gads_ctr_raw,
			gads_ctr: googleAdsConnected ? `${gads_ctr_raw.toFixed(2)}%` : "-",
			gads_conversions_value_raw,
			gads_conversions_value: googleAdsConnected ? money(gads_conversions_value_raw) : "-",
			gads_clicks: googleAdsConnected ? product.gads_clicks.toString() : "-",
			gads_impressions: googleAdsConnected ? product.gads_impressions.toString() : "-",
			gads_conversions: googleAdsConnected ? product.gads_conversions.toString() : "-",
			
			// Other fields (convert to string for consistency)
			units_sold: product.units_sold.toString(),
			order_count: product.order_count.toString(),
			customer_count: product.customer_count.toString()
		});
		}

		// 4b. Derive insights from FILTERED product list (optimized single-pass processing)
		if (facebookMarketingConnected && googleAdsConnected && filteredProducts.length && 
			avgRoas > 0 && avgAdSpend >= 0 && !isNaN(avgRoas) && !isNaN(avgAdSpend)) {
			const threshold = 0.5 * avgRoas;
			
			// Single pass to categorize products and avoid multiple iterations
			const heroProducts = [];
			const potentialProducts = [];
			const budgetWastingProducts = [];
			const topSpendProducts = [];

			for (const p of filteredProducts) {
				// Use exact original logic for insights calculation
				const netRevenue = Number(p.net_revenue_raw) || 0;
				const metaSpend = Number(p.meta_ads_spend_raw) || 0;
				const gadsSpend = Number(p.gads_spend_raw) || 0;
				const blendedSpend = (metaSpend + gadsSpend);
				const roas = blendedSpend > 0 ? (netRevenue / blendedSpend) : 0;
				
				// Skip products with no ad spend or ROAS (matching original logic)
				if (blendedSpend <= 0 || roas <= 0) continue;
				
				const insightProduct = {
					product_id: p.product_id || '',
					product_name: p.name || p.product_title || '',
					product_image: p.featured_image_url || '',
					net_revenue: p.net_revenue, // already formatted
					netRevenue: netRevenue,
					adSpend: blendedSpend,
					ad_spend: blendedSpend,
					roas,
					roas_display: roas > 0 ? roas.toFixed(2) : '0.00',
					adSpendDisplay: money(blendedSpend)
				};

				// Add to top spend products (for hero products selection)
				topSpendProducts.push(insightProduct);
				
				// Categorize for insights
				if (blendedSpend < avgAdSpend) {
					potentialProducts.push(insightProduct);
				}
				
				if (roas < threshold) {
					budgetWastingProducts.push(insightProduct);
				}
			}

			// Sort once and slice for each category
			topSpendProducts.sort((a, b) => b.adSpend - a.adSpend);
			const top20BySpend = topSpendProducts.slice(0, 20);
			
			// Get top hero products (highest ROAS among top 20 by spend)
			top_hero_products = [...top20BySpend]
				.sort((a, b) => b.roas - a.roas)
				.slice(0, 5);

			// Sort and slice other categories
			top_potential_products = potentialProducts
				.sort((a, b) => b.adSpend - a.adSpend || b.roas - a.roas)
				.slice(0, 5);

			top_budget_wasting_products = budgetWastingProducts
				.sort((a, b) => b.adSpend - a.adSpend)
				.slice(0, 5);
		}

		// 5. Set response fields (add insights)
		response.data.items = filteredProducts;
		response.data.count = filteredProducts.length;
		response.data.currency = shop.currency; // Add top-level currency
		response.data.avg_roas = avgRoas;
		response.data.avg_ad_spend = avgAdSpend;
		response.data.insights = {
			top_hero_products,
			top_potential_products,
			top_budget_wasting_products
		};

		return response
	} catch (error) {
		logger.error('product-performance.summary error', error)
		return response
	}
}


const productPerformanceExport = async ({ shop, start_date, end_date, breakdown = 'product_name', applied_filters = {}, inline_filters = {}, column_visibility = {} }) => {
	   try {
		   // Validate required parameters
		   if (!shop || !start_date || !end_date) {
			   logger.error('Product Performance Export - Missing parameters', {
				   shop_id: shop?.shop_id,
				   start_date,
				   end_date,
				   breakdown,
				   applied_filters,
				   inline_filters,
				   column_visibility
			   });
			   return {
				   data: [],
				   fields: [],
				   error: 'Missing required parameters: shop, start_date, or end_date'
			   };
		   }

		   // Use raw data from queryProductPerformance in data.js
		   const { data: rawData } = await data.queryProductPerformance(
			   shop.shop_id,
			   start_date,
			   end_date,
			   breakdown,
			   applied_filters,
			   inline_filters
		   );

		   if (!rawData || !Array.isArray(rawData)) {
			   return {
				   data: [],
				   fields: [],
				   error: 'No data available for the selected criteria'
			   };
		   }

		   // Transform raw data for export using slug field names (machine-friendly)
		   const exportData = rawData.map(item => {
				   const metaSpend = Number(item.meta_ads_spend) || 0;
				   const gadsSpend = Number(item.gads_spend) || 0;
				   const blended_spend_calc = metaSpend + gadsSpend;
				   const roas_calc = blended_spend_calc > 0 ? (Number(item.net_revenue) || 0) / blended_spend_calc : 0;
				   return {
				   product_id: item.product_id || '',
				   product_title: item.product_title || '',
				   product_type: item.product_type || '',
				   vendor: item.vendor || '',
				   net_revenue: Number(item.net_revenue) || 0,
				   units_sold: Number(item.units_sold) || 0,
				   order_count: Number(item.order_count) || 0,
				   customer_count: Number(item.customer_count) || 0,
				   avg_selling_price: Number(item.avg_selling_price) || 0,
				   meta_ads_spend: Number(item.meta_ads_spend) || 0,
				   meta_ads_clicks: Number(item.meta_ads_clicks) || 0,
				   meta_ads_impressions: Number(item.meta_ads_impressions) || 0,
				   meta_ads_cpc: Number(item.meta_ads_cpc) || 0,
				   meta_ads_cpm: Number(item.meta_ads_cpm) || 0,
				   meta_ads_ctr: Number(item.meta_ads_ctr) || 0,
				   gads_spend: Number(item.gads_spend) || 0,
				   gads_clicks: Number(item.gads_clicks) || 0,
				   gads_impressions: Number(item.gads_impressions) || 0,
				   gads_cpc: Number(item.gads_cpc) || 0,
				   gads_cpm: Number(item.gads_cpm) || 0,
				   gads_ctr: Number(item.gads_ctr) || 0,
				   gads_conversions: Number(item.gads_conversions) || 0,
				   gads_conversions_value: Number(item.gads_conversions_value) || 0,
				   	blended_catalog_spend: blended_spend_calc, // only used when both platforms truly connected
				   	blended_catalog_roas: roas_calc,
				   currency: shop.currency || ''
			   };
		   });

		// Define mapping between column accessors and export field (slug) names
		const columnToFieldMap = {
			product_cell: [], // Will be set based on breakdown below
			product_name_text: [], // search-only
			net_revenue: ['net_revenue'],
			units_sold: ['units_sold'],
			order_count: ['order_count'],
			customer_count: ['customer_count'],
			avg_selling_price: ['avg_selling_price'],
			meta_ads_spend: ['meta_ads_spend'],
			meta_ads_clicks: ['meta_ads_clicks'],
			meta_ads_impressions: ['meta_ads_impressions'],
			meta_ads_cpc: ['meta_ads_cpc'],
			meta_ads_cpm: ['meta_ads_cpm'],
			meta_ads_ctr: ['meta_ads_ctr'],
			gads_spend: ['gads_spend'],
			gads_clicks: ['gads_clicks'],
			gads_impressions: ['gads_impressions'],
			gads_cpc: ['gads_cpc'],
			gads_cpm: ['gads_cpm'],
			gads_ctr: ['gads_ctr'],
			gads_conversions: ['gads_conversions'],
			gads_conversions_value: ['gads_conversions_value'],
			blended_catalog_spend: ['blended_catalog_spend'],
			blended_catalog_roas: ['blended_catalog_roas']
		};

		// Set product_cell mapping based on breakdown type
		if (breakdown === 'product_name') {
			columnToFieldMap.product_cell = ['product_id', 'product_title'];
		} else if (breakdown === 'product_type') {
			columnToFieldMap.product_cell = ['product_type'];
		} else if (breakdown === 'vendor') {
			columnToFieldMap.product_cell = ['vendor'];
		}

		// Filter fields based on column visibility
		let visibleFields = [];
		
		// If no column visibility is provided, export all fields (backward compatibility)
		if (Object.keys(column_visibility).length === 0) {
			visibleFields = Array.from(new Set(exportData.flatMap(obj => Object.keys(obj))));
		} else {
			// Only include fields for visible columns
			Object.keys(column_visibility).forEach(columnAccessor => {
				// Handle both boolean and string values for column visibility
				const isVisible = column_visibility[columnAccessor] === true || column_visibility[columnAccessor] === 'true';
				
				if (isVisible && columnToFieldMap[columnAccessor]) {
					visibleFields = visibleFields.concat(columnToFieldMap[columnAccessor]);
				}
			});
			
			// Safeguard: If no columns are visible, include at least basic product info
			if (visibleFields.length === 0) {
				if (breakdown === 'product_name') {
					visibleFields = ['product_id', 'product_title', 'net_revenue'];
				} else if (breakdown === 'product_type') {
					visibleFields = ['product_type', 'net_revenue'];
				} else if (breakdown === 'vendor') {
					visibleFields = ['vendor', 'net_revenue'];
				}
			}
		}

		// Filter export data to only include visible fields
		const filteredExportData = exportData.map(item => {
			const filteredItem = {};
			visibleFields.forEach(field => {
				if (item.hasOwnProperty(field)) {
					filteredItem[field] = item[field];
				}
			});
			return filteredItem;
		});

		// Remove duplicates from visible fields
		const uniqueFields = [...new Set(visibleFields)];

		return {
			data: filteredExportData,
			fields: uniqueFields,
			error: ''
		};

	} catch (error) {
		logger.error('product-performance.export error', error);
		return {
			data: [],
			fields: [],
			error: error.message || 'Export failed'
		};
	}
}

export { productPerformance, productPerformanceExport }
