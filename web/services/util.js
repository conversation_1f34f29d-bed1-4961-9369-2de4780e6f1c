import logger from './logger.js';

export function logCurlFromApiInterceptorConfig(config) {
    // Build the cURL command
    let curl = `curl -X ${config.method.toUpperCase()}`;
    curl += ` '${config.baseURL}${config.url}'`;
    Object.entries(config.headers).forEach(([key, value]) => {
        curl += ` -H '${key}: ${value}'`;
    });
    if (config.data) {
        curl += ` -d '${JSON.stringify(config.data)}'`;
    }

    if (config.params) {
        curl += ` --data-urlencode '${new URLSearchParams(config.params).toString()}'`;
    }

    // Log the cURL command
    logger.info(`cURL: ${curl}`);
}