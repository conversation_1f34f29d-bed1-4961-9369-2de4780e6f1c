import { createRequire } from "module";
const require = createRequire(import.meta.url);
import {getToken, saveToken, generateToken} from "./token.js";
import {getAdAccounts, getMonthlyInsights} from "./adaccounts.js";
const {CloudTasksClient} = require('@google-cloud/tasks');
import logger from '../logger.js';
import shopService from '../shop.js';
import { cache } from '../redis.js';
import unleash from "../unleash.js";

var wantedScopes = [
    'ads_management',
    'ads_read',
]


async function initiateDataSync(shopName, userId, accessToken) {

    if (process.env.NODE_ENV !== "production") {
        return false
    }

    const project = process.env.PROJECT_ID;
    const queue = process.env.FB_SYNC_QUEUE;
    const location = process.env.DATA_SYNC_QUEUE_LOCATION;

    const payload = {
        shop: shopName,
        userId: userId,
        accessToken : accessToken
    };

    const client = new CloudTasksClient();
    async function createTask() {
        const parent = client.queuePath(project, location, queue);

        const task = {
            appEngineHttpRequest: {
                httpMethod: 'POST',
                relativeUri: '/fb_sync',
                body : Buffer.from(JSON.stringify(payload)).toString('base64')
            },
        };

        logger.info('Sending task:', task);
        const request = {parent, task};
        const [response] = await client.createTask(request);
        const name = response.name;
        logger.info(`Created task ${name}`);
    }

    await createTask()
}

async function getInitConfig(shop) {

    let config = {showConnector : true}
    if (!shop || !shop.shop_id) {
        logger.error("facebook.getInitConfig: Shop is invalid")
        return config
    }

    let cacheVal = await cache.get('facebook_config_' + shop.shop_id)
    if (!!cacheVal) {
        return cacheVal;
    }

    let tokenData = await getToken(shop.shop_id)
    if (!tokenData || Object.keys(tokenData).length == 0 || !tokenData.access_token) {
        logger.info("facebook.getInitConfig: tokenData is missing")
        return config // not connected to facebook
    }

    let accounts = await getAdAccounts(tokenData.access_token)
    if (!accounts || accounts.length == 0) {
        logger.error("facebook.getInitConfig: did not receive accounts")
        // connected to facebook but needs to reconnect
        return config
    }

    config.showConnector = false

    let shopState = await shopService.getShopState(shop.shop_id)

    config.accounts = accounts
    config.name = tokenData.fb_name ?? ''
    config.preferredAdAccount = ''
    let adAccountId = shopState.fbAccountId ?? ''; //last accessed facebook account ID

    for (var key in accounts) {
        if (accounts[key].id == adAccountId) { // one of the valid accounts
            config.preferredAdAccount = adAccountId;
            break;
        }
    }

    await cache.set('facebook_config_' + shop.shop_id, config)

    return config
}

async function afterAuth({shop, authResult}) {
    logger.info(`fb.afterAuth`, authResult);

    let response = {accounts: [], error: '', error_message : ''};

    if ('error' in authResult) {
        // error handling/if required
        logger.error('fb.afterAuth error: ', authResult.error)
        response.error = 'AUTH_ERROR';
        response.error_message = authResult.error;
        return response
    }


    // TODO : match scopes and restart if all scopes not present
    if ('grantedScopes' in authResult && authResult.grantedScopes != '') {
        let missingScopes = []
        wantedScopes.forEach(el => {
            if (authResult.grantedScopes.indexOf(el) === -1) {
                missingScopes.push(el)
            }
        });

        if (missingScopes.length > 0) {
            response.error = 'MISSING_SCOPES';
            response.error_message = 'For facebook integration to work, Please grant access to all permissions during login'
            logger.error(response.error_message);
            return response
        }
    }

    try {
        // generate a long lived token
        authResult.LongLivedAccessToken = await generateToken(authResult);
    } catch (err) {
        logger.error(`generateToken Error: `, err);
        response.error_message = 'Something went wrong. Please try again later';
        response.error = 'UNKNOWN'
        return response
    } finally {
        var {data, existingAccount} = await saveToken(shop, authResult)
        logger.info('success', data)
        if (!existingAccount) {
            // run cloud task - sync data on new signup
            await initiateDataSync(shop.myshopify_domain, authResult.id, authResult.LongLivedAccessToken)
        }
    }


    if (!!authResult.LongLivedAccessToken) {
        // send options to choose 1 ad account
        response.accounts = await getAdAccounts(authResult.LongLivedAccessToken)
        if (!response.accounts || response.accounts.length == 0) {
            response.error = 'NO_AD_ACCOUNTS';
            response.error_message = 'No Ad Accounts are linked to this account. Please try logging in with a different account.'
            logger.error(response.error_message)
            return response
        }
    } else {
        response.error = 'TOKEN_ERROR'
        response.error_message = 'Issue in connecting with facebook. Please try again later.'
        logger.error(response.error_message)
        return response
    }

    return response
}

export default { afterAuth, getInitConfig, getMonthlyInsights, getToken };
