import logger from "../../logger.js";
import integrations from "../../airbyte/integrations.js";
import { queryFacebookCampaigns } from "./data.js";
import { SOURCE_FB, V1_PLATFORM_OSS } from "../../airbyte/constants.js";
import util from "../../common/util.js";
import facebookIntegration from "../../airbyte/facebookIntegration.js";
import secretmanager from "../../secretmanager/index.js";

async function updateCampaign(shop_id, account_id, campaign_id, status) {
    const intgs = await integrations.getIntegrations(shop_id, SOURCE_FB);
    let facebookIntegrations = intgs[SOURCE_FB] || [];
    let required_intg = facebookIntegrations.find(intg => intg.source_config_id === account_id);

    if (!required_intg) {
        return { error: true, message: 'Account id not found' };
    }

    const creds = await secretmanager.getCredentials(required_intg.token_id);

    return await facebookIntegration.updateCampaign(creds.access_token, campaign_id, status);
}

async function getAccessTokensForAllAdAccounts(shop_id) {
    let intgs = await integrations.getIntegrations(shop_id, SOURCE_FB);
    let all_creds = await secretmanager.getAllCredsByShopAndIntegrationType(shop_id, SOURCE_FB);

    let facebookIntegrations = intgs[SOURCE_FB] || [];
    let account_ids = facebookIntegrations.map(intg => intg.source_config_id);

    if (account_ids.length === 0) {
        return {};
    }

    // Create a map of account_id to access_token from all_creds
    let accountToTokenMap = {};
    for (let cred of all_creds) {
        let matchingIntg = facebookIntegrations.find(intg => intg.token_id === cred.token_id);
        if (!matchingIntg || accountToTokenMap[matchingIntg.source_config_id]) {
            // since all_creds is sorted by latest first, picking the latest access_token
            continue;
        }

        accountToTokenMap[matchingIntg.source_config_id] = cred.access_token;
    }

    return accountToTokenMap;
}

async function getFacebookPages(shop_id) {
    let accountToTokenMap = await getAccessTokensForAllAdAccounts(shop_id);

    if (Object.keys(accountToTokenMap).length === 0) {
        return { error: true, message: 'No account ids found' };
    }

    // Execute all Facebook API calls concurrently
    let pagesPromises = Object.keys(accountToTokenMap).map((account_id) => {
        let access_token = accountToTokenMap[account_id];
        if (!access_token) {
            logger.error('No access token found for account_id:', account_id);
            return [];
        }

        return facebookIntegration.fetchFacebookPages(access_token) || [];
    });

    let all_pages = [];
    let pagesResults = await Promise.all(pagesPromises);
    let pageIdSet = new Set(); // To track unique page IDs

    for (let pages of pagesResults) {
        for (let page of pages) {
            // Only add page if we haven't seen this page ID before
            if (!pageIdSet.has(page.id)) {
                pageIdSet.add(page.id);
                all_pages.push(page);
            }
        }
    }

    return {
        data: {
            pages: all_pages
        }
    };
}

async function getCampaignsMetrics(shop_id, start_date, end_date) {
    let accountToTokenMap = await getAccessTokensForAllAdAccounts(shop_id);

    if (Object.keys(accountToTokenMap).length === 0) {
        return { error: true, message: 'No account ids found' };
    }

    // Execute all Facebook API calls concurrently
    let campaignPromises = Object.keys(accountToTokenMap).map((account_id) => {
        let access_token = accountToTokenMap[account_id];
        if (!access_token) {
            logger.error('No access token found for account_id:', account_id);
            return [];
        }

        return facebookIntegration.getCampaignsInfo(access_token, account_id) || [];
    });

    let all_campaigns_info = {};
    let campaignResults = await Promise.all(campaignPromises);
    for (let campaignsInfo of campaignResults) {
        for (let campaign of campaignsInfo) {
            all_campaigns_info[campaign.id] = campaign;
        }
    }

    let fbIntgs = await integrations.getIntegrations(shop_id, SOURCE_FB);
    let fbIntgsOSS = (fbIntgs[SOURCE_FB] ?? []).filter(intg => intg.platform == V1_PLATFORM_OSS).map(intg => intg.auto_id);

    let result = await queryFacebookCampaigns(fbIntgsOSS, start_date, end_date, true);
    let data = result.data || [];

    var money = util.moneyFormatter("INR")
    for (var k in data) {
        data[k].campaign_name = data[k].campaign_name?.toString()
        data[k].campaign_id = data[k].campaign_id?.toString()
        data[k].account_id = data[k].account_id?.toString()
        data[k].spend = money(data[k].spend)
        data[k].website_purchase_value = money(data[k].website_purchase_value)
        data[k].impressions = data[k].impressions?.toString()
        data[k].clicks = data[k].clicks?.toString()
        data[k].campaign_status = all_campaigns_info[data[k].campaign_id]?.status || "-"
    }

    let response = {
        data: {
            items: data,
            count: data.length,
            currency: result.currency
        }
    }

    return response;
}

export default { updateCampaign, getCampaignsMetrics, getFacebookPages };
