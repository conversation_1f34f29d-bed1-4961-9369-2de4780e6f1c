import { cubeAdminApiRequest } from '../../cube/api.js';
import logger from '../../logger.js'
import facebook from '../index.js'

// parse result to remove prefix from keys and format for frontend
const parseResult = result =>
  result.map(row =>
    Object.keys(row).reduce((acc, key) => {
      let newKey = key.replace('meta_ads_insights.', '')
      acc[newKey] = row[key] ?? 0
      return acc
    }, {})
  )

export const queryFacebookCampaigns = async (
  integration_ids,
  start_date,
  end_date,
  useFBOSSDataset = false
) => {

  let response = {
    data: [],
    currency: ""
  }

  if (!integration_ids || integration_ids.length == 0) {
    return response;
  }

  const baseFilters = [
    {
      member: 'meta_ads_insights.integration_id',
      operator: 'equals',
      values: integration_ids
    }
  ]


  let dimensions = [
    'meta_ads_insights.account_id',
    'meta_ads_insights.campaign_id',
    'meta_ads_insights.campaign_name',
    "meta_ads_insights.account_currency",
  ]

  const query = {
    limit: 5000,
    dimensions: dimensions,
    measures: [
      'meta_ads_insights.spend',
      'meta_ads_insights.impressions',
      'meta_ads_insights.clicks',
      'meta_ads_insights.website_purchase_value'
    ],
    timeDimensions: [
      {
        dimension: 'meta_ads_insights.partition_dt',
        dateRange: [start_date, end_date]
      }
    ],
    filters: baseFilters,
    order: {
      'meta_ads_insights.spend': 'desc'
    }
  }

  try {
    const dataResultSet = await cubeAdminApiRequest(query, useFBOSSDataset);
    const dataResult = dataResultSet.data || [];
    const parsedResult = parseResult(dataResult)

    response.data = parsedResult
    response.currency = dataResult[0]?.account_currency
  } catch (error) {
    logger.error('Error in fetching facebook campaigns data', {
      error: error.message,
      errorStack: error.stack,
      query: query
    })
    throw error
  }

  return response
}
