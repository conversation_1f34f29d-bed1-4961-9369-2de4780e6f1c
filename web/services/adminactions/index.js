import airbyteAdminActions from "./airbyte.js";

export async function handleAdminAction(type, data) {
    let result;

    const { shop_id = null, request_id = null, platform = null } = data;

    switch (type) {
        case 'move_fb_integration_to_zz_legacy':
            if (!shop_id || !request_id) {
                throw new Error("shop_id and request_id are required for move_fb_integration_to_zz_legacy");
            }
            result = await airbyteAdminActions.moveFbIntegrationToZzLegacy(shop_id, request_id);
            break;

        case 'trigger_airbyte_sync':
            const { connection_id } = data;
            if (!platform || !connection_id) {
                throw new Error("platform and connection_id are required for trigger_sync");
            }
            result = await airbyteAdminActions.triggerAirbyteSync(platform, connection_id);
            break;

        case 'disconnect_airbyte_integration':
            if (!request_id) {
                throw new Error("request_id is required for disconnect_airbyte_integration");
            }

            result = await airbyteAdminActions.disconnectAirbyteIntegration(request_id);
            break;

        case 'move_shopify_to_airbyte_oss':
            if (!shop_id || !platform) {
                throw new Error("shop_id and platform are required for move_shopify_to_airbyte_oss");
            }
            result = await airbyteAdminActions.moveShopifyToAirbyteOss(shop_id, platform);
            break;

        default:
            throw new Error("Invalid type");
    }

    return result;
}
