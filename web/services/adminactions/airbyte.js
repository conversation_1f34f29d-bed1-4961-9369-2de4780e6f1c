import integrations from "../airbyte/integrations.js";
import { triggerSync } from "../airbyte/integrations.js";
import crypto from "crypto";
import {
    PLATFORM_CLOUD,
    V1_PLATFORM_OSS,
    INTEGRATION_STATUS_DRAFT,
    INTEGRATION_STATUS_CONNECTED,
    SOURCE_FB
} from "../airbyte/constants.js";
import airbyteCloud from "../airbyte/cloud.js";
import logger from "../logger.js";

async function triggerAirbyteSync(platform, connection_id) {
    if (!platform || !connection_id || ![PLATFORM_CLOUD, V1_PLATFORM_OSS].includes(platform)) {
        logger.error("trigger_sync: invalid parameters", { platform, connection_id });
        return false;
    }

    try {
        const response = await triggerSync(connection_id, platform);
        if (response) {
            logger.info("trigger_sync: Triggered sync for connection", connection_id);
            return true;
        } else {
            logger.error("trigger_sync: Failed to trigger sync for connection", connection_id);
            return false;
        }
    } catch (error) {
        logger.error("Failed to trigger sync for connection", connection_id, error);
        return false;
    }
}

// TODO: fix connection name
async function moveFbIntegrationToZzLegacy(shop_id, old_intg_request_id) {
    if (!old_intg_request_id) {
        logger.error("move_fb_connection: invalid old_intg_request_id", { old_intg_request_id });
        return false;
    }

    const source_type = SOURCE_FB;

    const old_intg = await integrations.getIntegrationRequest(old_intg_request_id);

    logger.info("moveFbIntegrationToZzLegacy: Moving FB connection for shop", shop_id);

    if (!old_intg || !old_intg.request_id) {
        logger.error("moveFbIntegrationToZzLegacy: integration not found", { old_intg_request_id });
        return false;
    }

    const request_id = crypto.randomUUID();
    const platform = PLATFORM_CLOUD;

    const req_data = {
        shop_id,
        source_type,
        status: INTEGRATION_STATUS_DRAFT,
        request_id,
        platform,
        airbyte_workspace_id: airbyteCloud.getWorkspaceId(),
        airbyte_destination_id: airbyteCloud.getDestinationId(source_type),
        airbyte_source_id: old_intg.airbyte_source_id,
        secret_id: old_intg.secret_id,
        source_config: old_intg.source_config
    };

    logger.info("moveFbIntegrationToZzLegacy: Creating new integration with req_data: ", req_data);

    let done = await integrations.insert(req_data);
    if (!done) {
        logger.error("handleConnect failed to insert", { request_id });
        return false;
    }

    await new Promise(resolve => setTimeout(resolve, 2000));

    const intg = await integrations.getIntegrationRequest(request_id);
    if (!intg || !intg.request_id) {
        logger.error("getIntegrationRequest: invalid request_id", { request_id });
        return false;
    }

    logger.info("moveFbIntegrationToZzLegacy: Creating connection for new integration");

    let response = await airbyteCloud.createConnection(intg)
    if (response.connection_id) {
        logger.info("moveFbIntegrationToZzLegacy: Connection created", response.connection_id)
        await integrations.updatePartial(request_id, {
            airbyte_connection_id: response.connection_id,
            status: INTEGRATION_STATUS_CONNECTED
        })
    } else {
        logger.error("moveFbIntegrationToZzLegacy: Connection creation failed", response)
        return false
    }

    return true;
}

async function disconnectAirbyteIntegration(request_id) {
    if (!request_id) {
        logger.error("disconnectAirbyteIntegration: invalid request_id", { request_id });
        return false;
    }

    try {
        await integrations.handleDisconnect(request_id);
        return true;
    } catch (error) {
        logger.error("disconnectAirbyteIntegration: failed to disconnect", { request_id, error });
        return false;
    }
}

async function moveShopifyToAirbyteOss(shop_id, platform) {
    if (!shop_id || !platform || ![PLATFORM_CLOUD, V1_PLATFORM_OSS].includes(platform)) {
        logger.error("moveShopifyToAirbyteOss: invalid parameters", { shop_id, platform });
        return false;
    }

    try {
        return await integrations.setupShopifyConnection(shop_id, platform);
    } catch (error) {
        logger.error("moveShopifyToAirbyteOss: failed to move shopify to airbyte oss", { shop_id, platform, error });
        return false;
    }

}

export default {
    moveFbIntegrationToZzLegacy,
    triggerAirbyteSync,
    disconnectAirbyteIntegration,
    moveShopifyToAirbyteOss
}
