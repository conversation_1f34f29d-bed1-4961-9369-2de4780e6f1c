
import { createRequire } from "module";
const require = createRequire(import.meta.url);
const dayjs = require("dayjs");
import logger from '../logger.js';
import ltv from '../ltv/index.js';
import {rfmSegmentExport} from '../cube/data.js';

async function getJSONData({ shop, type, parameters }) {
    let response = {
        data : {},
        fields : [],
        error : ''
    }

    switch (type) {
        case 'basic_segments_export':
            let exportResopnse = await ltv.profileJSON(shop, parameters.start_date, parameters.end_date);
            response.data = exportResopnse.data ?? {};
            response.fields = exportResopnse.fields ?? [];
            response.error = exportResopnse.error ?? '';
            break;
        case 'rfm_segments_export':
            let exportRFMResopnse = await rfmSegmentExport(shop.shop_id, parameters.segment, parameters.period);
            response.data = exportRFMResopnse.data ?? {};
            response.fields = exportRFMResopnse.fields ?? [];
            response.error = exportRFMResopnse.error ?? '';
            break;
        case 'product_performance_export': {
        
            const { data = [], fields = [], error = '' } = await ltv.productPerformanceExport({
                shop,
                start_date: parameters.start_date,
                end_date: parameters.end_date,
                breakdown: parameters.breakdown || 'product_name',
                applied_filters: parameters.applied_filters || {},
                inline_filters: parameters.inline_filters || {},
                column_visibility: parameters.column_visibility || {}
            });
            
            response.data = data ?? [];
            response.fields = fields ?? [];
            response.error = error ?? '';
            break;
        }
    }

    return response;
}


export default {
    getJSONData
};