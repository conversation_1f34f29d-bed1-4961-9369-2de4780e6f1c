import { cubeAdminApiRequest } from "../../cube/api.js";
import util from "../../common/util.js";
import dayjs from "dayjs";
import quarterOfYear from "dayjs/plugin/quarterOfYear";
import weekOfYear from "dayjs/plugin/weekOfYear";
import isoWeek from "dayjs/plugin/isoWeek";
dayjs.extend(quarterOfYear);
dayjs.extend(weekOfYear);
dayjs.extend(isoWeek);
import logger from "../../logger.js";
import reporterUtil from "../util.js";


const DIMENSIONS = [
  "facebook-marketing:ad_id",
  "facebook-marketing:account_id",
  "facebook-marketing:campaign_id",
  "facebook-marketing:adset_id",
  "facebook-marketing:objective",
  "facebook-marketing:account_currency",
  "facebook-marketing:optimization_goal",
  "facebook-marketing:account_name",
  "facebook-marketing:campaign_name",
  "facebook-marketing:adset_name",
  "facebook-marketing:ad_name",
  "facebook-marketing:buying_type",
  "facebook-marketing:attribution_setting"
]

// parse result to remove prefix from keys
const parseResult = (result) => result.map(row =>
  Object.keys(row).reduce((acc, key) => {
    const newKey = key.replace("meta_ads_insights.", "facebook-marketing:");
    acc[newKey] = row[key] ?? 0;
    return acc;
  }, {})
);

const queryAccountMetrics = async (integration_ids, start_date, end_date, compare, compare_start_date, compare_end_date, granularity = "", useFBOSSDataset = false) => {
  let response = {
    currency: "",
    current: [],
    previous: []
  }

  if (!integration_ids || integration_ids.length == 0) {
    return response
  }

  let timeDimensionFilter = {
    "dimension": "meta_ads_insights.partition_dt",
    "compareDateRange": [
      [start_date, end_date]
    ],
  }

  if (compare) {
    timeDimensionFilter.compareDateRange.push([compare_start_date, compare_end_date])
  }

  // Validate granularity
  if (granularity && !util.TIME_FRAMES[granularity]) {
    logger.error("Invalid granularity", {granularity})
    response.error = "Invalid granularity";
    return response;
  }
  
  if (granularity) {
    timeDimensionFilter.granularity = granularity;
  }

  let query = {
    "limit": 5000,
    "dimensions": [
      // "meta_ads_insights.account_id",
      // "meta_ads_insights.account_name",
      "meta_ads_insights.account_currency",
    ],
    "filters": [
      {
        "member": "meta_ads_insights.integration_id",
        "operator": "equals",
        "values": integration_ids
      }
    ],
    "measures": [
      "meta_ads_insights.spend",
      "meta_ads_insights.clicks",
      "meta_ads_insights.impressions",
      "meta_ads_insights.link_click",
      "meta_ads_insights.add_to_cart",
      "meta_ads_insights.purchase",
      "meta_ads_insights.landing_page_view",
      "meta_ads_insights.website_lead",
      "meta_ads_insights.website_view_content",
      "meta_ads_insights.website_add_to_cart",
      "meta_ads_insights.website_initiate_checkout",
      "meta_ads_insights.website_purchase",
      "meta_ads_insights.app_view_content",
      "meta_ads_insights.app_add_to_cart",
      "meta_ads_insights.app_initiate_checkout",
      "meta_ads_insights.app_purchase",
      "meta_ads_insights.add_to_cart_value",
      "meta_ads_insights.website_purchase_value",
      "meta_ads_insights.app_purchase_value",
      "meta_ads_insights.roas",
      "meta_ads_insights.cpc_link",
      "meta_ads_insights.cpc_all",
      "meta_ads_insights.ctr_link",
      "meta_ads_insights.ctr_all",
      "meta_ads_insights.cost_per_purchase",
      "meta_ads_insights.cost_per_website_purchase",
      "meta_ads_insights.cost_per_add_to_cart"
    ],
    "timeDimensions": [timeDimensionFilter],
    "order": [["meta_ads_insights.partition_dt", "asc"]]
  }

  try {
      const cubeResponse = await cubeAdminApiRequest(query, useFBOSSDataset, "multi");
      const results = cubeResponse.results ?? [];

      results.forEach((currentResultSet, ind) => {
          let rawData = currentResultSet.data;
        if (rawData.length !== 0) {

          let parsedData = parseResult(rawData)
          response.currency = parsedData[0]["facebook-marketing:account_currency"] ?? "";
          if (ind == 0 && !granularity) {
            response.current = parsedData
          } else if (ind == 1 && !granularity) {
            response.previous = parsedData
          } else if (ind == 0 && !!granularity) {
            const timeDimension = `facebook-marketing:partition_dt.${granularity}`;
            response.current = reporterUtil.fillMissingDates('facebook-marketing', parsedData, start_date, end_date, granularity, timeDimension, DIMENSIONS)
          } else if (ind == 1 && !!granularity) {
            const timeDimension = `facebook-marketing:partition_dt.${granularity}`;
            response.previous = reporterUtil.fillMissingDates('facebook-marketing', parsedData, compare_start_date, compare_end_date, granularity, timeDimension, DIMENSIONS)
          }
        }
    });

    return response;
} catch (error) {
    logger.error("Error in fetching facebook ads account metrics", {
      error: error,
      query: query
    })
  response.error = error.message ?? ""
  return response
}
}

const queryPivotTable = async (integration_ids, start_date, end_date, useFBOSSDataset = false) => {

  if (!integration_ids || integration_ids.length == 0) {
    return {data: []}
  }

  let query = {
    "dimensions": [
      "meta_ads_insights.account_id",
      "meta_ads_insights.account_name",
      "meta_ads_insights.account_currency",
      "meta_ads_insights.campaign_id",
      "meta_ads_insights.campaign_name",
      "meta_ads_insights.adset_id",
      "meta_ads_insights.adset_name",
      "meta_ads_insights.ad_id",
      "meta_ads_insights.ad_name",
    ],
    "filters": [
      {
        "member": "meta_ads_insights.spend",
        "operator": "notEquals",
        "values": ["0"]
      },
      {
        "member": "meta_ads_insights.integration_id",
        "operator": "equals",
        "values": integration_ids
      }
    ],
    "measures": [
      "meta_ads_insights.spend",
      "meta_ads_insights.clicks",
      "meta_ads_insights.impressions",
      "meta_ads_insights.link_click",
      "meta_ads_insights.add_to_cart",
      "meta_ads_insights.purchase",
      "meta_ads_insights.landing_page_view",
      "meta_ads_insights.website_lead",
      "meta_ads_insights.website_view_content",
      "meta_ads_insights.website_add_to_cart",
      "meta_ads_insights.website_initiate_checkout",
      "meta_ads_insights.website_purchase",
      "meta_ads_insights.app_view_content",
      "meta_ads_insights.app_add_to_cart",
      "meta_ads_insights.app_initiate_checkout",
      "meta_ads_insights.app_purchase",
      "meta_ads_insights.add_to_cart_value",
      "meta_ads_insights.website_purchase_value",
      "meta_ads_insights.app_purchase_value",
      "meta_ads_insights.roas",
      "meta_ads_insights.cpc_link",
      "meta_ads_insights.cpc_all",
      "meta_ads_insights.ctr_link",
      "meta_ads_insights.ctr_all",
      "meta_ads_insights.cost_per_purchase",
      "meta_ads_insights.cost_per_website_purchase",
      "meta_ads_insights.cost_per_add_to_cart"
    ],
    "timeDimensions": [{
        "dimension": "meta_ads_insights.partition_dt",
        "dateRange": [start_date, end_date]
    }]
  }

  try {
    const dataResultSet = await cubeAdminApiRequest(query, useFBOSSDataset);
    const dataResult = dataResultSet.data || [];
    return { data: parseResult(dataResult) };
  } catch (error) {
    logger.error("Error in fetching facebook ads pivot table", {
      error: error,
      query: query
    })
    return {data: [], error: error.message ?? ""}
  }
}


export const queryWithBreakdown = async (integration_ids, start_date, end_date, granularity, breakdown, useFBOSSDataset = false) => {

  let response = {
    currency: "",
    current: [],
  }

  if (!integration_ids || integration_ids.length == 0) {
    return response
  }

  let timeDimensionFilter = {
    "dimension": "meta_ads_insights.partition_dt",
    "dateRange": [start_date, end_date]
  }

  // Validate granularity
  if (granularity && !util.TIME_FRAMES[granularity]) {
    logger.error("Invalid granularity", {granularity})
    response.error = "Invalid granularity";
    return response;
  }

  if (granularity) {
    timeDimensionFilter.granularity = granularity;
  }

  let dimensions = []

  switch (breakdown) {
    case "ad":
      dimensions.push("meta_ads_insights.ad_id")
      dimensions.push("meta_ads_insights.ad_name")
    case "adset":
      dimensions.push("meta_ads_insights.adset_id")
      dimensions.push("meta_ads_insights.adset_name")
    case "campaign":
      dimensions.push("meta_ads_insights.campaign_id")
      dimensions.push("meta_ads_insights.campaign_name")
    case "objective":
      dimensions.push("meta_ads_insights.objective")
    case "account":
      dimensions.push("meta_ads_insights.account_id")
      dimensions.push("meta_ads_insights.account_name")
      dimensions.push("meta_ads_insights.account_currency")
  }

  let query = {
    "limit": 5000,
    "dimensions": dimensions,
    "filters": [
      {
        "member": "meta_ads_insights.integration_id",
        "operator": "equals",
        "values": integration_ids
      },
      {
        "member": "meta_ads_insights.spend",
        "operator": "notEquals",
        "values": ["0"]
      }
    ],
    "measures": [
      "meta_ads_insights.spend",
      "meta_ads_insights.clicks",
      "meta_ads_insights.impressions",
      "meta_ads_insights.link_click",
      "meta_ads_insights.add_to_cart",
      "meta_ads_insights.purchase",
      "meta_ads_insights.landing_page_view",
      "meta_ads_insights.website_lead",
      "meta_ads_insights.website_view_content",
      "meta_ads_insights.website_add_to_cart",
      "meta_ads_insights.website_initiate_checkout",
      "meta_ads_insights.website_purchase",
      "meta_ads_insights.app_view_content",
      "meta_ads_insights.app_add_to_cart",
      "meta_ads_insights.app_initiate_checkout",
      "meta_ads_insights.app_purchase",
      "meta_ads_insights.add_to_cart_value",
      "meta_ads_insights.website_purchase_value",
      "meta_ads_insights.app_purchase_value",
      "meta_ads_insights.roas",
      "meta_ads_insights.cpc_link",
      "meta_ads_insights.cpc_all",
      "meta_ads_insights.ctr_link",
      "meta_ads_insights.ctr_all",
      "meta_ads_insights.cost_per_purchase",
      "meta_ads_insights.cost_per_website_purchase",
      "meta_ads_insights.cost_per_add_to_cart"
    ],
    "timeDimensions": [timeDimensionFilter],
    "order": [["meta_ads_insights.spend", "desc"]]
  }

  try {
    const dataResultSet = await cubeAdminApiRequest(query, useFBOSSDataset);
    const dataResult = dataResultSet.data || [];

    if (dataResult.length === 0) {
      return response;
    }


    let parsedData = parseResult(dataResult)
    response.currency = parsedData[0]["facebook-marketing:account_currency"] ?? "";
    const timeDimension = `facebook-marketing:partition_dt.${granularity}`;
    response.current = reporterUtil.fillMissingDates('facebook-marketing', parsedData, start_date, end_date, granularity, timeDimension, DIMENSIONS)
    return response;
  } catch (error) {
    logger.error("Error in fetching facebook ads breakdown metrics", {
      error: error,
      query: query
    })
    response.error = error.message ?? ""
    return response
  }
}


export default {
  queryAccountMetrics,
  queryPivotTable,
  queryWithBreakdown
}