import { SOURCE_FB, PLATFORM_CLOUD, V1_PLATFORM_OSS } from '../../airbyte/constants.js';
import logger from '../../logger.js';
import data from './data.js';
import errors from '../../errors.js';
import {getIntegrations} from '../../airbyte/integrations.js';

const summary = async (integrations, filters) => {

    const {
        start_date,
        end_date,
        compare,
        compare_start_date,
        compare_end_date,
        time_frame
    } = filters;


    let response = {
        data: {
            currency: "",
            current: [],
            previous: [],
            currentAgg : {},
            previousAgg: {}
        }
    }

    if (!integrations || integrations.length == 0) {
        return response;
    }

    let {useFBOSSDataset, integrations: intgs} = getFacebookIntegrationsForQuery(integrations)
    let integration_ids = intgs.map(intg => intg.auto_id)
    try {
        
        let granularData = data.queryAccountMetrics(integration_ids, start_date, end_date, compare, compare_start_date, compare_end_date, time_frame, useFBOSSDataset)
        let aggregateData = data.queryAccountMetrics(integration_ids, start_date, end_date, compare, compare_start_date, compare_end_date, "", useFBOSSDataset)

        let resultSet = await Promise.all([
            granularData,
            aggregateData
        ]);

        // TODO - handle error

        response.data.currency = resultSet[0]?.currency ?? resultSet[1]?.currency ?? ""
        response.data.current = resultSet[0].current ?? []
        response.data.previous = resultSet[0].previous ?? []
        response.data.currentAgg = resultSet[1].current?.[0] ?? {}
        response.data.previousAgg = resultSet[1].previous?.[0] ?? {}
        return response;
    } catch (e) {
        logger.error('facebook.summary error', e)
        return response;
    }
}


// filtering facebook integrations - to only get either cloud or oss at a time
// preceedence for oss integrations
// we should not ask to fetch a mix of oss and cloud integrations - logs a warning if this happens
const getFacebookIntegrationsForQuery = (fbIntgs) => {

    if (!fbIntgs || fbIntgs.length == 0) {
        return {
            useFBOSSDataset: false,
            integrations : []
        }
    }

    let useFBOSSDataset = fbIntgs.some(intg => intg.platform == V1_PLATFORM_OSS)
    let filtered = useFBOSSDataset 
        ? fbIntgs.filter(intg => intg.platform == V1_PLATFORM_OSS)
        : fbIntgs.filter(intg => intg.platform == PLATFORM_CLOUD);

    if (filtered.length != fbIntgs.length) {
        logger.warn('facebook.getFacebookIntegrationsForQuery - filtered integrations length mismatch', {
            useFBOSSDataset,
            shop_id: fbIntgs[0].shop_id,
            integration_ids: fbIntgs.map(intg => intg.auto_id),
            fbIntgsLength: fbIntgs.length,
            filteredLength: filtered.length
        })
    }

    return {
        useFBOSSDataset,
        integrations : filtered
    }
}

// depreated - legacy call
const pivotTable = async (shop_id, filters) => {
    const {
        start_date,
        end_date,
        integration_request_ids
    } = filters;

    let response = {
        data: []
    }

    if (!integration_request_ids || integration_request_ids.length == 0) {
        response.error = errors.client.invalidInput
        return response;
    }

    let intgs = await getIntegrations(shop_id, SOURCE_FB)
    let fbIntgs = (intgs[SOURCE_FB] ?? []).filter(intg => integration_request_ids.includes(intg.request_id))
    let {useFBOSSDataset, integrations} = getFacebookIntegrationsForQuery(fbIntgs)
    let integration_ids = integrations.map(intg => intg.auto_id)
    try {
        let result = await data.queryPivotTable(integration_ids, start_date, end_date, useFBOSSDataset)
        response.data = result.data ?? []
        return response;
    } catch (e) {
        logger.error('facebook.pivotTable error', e)
        return response;
    }
}

const breakdownData = async (integrations, filters) => {
    const {
        start_date,
        end_date,
        time_frame,
        breakdown
    } = filters;

    let response = {
        data: {
            currency: "",
            current: [],
        }
    }

    if (!integrations || integrations.length == 0) {
        return response;
    }

    let {useFBOSSDataset, integrations: intgs} = getFacebookIntegrationsForQuery(integrations)
    let integration_ids = intgs.map(intg => intg.auto_id)

    try {
        let result = await data.queryWithBreakdown(integration_ids, start_date, end_date, time_frame, breakdown, useFBOSSDataset)
        
        response.data.currency = result.currency ?? ""
        response.data.current = result.current ?? []
        return response;
    } catch (e) {
        logger.error('facebook.breakdownData error', e)
        return response;
    }
}

export default {
    summary,
    pivotTable,
    breakdownData
}