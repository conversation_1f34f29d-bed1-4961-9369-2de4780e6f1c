import logger from '../logger.js'
import errors from '../errors.js'
import breakdown from './breakdown/index.js';
import facebook from './facebook/index.js';
import metrics from './metrics/index.js';

const build = async (shop_id, type, filters) => {

  let response = {}
  switch (type) {
    case 'facebook_pivot_table':
      response = await facebook.pivotTable(shop_id, filters)
      break
    case 'breakdown_data':
      response = await breakdown.data(shop_id, filters)
      break
    case 'metrics_summary':
      response = await metrics.summary(shop_id, filters)
      break
  }

  if (response.error) {
    logger.error('reporter.build error', {error: response.error})
    return {error: errors.client.generic}
  }

  return response
}

export default {
  build
}
