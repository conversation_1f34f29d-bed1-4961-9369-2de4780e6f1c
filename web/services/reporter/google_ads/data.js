import { cubeAdminApiRequest } from "../../cube/api.js";
import util from "../../common/util.js";
import dayjs from "dayjs";
import quarterOfYear from "dayjs/plugin/quarterOfYear";
import weekOfYear from "dayjs/plugin/weekOfYear";
import isoWeek from "dayjs/plugin/isoWeek";
dayjs.extend(quarterOfYear);
dayjs.extend(weekOfYear);
dayjs.extend(isoWeek);
import logger from "../../logger.js";
import reporterUtil from "../util.js";

const DIMENSIONS = [
  "google-ads:customer_currency_code",
  "google-ads:campaign_id",
  "google-ads:campaign_name",
  "google-ads:campaign_status",
  "google-ads:segments_ad_network_type",
  "google-ads:integration_id",
  "google-ads:customer_id",
  "google-ads:customer_descriptive_name",
]

// parse result to remove prefix from keys
const parseResult = (result) => result.map(row =>
  Object.keys(row).reduce((acc, key) => {
    const newKey = key.replace("google_ads_account_metrics.", "google-ads:").replace("google_ads_campaign.", "google-ads:");
    acc[newKey] = row[key] ?? 0;
    return acc;
  }, {})
);

const queryAccountMetrics = async (integration_ids, start_date, end_date, compare, compare_start_date, compare_end_date, granularity = "") => {
  let response = {
    currency : "",
    current: [],
    previous: []
  }

  if (!integration_ids || integration_ids.length == 0) {
    logger.error("Invalid integration_ids", {integration_ids})
    response.error = "Invalid integration_ids";
    return response
  }

  let timeDimensionFilter = {
    "dimension": "google_ads_account_metrics.segments_date",
    "compareDateRange": [
      [start_date, end_date]
    ],
  }

  if (compare) {
    timeDimensionFilter.compareDateRange.push([compare_start_date, compare_end_date])
  }

  // Validate granularity
  if (granularity && !util.TIME_FRAMES[granularity]) {
    logger.error("Invalid granularity", {granularity})
    response.error = "Invalid granularity";
    return response;
  }

  if (granularity) {
    timeDimensionFilter.granularity = granularity;
  }

  let query = {
    "limit": 5000,
    "dimensions": [
      "google_ads_account_metrics.customer_currency_code"
    ],
    "filters": [
      {
        "member": "google_ads_account_metrics.integration_id",
        "operator": "equals",
        "values": integration_ids
      }
    ],
    "measures": [
      "google_ads_account_metrics.clicks",
      "google_ads_account_metrics.conversions",
      "google_ads_account_metrics.cost",
      "google_ads_account_metrics.cost_per_conversion",
      "google_ads_account_metrics.cost_per_conversion_all",
      "google_ads_account_metrics.conversions_value",
      "google_ads_account_metrics.conversions_value_all",
      "google_ads_account_metrics.conversions_all",
      "google_ads_account_metrics.count",
      "google_ads_account_metrics.cpc",
      "google_ads_account_metrics.cpm",
      "google_ads_account_metrics.ctr",
      "google_ads_account_metrics.engagements",
      "google_ads_account_metrics.impressions",
      "google_ads_account_metrics.order_rate",
      "google_ads_account_metrics.order_rate_all",
      "google_ads_account_metrics.revenue_per_click",
      "google_ads_account_metrics.revenue_per_click_all",
      "google_ads_account_metrics.roas",
      "google_ads_account_metrics.roas_all",
      "google_ads_account_metrics.rpm",
      "google_ads_account_metrics.rpm_all",
      "google_ads_account_metrics.value_per_conversion",
      "google_ads_account_metrics.video_views",
      "google_ads_account_metrics.view_through_conversions",
      "google_ads_account_metrics.avg_conversion_value_all",
      "google_ads_account_metrics.avg_conversion_value"
    ],
    "timeDimensions": [timeDimensionFilter],
    "order": [["google_ads_account_metrics.segments_date", "asc"]]
  }

  try {
      const cubeResponse = await cubeAdminApiRequest(query, false, "multi");
      const results = cubeResponse.results ?? [];

      results.forEach((currentResultSet, ind) => {
          let rawData = currentResultSet.data;
          if (rawData.length !== 0) {

            let parsedData = parseResult(rawData)
            response.currency = parsedData[0]["google-ads:customer_currency_code"] ?? "";
            if (ind == 0 && !granularity) {
              response.current = parsedData
            } else if (ind == 1 && !granularity) {
              response.previous = parsedData
            } else if (ind == 0 && !!granularity) {
              const timeDimension = `google-ads:segments_date.${granularity}`;
              response.current = reporterUtil.fillMissingDates('google-ads', parsedData, start_date, end_date, granularity, timeDimension, DIMENSIONS)
            } else if (ind == 1 && !!granularity) {
              const timeDimension = `google-ads:segments_date.${granularity}`;
              response.previous = reporterUtil.fillMissingDates('google-ads', parsedData, compare_start_date, compare_end_date, granularity, timeDimension, DIMENSIONS)
            }
          }
      });

      return response;
  } catch (error) {
    logger.error("Error in fetching google ads account metrics", {
      error: error,
      query: query
    })
    response.error = error.message ?? ""
    return response
  }
}

export const queryWithBreakdown = async (integration_ids, start_date, end_date, granularity, breakdown) => {

  let response = {
    currency: "",
    current: [],
  }

  let timeDimensionFilter = {
    "dimension": "google_ads_campaign.segment_datetime",
    "dateRange": [start_date, end_date]
  }

  // Validate granularity
  if (granularity && !util.TIME_FRAMES[granularity]) {
    logger.error("Invalid granularity", {granularity})
    response.error = "Invalid granularity";
    return response;
  }

  if (granularity) {
    timeDimensionFilter.granularity = granularity;
  }

  let dimensions = [
    "google_ads_campaign.customer_id",
    "google_ads_campaign.customer_currency_code",
    "google_ads_campaign.customer_descriptive_name"
  ];

  switch (breakdown) {
    case "campaign":
      dimensions.push("google_ads_campaign.campaign_id")
      dimensions.push("google_ads_campaign.campaign_name")
      dimensions.push("google_ads_campaign.campaign_status")
      break
    case 'ad_network_type':
      dimensions.push("google_ads_campaign.segments_ad_network_type")
      break
    case "account":
      break;
  }

  let query = {
    "limit": 5000,
    "dimensions": dimensions,
    "filters": [
      {
        "member": "google_ads_campaign.integration_id",
        "operator": "equals",
        "values": integration_ids
      }
    ],
    "measures": [
      "google_ads_campaign.clicks",
      "google_ads_campaign.impressions",
      "google_ads_campaign.video_views",
      "google_ads_campaign.interactions",
      "google_ads_campaign.active_view_impressions",
      "google_ads_campaign.active_view_measurable_impressions",
      "google_ads_campaign.conversions",
      "google_ads_campaign.cost",
      "google_ads_campaign.conversions_value",
      "google_ads_campaign.active_view_measurable_cost",
      "google_ads_campaign.ctr",
      "google_ads_campaign.cpc",
      "google_ads_campaign.cpm",
      "google_ads_campaign.cost_per_conversion",
      "google_ads_campaign.avg_conversion_value",
      "google_ads_campaign.revenue_per_click",
      "google_ads_campaign.rpm",
      "google_ads_campaign.roas",
      "google_ads_campaign.order_rate",
      "google_ads_campaign.active_view_cpm",
      "google_ads_campaign.cost_per_interaction",
      "google_ads_campaign.value_per_conversion",
      "google_ads_campaign.active_view_viewability",
      "google_ads_campaign.active_view_measurability"
    ],
    "timeDimensions": [timeDimensionFilter],
    "order": [["google_ads_campaign.cost", "desc"]]
  }

  try {
      const dataResultSet = await cubeAdminApiRequest(query);
      const dataResult = dataResultSet.data || [];

      if (dataResult.length === 0) {
      return response;
    }

      let parsedData = parseResult(dataResult)
    response.currency = parsedData[0]["google-ads:customer_currency_code"] ?? "";
    const timeDimension = `google-ads:segment_datetime.${granularity}`;
    response.current = reporterUtil.fillMissingDates('google-ads', parsedData, start_date, end_date, granularity, timeDimension, DIMENSIONS)

    return response;
  } catch (error) {
    logger.error("Error in fetching google ads breakdown metrics", {
      error: error,
      query: query
    })
    // response.error = 
    return response;
  }
}

export default {
  queryAccountMetrics,
  queryWithBreakdown
}