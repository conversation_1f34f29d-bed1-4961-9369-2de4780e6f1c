import { queryProductPerformance } from '../../ltv/product/performance/data.js';
import bigquery from '../../bigquery/index.js';
import { DEFAULT_REPURCHASE_WINDOW } from '../../ltv/repurchase.js';

export const AutoInsightsSupportedComponents = {
    "product-performance": "Product Performance",
    "basket-analysis": "Basket Analysis",
    "repurchase-rate": "Repurchase Rate"
}

export async function getInlineData(component_id, shop_id, filters = {}) {
    if (!AutoInsightsSupportedComponents[component_id]) {
        throw new Error(`Component ${component_id} is not supported`);
    }

    if (!filters.start_date || !filters.end_date) {
        // Set duration as last 12 months if dates are missing
        const endDate = new Date();
        const startDate = new Date();
        startDate.setMonth(startDate.getMonth() - 12);
        
        filters.start_date = filters.start_date || startDate.toISOString().split('T')[0];
        filters.end_date = filters.end_date || endDate.toISOString().split('T')[0];
    }

    switch (component_id) {
        case "product-performance":
            const { data: productPerformanceData } = await queryProductPerformance(
                shop_id,
                filters.start_date,
                filters.end_date,
                filters.breakdown
            );
            return productPerformanceData ?? [];
        case "basket-analysis":
            const basketAnalysisData = await bigquery.cartAnalysis(
                shop_id,
                filters.start_date,
                filters.end_date
            );
            return basketAnalysisData ?? [];
        case "repurchase-rate":
            const repurchaseRateData = await bigquery.productRepurchases(
                shop_id,
                filters.start_date,
                filters.end_date,
                DEFAULT_REPURCHASE_WINDOW
            );
            return repurchaseRateData ?? [];
        default:
            throw new Error(`Component ${component_id} is not supported`);
   }
}