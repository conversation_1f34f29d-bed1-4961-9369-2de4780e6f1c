import { cache } from "../../redis.js";

const CACHE_KEY_TEMPLATE = "auto_insights_{shop_id}_{component_id}_{start_date}_{end_date}";

const AUTO_INSIGHTS_CACHE_TTL = 24 * 60 * 60; // 24 hours

function getCacheKey(shopId, componentId, startDate, endDate) {
    return CACHE_KEY_TEMPLATE
        .replace("{shop_id}", shopId)
        .replace("{component_id}", componentId)
        .replace("{start_date}", startDate)
        .replace("{end_date}", endDate);
}

function processStreamEvent(event, res, cacheKey, mergedCustomStreamOutput = {}) {
    const eventType = event.event || 'message'; // Default to 'message' if no event type is provided
    const eventData = event.data || event;

    if (eventType == "custom" || eventType.startsWith("custom")) {
        for (const key in eventData) {
            if (eventData[key]) {
                mergedCustomStreamOutput[key] = eventData[key];
            }
        }
        if (mergedCustomStreamOutput?.insights && mergedCustomStreamOutput?.insights.length > 0) {
            cache.set(cacheKey, {
                insights: mergedCustomStreamOutput?.insights
            }, AUTO_INSIGHTS_CACHE_TTL);
        } else if (mergedCustomStreamOutput?.final_answer) {
            cache.set(cacheKey, {
                final_answer: mergedCustomStreamOutput?.final_answer
            }, AUTO_INSIGHTS_CACHE_TTL);
        }

        // Write event type and data to the response
        res.write(`event: custom\n`);
        res.write(`data: ${JSON.stringify(mergedCustomStreamOutput)}\n\n`);
    } else {
        // Write event type and data to the response as it is
        res.write(`event: ${eventType}\n`);
        res.write(`data: ${JSON.stringify(eventData)}\n\n`);
    }
    
    return mergedCustomStreamOutput;
}

export default {
    processStreamEvent,
    getCacheKey
};