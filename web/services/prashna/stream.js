function processStreamEvent(event, res, mergedCustomStreamOutput = {}) {
    // Extract event type and data
    const eventType = event.event || 'message'; // Default to 'message' if no event type is provided
    const eventData = event.data || event;

    if (eventType == "custom" || eventType.startsWith("custom")) {
        for (const key in eventData) {
            if (eventData[key]) {
                mergedCustomStreamOutput[key] = eventData[key];
            }
        }

        if (mergedCustomStreamOutput?.interrupt_message && mergedCustomStreamOutput?.interrupt_message != "") {
            res.write(`event: __interrupt__\n`);
            res.write(`data: ${JSON.stringify(mergedCustomStreamOutput)}\n\n`);
            res.end();
            return;
        }
        // Write event type and data to the response
        res.write(`event: custom\n`);
        res.write(`data: ${JSON.stringify(mergedCustomStreamOutput)}\n\n`);
    } else {
        // Write event type and data to the response as it is
        res.write(`event: ${eventType}\n`);
        res.write(`data: ${JSON.stringify(eventData)}\n\n`);
    }
    
    return mergedCustomStreamOutput;
}

export default {
    processStreamEvent
};