import integrations from '../airbyte/integrations.js';
import { SOURCE_GOOGLE_ADS, SOURCE_FB, SOURCE_GOOGLE_ANALYTICS } from '../airbyte/constants.js';
import { V1_PLATFORM_OSS } from '../airbyte/constants.js';

/**
 * For Admin:
 *  Filter threads only with shop_id -> threads created by admin + threads created by non-admin users
 *
 * For Non-Admin User Flow:
 *  Filter threads with shop_id and user_email -> threads unique to the user
 *
 * For Non-Admin Shop Flow:
 *  Filter threads with shop_id and user_email = "" -> threads common to all users of the shop
 */


export function getMetadataForSearchThreads(shop_id, is_admin, is_user_flow = false, user_email = "") {
    if (is_admin) {
        return {
            shop_id: shop_id
        }
    } else if (is_user_flow) {
        return {
            shop_id: shop_id,
            user_email: user_email,
            is_admin: false
        }
    } else {
        return {
            shop_id: shop_id,
            user_email: "", // to ensure user flow threads are not shown in shop flow
            is_admin: false
        }
    }
}

export async function getIntegrationSources(shop_id) {
  const intgs = await integrations.getIntegrations(shop_id, "")

  const googleAdsIntgs = (intgs[SOURCE_GOOGLE_ADS] ?? []).reduce((acc, intg) => {
    acc.source_config.integration_ids.push(intg.auto_id + '')
    return acc;
  }, { source: SOURCE_GOOGLE_ADS, source_config: { integration_ids: [] } })

  const facebookIntgs = (intgs[SOURCE_FB] ?? []).reduce((acc, intg) => {
    acc.source_config.integration_ids.push(intg.auto_id + '')
    acc.source_config.source_config_ids.push(intg.source_config_id)

    // Set region to EU if any integration has airbyte_oss_v1 source_type
    if (intg.platform == V1_PLATFORM_OSS) {
      acc.region = 'eu';
    }

    return acc;
  }, { source: SOURCE_FB, region: 'us', source_config: { integration_ids: [], source_config_ids: [] } })

  const googleAnalyticsIntgs = (intgs[SOURCE_GOOGLE_ANALYTICS] ?? []).reduce((acc, intg) => {
    acc.source_config.integration_ids.push(intg.auto_id + '')
    return acc;
  }, { source: SOURCE_GOOGLE_ANALYTICS, source_config: { integration_ids: [] } })

  return [googleAdsIntgs, facebookIntgs, googleAnalyticsIntgs];
}
