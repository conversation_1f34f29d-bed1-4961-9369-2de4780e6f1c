import { createRequire } from "module";
const require = createRequire(import.meta.url);

import logger from '../logger.js';
const { JobsClient } = require('@google-cloud/run').v2;


export const DBT_PROD_DATASET_OSS = 'dbt_prod';

const commandAliases = {
    'run': 'dbt run --target prod_eu',
    'test': 'dbt test --target prod_eu',
    'debug': 'dbt debug --target prod_eu',
    'shopify': 'dbt run -s tag:shopify_dashr --target prod_eu',
    'meta': 'dbt run -s tag:meta_ads_dashr_eu --target prod_eu',
    'meta_legacy': 'dbt run -s tag:meta_ads_dashr --target prod_us',
    'google_ads': 'dbt run -s tag:google_ads_dashr --target prod_eu',
    'google_analytics': 'dbt run -s tag:google_analytics_dashr --target prod_eu',
    'product_performance': 'dbt run -s tag:product_performance --target prod_eu'
}

// Enhanced dbt function interface
// Allows flexible parameters with named arguments for better usability
async function execute({
    commandAlias = 'debug',
    location = 'europe-west1',
    jobId = 'dashr-dbt-v1',
    waitForCompletion = false // New argument to control waiting for completion
}) {
    try {

        if (!process.env.PROJECT_ID) {
            throw new Error("PROJECT_ID is not set in environment variables");
        }

        if (!(commandAlias in commandAliases)) {
            throw new Error("Invalid or missing dbt command");
        }

        // Prepare the override arguments
        const overrideArgs = ['poetry', 'run', ...commandAliases[commandAlias].split(' ')];

        // Create a client for Cloud Run Jobs
        const client = new JobsClient();

        // Format the job name
        const jobName = `projects/${process.env.PROJECT_ID}/locations/${location}/jobs/${jobId}`;

        // Construct the request with the override arguments
        const request = {
            name: jobName,
            overrides: {
                containerOverrides: [
                    {
                        args: overrideArgs
                    }
                ]
            }
        };

        // Log job initiation
        logger.info(`Starting Cloud Run job with dbt command: ${commandAliases[commandAlias]}`, request);

        // Run the Cloud Run job asynchronously
        const [operation] = await client.runJob(request);
        logger.info(`Cloud Run job started: ${operation.name ?? ""}`);

        if (waitForCompletion) {
            // Wait for the operation to complete if the flag is set to true
            const [response] = await operation.promise();
            logger.info(`Cloud Run job completed successfully`, response);
            return response;
        } else {
            // Return immediately without waiting
            return { message: 'Job started successfully', operationName: operation.name ?? "" };
        }

    } catch (error) {
        // Handle errors and log them
        logger.error(`Error running dbt job: ${error.message}`, error);
        return { error: error.message };
    }
}

export async function dbt(commandAlias) {
    return execute({ commandAlias, location: 'europe-west1', jobId : 'dashr-dbt-v1', waitForCompletion: false });
}

export async function dbtSync(commandAlias) {
    return execute({ commandAlias, location: 'europe-west1', jobId : 'dashr-dbt-v1', waitForCompletion: true });
}

export default dbt;
