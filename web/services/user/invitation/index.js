import mysql from '../../database.js'
import logger from '../../logger.js'
import { cache } from '../../redis.js'
import { SESv2Client, SendEmailCommand } from '@aws-sdk/client-sesv2'
import { userInvitationEmailRender } from '../../../dist/index.js'
import { getUserByEmail } from '../../user.js'
import WorkspaceUser from '../../workspace/workspace_user.js'

export const USER_INVITATION_STATUS_REVOKED = 0
export const USER_INVITATION_STATUS_PENDING = 1
export const USER_INVITATION_STATUS_ACCEPTED = 2
export const USER_INVITATION_STATUS_REJECTED = 3

export const USER_INVITATION_STATUS_MAP = {
  [USER_INVITATION_STATUS_PENDING]: 'pending',
  [USER_INVITATION_STATUS_REVOKED]: 'revoked',
  [USER_INVITATION_STATUS_ACCEPTED]: 'accepted',
  [USER_INVITATION_STATUS_REJECTED]: 'rejected'
}

export const USER_INVITATION_STATUS_MAP_REVERSE = Object.fromEntries(
  Object.entries(USER_INVITATION_STATUS_MAP).map(([key, value]) => [value, key])
)

class UserInvitation {
  constructor (authed_user) {
    this.authed_user = authed_user
    this.user_name = null
    this.user_email = null
    this.user_role = null
    this.workspace_id = null
    this.shop_ids = null
    this.invitation_id = null
  }

  setName (name) {
    this.user_name = name
    return this
  }

  setEmail (email) {
    this.user_email = email
    return this
  }

  setRole (role) {
    this.user_role = role
    return this
  }

  setWorkspaceId (workspace_id) {
    this.workspace_id = workspace_id
    return this
  }

  setShopIds (shop_ids) {
    this.shop_ids = shop_ids
    return this
  }

  setInvitationId (invitation_id) {
    this.invitation_id = invitation_id
    return this
  }

  async #createUserInvitation () {
    if (!this.user_email || !this.user_role || !this.workspace_id) {
      logger.error('user.createUserInvitation : invalid params', {
        email: this.user_email,
        role: this.user_role,
        workspace_id: this.workspace_id
      })
      return -1
    }

    // TODO: move this to a util function
    let shop_ids_str = this.shop_ids.join(',')
    try {
      let result = await mysql.query('INSERT INTO user_invitations SET ?', {
        name: this.user_name ? this.user_name : '',
        email: this.user_email,
        role: this.user_role,
        workspace_id: String(this.workspace_id),
        shop_ids: this.shop_ids ? shop_ids_str : '',
        row_created_by: this.authed_user.uid
      })
      await UserInvitation.clearUserPendingInvitationsByEmailCache(
        this.user_email
      )
      await UserInvitation.clearAllByWorkspaceIDAndStatusCache(
        this.workspace_id,
        USER_INVITATION_STATUS_PENDING
      )
      return result.insertId
    } catch (err) {
      logger.error('user.createUserInvitation: ', err)
      return -1
    }
  }

  // Generate invite link based on user role
  async #generateInviteLink () {
    let invitation_link = 'https://app.datadrew.io/'

    // check if user already exists
    let userObj = await getUserByEmail(this.user_email)

    if (!userObj || !userObj.uid) {
      invitation_link +=
        'sign-up?email=' +
        this.user_email
    } else {
      invitation_link +=
        'sign-in?email=' +
        this.user_email
    }

    return invitation_link
  }

  async #sendEmail (emailContent) {
    const sesClient = new SESv2Client({
      region: process.env.AWS_SES_REGION,
      credentials: {
        accessKeyId: process.env.AWS_SES_ACCESS_KEY,
        secretAccessKey: process.env.AWS_SES_SECRET
      }
    })

    let htmlContent = ''
    try {
      htmlContent = await userInvitationEmailRender(emailContent)
    } catch (error) {
      logger.error(`Failed to render email: ${error.message ?? ''}`, {
        error: error.message ?? ''
      })
      return false
    }

    const params = {
      FromEmailAddress: 'Datadrew Support <<EMAIL>>',
      Destination: {
        ToAddresses: [this.user_email]
      },
      Content: {
        Simple: {
          Subject: {
            Data: "You've been invited to join Datadrew Analytics",
            Charset: 'UTF-8'
          },
          Body: {
            Html: {
              Data: htmlContent,
              Charset: 'UTF-8'
            }
          }
        }
      }
    }

    try {
      const command = new SendEmailCommand(params)
      const sendResp = await sesClient.send(command)
      logger.info(`UserInvitation->sendEmail ${this.user_email}: `, sendResp)
      return true
    } catch (error) {
      logger.error(`Failed to send email to ${this.user_email}: ${error.message ?? ''}`, {
        error: error.message ?? '',
        email: this.user_email
      })
      return false
    }
  }

  // Send email with invitation link
  async createAndSendInvite () {
    if (
      !this.user_name ||
      !this.user_email ||
      !this.user_role ||
      !this.workspace_id ||
      !this.shop_ids
    ) {
      logger.error(
        'UserInvitation->sendInvite : missing required params to send invite',
        {
          name: this.user_name,
          email: this.user_email,
          role: this.user_role,
          workspace_id: this.workspace_id,
          shop_ids: this.shop_ids
        }
      )
      return { status: false }
    }

    try {
      let workspace_user = await WorkspaceUser.getByWorkspaceIDAndEmail(
        this.workspace_id,
        this.user_email
      )
      if (workspace_user && workspace_user.id) {
        logger.error(
          'UserInvitation->sendInvite : user already present in workspace',
          {
            workspace_id: this.workspace_id,
            user_id: this.authed_user.uid
          }
        )

        return {
          status: false,
          error:
            'User already present in workspace. Please manage permissions in workspace settings.'
        }
      }

      let user_invitations =
        await UserInvitation.getUserPendingInvitationsByEmail(this.user_email)
      if (
        user_invitations &&
        user_invitations.length > 0 &&
        user_invitations.some(
          invitation => invitation.workspace_id === this.workspace_id
        )
      ) {
        logger.error(
          'UserInvitation->sendInvite : user already has a pending invitation to this workspace',
          {
            user_invitation_id: user_invitations.find(
              invitation => invitation.workspace_id === this.workspace_id
            ).id
          }
        )
        return {
          status: false,
          error: 'User already has a pending invitation to this workspace.'
        }
      }

      let user_invitation_id = await this.#createUserInvitation()
      if (!user_invitation_id || user_invitation_id === -1) {
        logger.error('inviteUser : createUserInvitation failed')
        return { status: false }
      }

      const inviteLink = await this.#generateInviteLink()

      let emailContent = {
        inviteLink: inviteLink
      }

      let emailSent = await this.#sendEmail(emailContent)

      if (!emailSent) {
        // Log the failure but keep the invitation record for potential resending
        logger.error('UserInvitation->createAndSendInvite: Email sending failed, invitation created but email not delivered', {
          user_invitation_id: user_invitation_id,
          user_email: this.user_email,
          workspace_id: this.workspace_id
        })
        return { 
          status: false, 
          error: 'Failed to send invitation email, but invitation has been created. You can resend it later.',
          invitation_id: user_invitation_id
        }
      }

      return { status: true }
    } catch (error) {
      logger.error('UserInvitation->sendInvite - Failed:', error)
      return { success: false, error: error.message || error }
    }
  }

  async #handleAcceptInvitation () {
    // TODO:
    // [Done] 1. create user if not exists, although user is already created in the sign-up flow
    // [Done] 2. check if workspace exists, if not return error since the workspace supposed to be already created
    // [Done] 3. add user to workspace users if not exists
    // 4. send acceptance email to inviter

    // Note: using authed_user.uid since the invitation is sent to the user currently logged in
    let user_id = this.authed_user.uid

    try {
      let result = await WorkspaceUser.addUserToWorkspace(
        this.workspace_id,
        user_id,
        this.invitation_obj.role,
        this.invitation_obj.shop_ids
      )

      if (!result || !result.status) {
        logger.error(
          'UserInvitation->handleAcceptInvitation : failed to add user to workspace',
          {
            workspace_id: this.workspace_id,
            user_id: user_id
          }
        )
        return false
      }

      // TODO: send acceptance email to inviter
      // let emailContent = {
      //     "inviteLink": inviteLink
      // }
    } catch (err) {
      logger.error(
        'UserInvitation->handleAcceptInvitation : failed to accept invitation',
        {
          error: err,
          invitation_id: this.invitation_id
        }
      )
      return false
    }
  }

  async #handleRejectInvitation () {
    // TODO:
    // 1. send notification email to inviter

    try {
      // TODO: send rejection email to inviter
      // let emailContent = {
      //     "inviteLink": inviteLink
      // }
    } catch (err) {
      logger.error(
        'UserInvitation->handleAcceptInvitation : failed to reject invitation',
        {
          error: err,
          invitation_id: this.invitation_id
        }
      )
      return false
    }
  }

  async #setInvitationObj () {
    this.invitation_obj = await UserInvitation.getUserInvitationByID(
      this.invitation_id
    )
  }

  async #getInvitationObj () {
    if (!this.invitation_obj) {
      this.invitation_obj = await UserInvitation.getUserInvitationByID(
        this.invitation_id
      )
    }

    return this.invitation_obj
  }

  async validateUpdateInvitationRequest (status) {
    if (
      ([USER_INVITATION_STATUS_ACCEPTED, USER_INVITATION_STATUS_REJECTED].includes(status) &&
        this.invitation_obj.email != this.authed_user.email) ||
      ([USER_INVITATION_STATUS_REVOKED].includes(status) &&
        this.authed_user.uid != this.invitation_obj.row_created_by)
    ) {
      return false
    }

    return true
  }

  /**
   * @param {string} status
   * @returns {boolean}
   */
  async updateInvitation (status) {
    if (
      !this.invitation_id ||
      !status ||
      !USER_INVITATION_STATUS_MAP_REVERSE[status.toLowerCase()]
    ) {
      logger.error(
        'UserInvitation->updateInvitation : missing / invalid required params to update invitation',
        {
          invitation_id: this.invitation_id,
          status: status
        }
      )
      return { status: false }
    }

    let newStatus = Number(
      USER_INVITATION_STATUS_MAP_REVERSE[status.toLowerCase()]
    )

    await this.#setInvitationObj()

    if (!this.validateUpdateInvitationRequest(newStatus)) {
      return {
        status: false,
        error: 'You are not authorized to update this invitation.'
      }
    }

    if (!this.invitation_obj || !this.invitation_obj.id) {
      logger.error('UserInvitation->updateInvitation : invitation not found', {
        invitation_id: this.invitation_id,
        status: status
      })
      return { status: false }
    }

    this.setWorkspaceId(this.invitation_obj.workspace_id)

    switch (newStatus) {
      case USER_INVITATION_STATUS_ACCEPTED:
        await this.#handleAcceptInvitation()
        break
      case USER_INVITATION_STATUS_REJECTED:
        await this.#handleRejectInvitation()
        break
      case USER_INVITATION_STATUS_REVOKED:
        logger.info('UserInvitation->updateInvitation : revoked invitation', {
          authed_uid: this.authed_user.uid,
          invitation_id: this.invitation_id
        })
        break
      default:
        logger.error('UserInvitation->updateInvitation : invalid status', {
          statusInt: newStatus,
          statusStr: status
        })
        return { status: false }
    }

    try {
      await mysql.query('UPDATE user_invitations SET status = ? WHERE id = ?', [
        newStatus,
        this.invitation_id
      ])
      await UserInvitation.clearUserPendingInvitationsByEmailCache(
        this.invitation_obj.email
      )
      await UserInvitation.clearUserInvitationByIDCache(this.invitation_id)
      await UserInvitation.clearAllByWorkspaceIDAndStatusCache(
        this.invitation_obj.workspace_id,
        USER_INVITATION_STATUS_PENDING
      )
      return { status: true }
    } catch (err) {
      logger.error(
        'UserInvitation->updateInvitation: failed to update invitation status',
        err
      )
      return { status: false }
    }
  }

  static #getUserPendingInvitationsByEmailCacheKey (email) {
    return `user_pending_invitations_by_email_v2_${email}`
  }

  static async clearUserPendingInvitationsByEmailCache (email) {
    return await cache.del(this.#getUserPendingInvitationsByEmailCacheKey(email))
  }

  static async getUserPendingInvitationsByEmail (email) {
    var cacheKey = this.#getUserPendingInvitationsByEmailCacheKey(email)
    var fromCache = await cache.get(cacheKey)
    if (fromCache !== null) {
      return fromCache
    }

    var invitations = []
    try {
        const query = `
            SELECT
                a.id as invitation_id,
                a.name,
                a.email,
                a.role,
                a.workspace_id,
                GROUP_CONCAT(DISTINCT d.shop_id) AS shop_ids,
                b.name as workspace_name,
                c.onboard_name as inviter_name,
                c.email as inviter_email
            FROM user_invitations a
            INNER JOIN workspaces b ON a.workspace_id = b.workspace_id
            INNER JOIN users c ON a.row_created_by = c.uid
            INNER JOIN workspace_shops d
                ON FIND_IN_SET(d.shop_id, a.shop_ids) > 0
                AND d.workspace_id = a.workspace_id
                AND d.status = 1
            WHERE a.email = ? AND a.status = ?
            GROUP BY a.id
        `
      let result = await mysql.query(query, [
        email,
        Number(USER_INVITATION_STATUS_PENDING)
      ])

      if (result.length > 0) {
        invitations = result.map(invitation => ({
          ...invitation,
          shop_ids: invitation.shop_ids ? invitation.shop_ids.split(',') : []
        }))
      }
    } catch (err) {
      logger.error('user.getUserInvitationByEmail error : ', err)
    }

    if (invitations) {
      await cache.set(cacheKey, invitations)
    }

    return invitations
  }

  static #getUserInvitationByIDCacheKey (invitation_id) {
    return `user_invitation_by_id_v1_${invitation_id}`
  }

  static async clearUserInvitationByIDCache (invitation_id) {
    return await cache.del(this.#getUserInvitationByIDCacheKey(invitation_id))
  }

  static async getUserInvitationByID (invitation_id) {
    var cacheKey = this.#getUserInvitationByIDCacheKey(invitation_id)
    var fromCache = await cache.get(cacheKey)
    if (fromCache !== null) {
      return fromCache
    }

    var invitation = {}
    try {
      let result = await mysql.query(
        'SELECT * from user_invitations where id = ?',
        [invitation_id]
      )
      invitation = result.length > 0 ? result[0] : {}
    } catch (err) {
      logger.error('user.getUserInvitationByEmail error : ', err)
    }

    if (invitation) {
      await cache.set(cacheKey, invitation)
    }

    return invitation
  }

  /**
   * @param {int} workspace_id
   * @param {int} status
   * @returns {string}
   */
  static #getAllByWorkspaceIDAndStatusCacheKey (workspace_id, status) {
    return `user_invitations_by_workspace_id_and_status_v1_${workspace_id}_${status}`
  }

  /**
   * @param {int} workspace_id
   * @param {int} status
   * @returns {boolean}
   */
  static async clearAllByWorkspaceIDAndStatusCache (workspace_id, status) {
    var cacheKey = UserInvitation.#getAllByWorkspaceIDAndStatusCacheKey(
      workspace_id,
      status
    )
    await cache.del(cacheKey)
  }

  /**
   * @param {int} workspace_id
   * @param {int} status
   * @returns {array}
   */
  static async getAllByWorkspaceIDAndStatus (workspace_id, status) {
    if (!workspace_id || !status) {
        logger.error(
          'UserInvitation->getAllByWorkspaceIDAndStatus : missing required params',
          {
            workspace_id: workspace_id,
            status: status
          }
        )
        return []
    }

    var cacheKey = UserInvitation.#getAllByWorkspaceIDAndStatusCacheKey(
      workspace_id,
      status
    )
    var fromCache = await cache.get(cacheKey)
    if (fromCache !== null) {
      return fromCache
    }

    let user_invitations = []
    try {
      user_invitations = await mysql.query(
        'SELECT * FROM user_invitations WHERE workspace_id = ? AND status = ?',
        [workspace_id, status]
      )
      if (user_invitations && user_invitations.length > 0) {
        user_invitations = user_invitations.map(ui => ({
          ...ui,
          shop_ids: ui.shop_ids ? ui.shop_ids.split(',') : []
        }))
      } else {
        user_invitations = []
      }
    } catch (err) {
      logger.error(
        'UserInvitation->getAllByWorkspaceIDAndStatus : failed to get all user invitations by workspace ID and status',
        err
      )
      return []
    }

    if (user_invitations) {
      await cache.set(cacheKey, user_invitations)
    }

    return user_invitations
  }

}

export default UserInvitation
