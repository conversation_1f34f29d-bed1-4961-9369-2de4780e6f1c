import { createRequire } from "module";
const require = createRequire(import.meta.url);
const dayjs = require("dayjs");
var relativeTime = require('dayjs/plugin/relativeTime')
dayjs.extend(relativeTime);
import errors from '../errors.js';
import mysql from "../database.js";
import shop from "../shop.js";
import logger from '../logger.js';
import { cache } from '../redis.js';
import { clearCubeCacheOnShopifySyncCompletion } from '../cube/data.js';
import {
  PLATFORM_CLOUD,
  PLATFORM_OSS,
  V1_PLATFORM_OSS,
  INTEGRATION_STATUS_DRAFT,
  INTEGRATION_STATUS_CONNECTED,
  INTEGRATION_STATUS_DISCONNECTED,
  INTEGRATION_STATUS_SYNCED,

  SOURCE_GOOGLE_ANALYTICS,
  SOURCE_FB,
  SOURCE_SHOPIFY,
  SOURCE_KLAVIYO,
  SOURCE_GOOGLE_ADS,

  VALID_OSS_SOURCE_TYPES,
} from "./constants.js";
import googleIntegration from "./googleIntegration.js";
import facebookIntegration from "./facebookIntegration.js";
import secretmanager from "../secretmanager/index.js";
import oss from './oss.js';
import crypto from "crypto";
import cloud from './cloud.js';
import automation from '../automation/index.js';
import dbt from "../dbt/index.js";
import bqRuns from '../bigquery/run.js';
import bigquery from '../bigquery/index.js';
import facebook from '../facebook/index.js';

const platformHandlers = {
    [PLATFORM_OSS]: {
        getWorkspaceId: (...args) => oss.getWorkspaceId(PLATFORM_OSS, ...args),
        getDestinationId: (...args) => oss.getDestinationId(PLATFORM_OSS, ...args),
        markConnectionInactive: (...args) => oss.markConnectionInactive(PLATFORM_OSS, ...args),
        getLatestJobs: (...args) => oss.getLatestJobs(PLATFORM_OSS, ...args),
        triggerConnectionSync: (...args) => oss.triggerConnectionSync(PLATFORM_OSS, ...args),

        handleConnect: oss.handleConnect,
        createSource: oss.createSource,
        createConnection: oss.createConnection,
    },
    [V1_PLATFORM_OSS]: {
        getWorkspaceId: (...args) => oss.getWorkspaceId(V1_PLATFORM_OSS, ...args),
        getDestinationId: (...args) => oss.getDestinationId(V1_PLATFORM_OSS, ...args),
        markConnectionInactive: (...args) => oss.markConnectionInactive(V1_PLATFORM_OSS, ...args),
        getLatestJobs: (...args) => oss.getLatestJobs(V1_PLATFORM_OSS, ...args),
        triggerConnectionSync: (...args) => oss.triggerConnectionSync(V1_PLATFORM_OSS, ...args),

        handleConnect: oss.handleConnect,
        createSource: oss.createSource,
        createConnection: oss.createConnection,
    },
    [PLATFORM_CLOUD]: {
        getWorkspaceId: cloud.getWorkspaceId,
        getDestinationId: cloud.getDestinationId,
        handleConnect: cloud.handleConnect,
        markConnectionInactive: cloud.markConnectionInactive,
        getLatestJobs: cloud.getLatestJobs,
        createSource: cloud.createSource,
        createConnection: cloud.createConnection,
        triggerConnectionSync: cloud.triggerConnectionSync
    },
};

const getPlatformHandler = (platform) => {
    const handler = platformHandlers[platform];
    if (!handler) {
        logger.error("Invalid platform", { platform });
        throw new Error("Invalid platform");
    }
    return handler;
};

const getPlatformForSourceType = function (source_type) {
    // use current VALID_OSS_SOURCE_TYPES to determine platform
    if (VALID_OSS_SOURCE_TYPES.indexOf(source_type) !== -1) {
        return V1_PLATFORM_OSS;
    } else {
        return PLATFORM_CLOUD;
    }
}

export const insert = async function (data) {
    try {
        await mysql.query("INSERT INTO airbyte_integrations SET ?", data);
        if (!!data.shop_id) {
            await cache.del(`integrations_v7_${data.shop_id}`)
        }
        return true;
    } catch (err) {
        logger.error('integrations.insert: ', err);
        return false;
    }
}

async function getIntegrationByConnectionId(connection_id) {

    let key = `integration_connection_v3_${connection_id}`
    let fromCache = await cache.get(key)
    if (!!fromCache) {
        return fromCache
    }
    
    var intg = {}
    try {
        let result = await mysql.query('SELECT * FROM airbyte_integrations WHERE airbyte_connection_id = ?', [connection_id]);
        intg = result[0]
    } catch (err) {
        logger.error('integrations.getIntegrationByConnectionId error : ', err)
    }

    if (intg && intg.request_id) {
        await cache.set(key, intg)
    }

    return intg
}

async function parseSourceConfigId(intg) {

    if (!intg || !intg.source_config) {
        return "";
    }
    
    let parsed_source_config;
    try {
        parsed_source_config = JSON.parse(intg.source_config);
    } catch (err) {
        logger.error('integrations.parseSourceConfigId: error parsing source_config', { err: err.message, intg });
        return "";
    }

    return parsed_source_config.ids?.[0] ?? "";
}


async function getIntegrationRequest(request_id) {

    let key = `integration_req_v3_${request_id}`
    let fromCache = await cache.get(key)
    if (!!fromCache) {
        return fromCache
    }
    
    var intg = {}
    try {
        let result = await mysql.query('SELECT * FROM airbyte_integrations WHERE request_id = ?', [request_id]);
        intg = result[0]
    } catch (err) {
        logger.error('integrations.getIntegrationRequest error : ', err)
    }

    if (intg && intg.request_id) {
        await cache.set(key, intg)
    }

    return intg
}

// updatePartial function receives a subset of database columns to update
export const updatePartial = async function (request_id, partial_configuration) {

    let intg = await getIntegrationRequest(request_id)
    if (!intg || !intg.request_id) {
        // request id is invalid
        return false
    }

    delete intg.row_updated_at
    delete intg.row_created_at

    let newIntg = Object.assign(intg, partial_configuration)

    try {
        await mysql.query("UPDATE airbyte_integrations SET ? WHERE request_id = ?", [newIntg, request_id]);
        await cache.del(`integrations_v7_${intg.shop_id}`)
        await cache.del(`integration_req_v3_${request_id}`)
        await cache.del(`integration_connection_v3_${intg.airbyte_connection_id}`)
        return true;
    } catch (err) {
        logger.error('integrations.updatePartial: ', err);
        return false;
    }
}

export const getIntegrationsFromDB = async (shop_id) => {

    let key = `integrations_v7_${shop_id}`
    let integrations = await cache.get(key)
    if (!!integrations) {
        return integrations
    }

    let dbIntegrations = []
    let query = `
        SELECT *
        FROM airbyte_integrations
        WHERE shop_id = ?
        AND platform IN ('airbyte_cloud', 'airbyte_oss_v1')
        AND status NOT IN (0,2)
    `;
    try {
        dbIntegrations = await mysql.query(query, [shop_id + ""]);
    } catch (err) {
        logger.error('getIntegrationsFromDB error : ', err)
    }

    if (Array.isArray(dbIntegrations) && dbIntegrations.length > 0) {
        await cache.set(key, dbIntegrations)
    }

    return dbIntegrations
}

export const getIntegrations = async function (shop_id, source_type = "") {

    let integrations = await getIntegrationsFromDB(shop_id)
    if (!integrations || integrations.length == 0) {
        return {}
    }

    let intgs = {};
    try {
        for (var k in integrations) {

            if (source_type != "" && integrations[k].source_type != source_type) {
                continue
            }

            let connected = false;
            let configured = false;
            let synced = false;


            if (integrations[k].status != INTEGRATION_STATUS_DRAFT
                && integrations[k].status != INTEGRATION_STATUS_DISCONNECTED
            ) {
                connected = true;
            }


            let parsed_source_config;
            try {
                parsed_source_config = JSON.parse(integrations[k].source_config);
            } catch (error) {
                logger.error('Error parsing source_config:', error);
                parsed_source_config = {};
            }

            configured = connected && !!parsed_source_config && 
                            Object.keys(parsed_source_config).length > 0;

            if (configured && integrations[k].status == INTEGRATION_STATUS_SYNCED) {
                synced = true;
            }

            let source_intg_item = {
                shop_id: integrations[k].shop_id,
                auto_id: integrations[k].auto_id,
                request_id : integrations[k].request_id,
                source_type: integrations[k].source_type,
                status: integrations[k].status,
                source_id : integrations[k].airbyte_source_id,
                connection_id : integrations[k].airbyte_connection_id,
                klaviyo_api_key: integrations[k].klaviyo_api_key,
                connected : connected, 
                configured : configured,
                synced : synced,
                source_config_id: parsed_source_config.ids?.[0] ?? "",
                source_config_name: parsed_source_config.name ?? "",
                token_id: integrations[k].token_id,
                workspace_id: integrations[k].airbyte_workspace_id,
                platform: integrations[k].platform,
                destination_id: integrations[k].airbyte_destination_id,
                created_at_display : dayjs(integrations[k].row_created_at).format('MMMM D, YYYY')
            }

            if (integrations[k].source_type in intgs) {
                intgs[integrations[k].source_type].push(source_intg_item);
            } else {
                intgs[integrations[k].source_type] = [source_intg_item];
            }
        }
        return intgs;
    } catch (err) {
        logger.error('integrations.get: ', err);
        return {};
    }
}

export const createNewSource = async function (request_id) {

    let intg = await getIntegrationRequest(request_id)
    if (!intg || !intg.request_id) {
        logger.error("createNewSource: invalid request_id", { request_id });
        return false
    }

    if (!!intg.airbyte_source_id) {
        logger.error("createNewSource: source exists already", { request_id });
        return true
    }

    const handler = getPlatformHandler(intg.platform);
    let response = await handler.createSource(intg)

    if (!response.source_id) {
        logger.error("createNewSource: failed", { request_id });
        return false
    }

    return await updatePartial(request_id, {airbyte_source_id : response.source_id});
}

export const triggerSync = async function (connection_id, platform) {
    
    if (!connection_id) {
        return false
    }

    const handler = getPlatformHandler(platform);
    return await handler.triggerConnectionSync(connection_id);
}

export const getJobStatus = async (request_id) => {
    if (!request_id) {
        logger.error("getJobStatus: request_id not present", { request_id });
        return {poll : false, error: errors.client.invalidInput};
    }

    let intg = await getIntegrationRequest(request_id);
    if (!intg || !intg.request_id) {
        logger.error("getJobStatus invalid request_id", { request_id });
        return {poll : false, error: errors.client.invalidInput};
    }

    if (!intg.airbyte_connection_id) {
        return {
            status: "setting-conn",
            poll : true
        };
    }

    const handler = getPlatformHandler(intg.platform);
    let jobs = await handler.getLatestJobs(intg.airbyte_connection_id);
    if (!jobs || jobs.length === 0) {
        return {poll : false, error: errors.client.generic};
    }

    const job = jobs[0];
    if (!job.jobId) {
        logger.error("getJobStatus invalid job_id", { job_id: job.jobId });
        return {poll : false, error: errors.client.generic};
    }


    let shouldPoll = job.status === "pending" || job.status === "running";
    return {
        job_id: job.jobId,
        poll : shouldPoll,
        status: job.status,
        job_type: job.jobType,
        start_time: job.startTime,
        start_time_display: dayjs(job.startTime).fromNow(),
    };
}

export const createNewConnection = async function (request_id) {

    let intg = await getIntegrationRequest(request_id)
    if (!intg || !intg.request_id) {
        logger.error("createNewConnection: invalid request_id", { request_id });
        return false
    }

    if (!intg.source_type || !intg.airbyte_source_id || !intg.airbyte_destination_id) {
        logger.error("createNewConnection: missing source or destination", { request_id });
        return false
    }

    if (!!intg.airbyte_connection_id) {
        logger.error("createNewConnection: connection exists already", { request_id });
        return true
    }

    const handler = getPlatformHandler(intg.platform);
    let response = await handler.createConnection(intg)
    if (response.connection_id) {
        await handler.triggerConnectionSync(response.connection_id)
        return await updatePartial(request_id, {
            airbyte_connection_id : response.connection_id
        })
    }

    logger.error("createNewConnection failed", { request_id, response });
    return false;
}

// configureSource will accept multiple options for a source and create a new source and connection for each option
const configureSource = async function (request_id, selected_options) {

    if (!selected_options || selected_options.length === 0) {
        logger.error('integrations.configureSource: invalid selected_options', { request_id });
        return { error: true };
    }

    const intg = await getIntegrationRequest(request_id);
    if (!intg || !intg.request_id) {
        logger.error('integrations.configureSource: invalid request_id', { request_id });
        return { error: true };
    }

    try {

        // Trim all ids in selected_options
        selected_options = selected_options.map(option => ({
            ...option,
            id: option.id.trim()
        }));

        const firstOption = selected_options[0];

        const optionsSaved = await updatePartial(request_id, {
            source_config: JSON.stringify({
                name: firstOption.name,
                ids: [firstOption.id]
            })
        });

        if (!optionsSaved) {
            return { error: true };
        }

        const requestIds = [request_id];

        if (selected_options.length > 1) {
            const insertionPromises = selected_options.slice(1).map(async (option) => {
                const new_request_id = crypto.randomUUID();
                const newData = {
                    shop_id : intg.shop_id,
                    request_id: new_request_id,
                    source_type: intg.source_type,
                    airbyte_workspace_id: intg.airbyte_workspace_id,
                    platform: intg.platform,
                    airbyte_destination_id: intg.airbyte_destination_id,
                    token_id: intg.token_id,
                    status: intg.status,
                    source_config: JSON.stringify({
                        name: option.name,
                        ids: [option.id]
                    })
                };
                const insertResult = await insert(newData);
                if (insertResult) {
                    requestIds.push(new_request_id);
                }
                return insertResult;
            });

            const insertionResults = await Promise.all(insertionPromises);
            const allInsertionsSuccessful = insertionResults.every(result => result);

            if (!allInsertionsSuccessful) {
                logger.error('integrations.configureSource: failed to insert additional sources', { request_id });
                return { error: true };
            }
        }

        // create sources and connections via worker
        await shop.pushAsyncTask({
            action: "setupDataPipeline",
            requestIds: requestIds
        });

        return { success : true };
    } catch (error) {
        logger.error('integrations.configureSource :', { message: error.message ?? "", request_id });
        return { error: true };
    }
};

const setupDataPipeline = async function ({requestIds}) {
    if (!requestIds || requestIds.length === 0) {
        logger.error('integrations.setupDataPipeline: invalid request', { requestIds });
        return false;
    }

    const updatePromises = requestIds.map(async (request_id) => {
        try {

            const intg = await getIntegrationRequest(request_id);
            if (!intg || !intg.request_id) {
                logger.error('integrations.setupDataPipeline: invalid request_id', { request_id });
                return false;
            }

            if (!intg.airbyte_source_id) {
                const sourceCreated = await createNewSource(request_id);
                if (!sourceCreated) {
                    logger.error('integrations.setupDataPipeline: failed to create source', { request_id });
                    return false;
                }
            }

            if (!intg.airbyte_connection_id) {
                const connectionCreated = await createNewConnection(request_id);
                if (!connectionCreated) {
                    logger.error('integrations.setupDataPipeline: failed to create connection', { request_id });
                    return false;
                }
            }
            return true;
        } catch (error) {
            logger.error('integrations.setupDataPipeline: error',  { message: error.message ?? "", request_id });
            return false;
        }
    });

    const updateResults = await Promise.all(updatePromises);
    const allUpdatesSuccessful = updateResults.every(result => result);

    return allUpdatesSuccessful;
}

const getConfigureOptions = async function (request_id) {
    try {
        const intg = await getIntegrationRequest(request_id);
        if (!intg || !intg.request_id) {
            logger.error('integrations.getConfigureOptions: invalid request_id', { request_id });
            return {error: 'Invalid request'};
        }

        // sending empty data - users are expected to manually input data for cloud integrations
        if (intg.platform == PLATFORM_CLOUD) {
            return {
                data: []
            };
        }

        const credentials = await secretmanager.getCredentials(intg.token_id);
        if (credentials.error) {
            logger.error('integrations.getConfigureOptions: failed to retrieve credentials', { request_id, error: credentials.error });
            return { error: 'Failed to retrieve credentials' };
        }

        if (intg.source_type == SOURCE_GOOGLE_ANALYTICS || intg.source_type == SOURCE_GOOGLE_ADS) {
            const googleAccountOptions = await googleIntegration.getGoogleData(credentials);
            if (googleAccountOptions.error) {
                logger.error('integrations.getConfigureOptions: failed to retrieve Google account options', { request_id, error: googleAccountOptions.error });
                return {error: 'Failed to retrieve data'};
            }

            if (googleAccountOptions.data && googleAccountOptions.data.length === 0) {
                logger.error('integrations.getConfigureOptions: no Google account options found', { request_id });
                return {error: 'No account options found. Please reach out to support.'};
            }

            return {data: googleAccountOptions.data ?? []};
        } else if (intg.source_type == SOURCE_FB) {
            const facebookAdAccounts = await facebookIntegration.fetchFacebookAdAccounts(credentials.access_token);
            if (facebookAdAccounts.error) {
                logger.error('integrations.getConfigureOptions: failed to retrieve Facebook ad accounts', { request_id, error: facebookAdAccounts.error });
                return { error: 'Failed to retrieve data' };
            }

            return { data: facebookAdAccounts.data ?? [] };
        }

        // return empty data for non-Google integrations
        return {data: []};
    } catch (error) {
        logger.error('integrations.getConfigureOptions :', { request_id, message: error.message ?? ""});
        return {error: 'Failed to retrieve data'};
    }
}

const setupKlaviyoConnection = async function (shop_id, klaviyo_api_key, platform) {
    let request_id = crypto.randomUUID();


    const handler= getPlatformHandler(platform);

    let done = await insert({
        shop_id,
        source_type : SOURCE_KLAVIYO,
        klaviyo_api_key: klaviyo_api_key,
        status : INTEGRATION_STATUS_CONNECTED,
        request_id,
        platform,
        airbyte_workspace_id: handler.getWorkspaceId(),
        airbyte_destination_id : handler.getDestinationId(SOURCE_KLAVIYO)
    });

    if (!done) {
        return false
    }

    // Disabling klaviyo connection setup since not needed right now
    // await shop.pushAsyncTask({
    //     action: "setupDataPipeline",
    //     requestIds: [request_id]
    // });

    return true;
}

const setupShopifyConnection = async function (shop_id, platform) {
    let request_id = crypto.randomUUID();

    const handler = getPlatformHandler(platform);

    let done = await insert({
        shop_id,
        source_type : SOURCE_SHOPIFY,
        status : INTEGRATION_STATUS_CONNECTED,
        request_id,
        platform,
        airbyte_workspace_id: handler.getWorkspaceId(),
        airbyte_destination_id : handler.getDestinationId(SOURCE_SHOPIFY)
    });

    if (!done) {
        return false
    }

    await shop.pushAsyncTask({
        action: "setupDataPipeline",
        requestIds: [request_id]
    });

    return true;
}

// handleConnect will create a new integration request
// and tries to create a oauth url for the source_type
const handleConnect = async function (shop_id, source_type) {

    let request_id = crypto.randomUUID();

    let platform = getPlatformForSourceType(source_type);

    const handler = getPlatformHandler(platform);

    let req_data = {
        shop_id,
        source_type,
        status: INTEGRATION_STATUS_DRAFT,
        request_id,
        platform,
        airbyte_workspace_id: handler.getWorkspaceId(),
        airbyte_destination_id : handler.getDestinationId(source_type)
    }

    let done = await insert(req_data);
    if (!done) {
        logger.error("handleConnect failed to insert", { request_id });
        return {error : errors.client.generic};
    }

    return await handler.handleConnect(source_type, request_id, shop_id);
}

const disconnectAllIntegrations = async function (shop_id) {
    let integrations = await getIntegrationsFromDB(shop_id);
    if (!integrations || integrations.length === 0) {
        logger.info(`No integrations found for shop_id: ${shop_id}`);
        return true;
    }

    logger.info(`Disconnecting ${integrations.length} integrations for shop_id: ${shop_id}`);

    try {
        const disconnectionPromises = integrations.map(integration => handleDisconnect(integration.request_id));
        const results = await Promise.all(disconnectionPromises);

        // Identify which integrations failed to disconnect
        const failedIntegrations = results
            .map((result, index) => !result ? integrations[index].request_id : null)
            .filter(Boolean);
            
        if (failedIntegrations.length > 0) {
            logger.error(`Failed to disconnect integrations for shop_id: ${shop_id}. Failed request_ids: ${failedIntegrations.join(', ')}`);
            return false;
        }

        return true;
    } catch (error) {
        logger.error(`Error disconnecting integrations for shop_id: ${shop_id}:`, error);
        return false;
    }
}

// used for migration from airbyte_oss to airbyte_oss_v1
const cloneIntegrationToPlatform = async function (request_id, platform) {
    const intg = await getIntegrationRequest(request_id);
    if (!intg || !intg.request_id) {
        logger.error('cloneIntegration: invalid request_id', { request_id });
        return false;
    }

    const new_request_id = crypto.randomUUID();
    const handler = getPlatformHandler(platform);

    const newData = {
        shop_id : intg.shop_id,
        request_id: new_request_id,
        source_type: intg.source_type,
        airbyte_workspace_id: handler.getWorkspaceId(),
        platform: platform,
        airbyte_destination_id: handler.getDestinationId(intg.source_type),
        secret_id: intg.secret_id,
        token_id: intg.token_id,
        status: INTEGRATION_STATUS_CONNECTED,
        source_config: intg.source_config,
    };

    const insertResult = await insert(newData);

    if (!insertResult) {
        logger.error('cloneIntegration: failed to insert new integration', { request_id });
        return false;
    }

    let done = await shop.pushAsyncTask({
        action: "setupDataPipeline",
        requestIds: [new_request_id]
    });

    return done;
}

const markShopAsReadyForMigration = async function (shop_id) {
    let shopObj = await shop.getShopById(shop_id);
    if (!shopObj || !shopObj.shop_id) {
        logger.error('handleConnectionWebhook: shop not found', { shop_id });
        return false
    }

    if (shopObj.is_migration_ready) {
        return true;
    }

    await shop.updatePartial(shopObj.shop_id, { is_migration_ready: 1 });
    return true;
}

const runShopifyTransformation = async function (request_id, connection_id) {
    let bqRunDone = await bqRuns.runDatadrewEUSync() // sync datadrew.shops first
    if (!bqRunDone) {
        logger.error('handleConnectionWebhook: bqRuns.runDatadrewEUSync failed', { request_id, connection_id });
        return false
    }

    let response = await dbt("shopify")
    return response
}

const handleConnectionWebhook = async function (connection_id, success) {

    if (!connection_id) {
        logger.error('handleConnectionWebhook: connection_id not found in payload data');
        return false
    }

    if (!success) {
        logger.error('handleConnectionWebhook: Sync was not successful', { connection_id });
        return false;
    }

    const intg =  await getIntegrationByConnectionId(connection_id)
    if (!intg || !intg.request_id) {
        logger.error('handleConnectionWebhook: Connection not found', { connection_id });
        return false;
    }


    if (intg.status == INTEGRATION_STATUS_DISCONNECTED) {
        logger.warn('handleConnectionWebhook: disconnected integration', { connection_id });
        return true;
    }

    if (intg.source_type === SOURCE_SHOPIFY) {
        await markShopAsReadyForMigration(intg.shop_id);
        await clearCubeCacheOnShopifySyncCompletion(intg.shop_id);
    }

    // For the first successful sync, we need to trigger the transformation
    // If the status is already synced, we can skip the transformation
    if (intg.status == INTEGRATION_STATUS_SYNCED) {
        logger.info('handleConnectionWebhook: Already synced', { connection_id });
        return true;
    }

    let response = {};
    if (intg.platform === PLATFORM_CLOUD && intg.source_type == SOURCE_FB) {
        response = await dbt("meta_legacy")
    } else if (intg.platform === PLATFORM_OSS || intg.platform === V1_PLATFORM_OSS) {
        switch (intg.source_type) {
            case SOURCE_FB:
                response = await dbt("meta")
                break;
            case SOURCE_GOOGLE_ADS:
                response = await dbt("google_ads")
                break;
            case SOURCE_GOOGLE_ANALYTICS:
                response = await dbt("google_analytics")
                break;
            case SOURCE_SHOPIFY:
                response = await runShopifyTransformation(intg.request_id, connection_id)
                break;
            default:
                logger.warn('handleConnectionWebhook: No matching transformation found', { request_id: intg.request_id, connection_id });
                break;
        }
    }

    if (Object.keys(response).length === 0) {
        logger.warn("handleConnectionWebhook: No matching transformation found", { connection_id, request_id: intg.request_id });
    }

    if (!!response.error) {
        logger.error('handleConnectionWebhook: transformation failed', { connection_id , request_id: intg.request_id, error : response.error });
        return false
    }

    let done = await updatePartial(intg.request_id, {
        status : INTEGRATION_STATUS_SYNCED
    });

    if (!done) {
        logger.error('handleConnectionWebhook: failed to set status to synced', { request_id: intg.request_id });
        return false
    }

    return done
}

const handleDisconnect = async function (request_id) {
    let intg = await getIntegrationRequest(request_id)
    if (!intg || !intg.request_id) {
        logger.error("handleDisconnect: invalid request_id", { request_id });
        return false;
    }

    if (intg.status == INTEGRATION_STATUS_DISCONNECTED) {
        logger.info("handleDisconnect: Already disconnected", { request_id });
        return true;
    }

    // Mark connection as inactive in airbyte
    if (!!intg.airbyte_connection_id) {
        let markedInactive = false;
        const handler = getPlatformHandler(intg.platform);
        markedInactive = await handler.markConnectionInactive(intg.airbyte_connection_id);
    
        if (!markedInactive) {
            logger.error("handleDisconnect: Error disconnecting connection", { request_id, connection_id: intg.airbyte_connection_id });
            return false
        }
    }

    let done = await updatePartial(request_id, {status : INTEGRATION_STATUS_DISCONNECTED})
    if (!done) {
        logger.error("handleDisconnect: Error disconnecting integration", { request_id });
        return false
    }

    return true
}

export default {
    insert,
    getJobStatus,
    handleDisconnect,
    disconnectAllIntegrations,
    getIntegrations,
    createNewSource,
    createNewConnection,
    updatePartial,
    getIntegrationRequest,
    getIntegrationsFromDB,
    configureSource,
    getConfigureOptions,
    setupDataPipeline,
    handleConnect,
    setupKlaviyoConnection,
    setupShopifyConnection,
    parseSourceConfigId,
    handleConnectionWebhook,
    cloneIntegrationToPlatform
}