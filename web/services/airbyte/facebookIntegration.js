import secretmanager from '../secretmanager/index.js';
import logger from '../logger.js';
import axios from 'axios';
import { constructRedirectUrl } from './util.js';
import { INTEGRATION_STATUS_CONNECTED } from './constants.js';
import integrations from './integrations.js';
import shop from '../shop.js';

const FB_API_OAUTH = `https://www.facebook.com/v23.0/dialog/oauth`;

const FB_API_BASE = 'https://graph.facebook.com/v23.0';
const FB_API_ACCESS_TOKEN = `${FB_API_BASE}/oauth/access_token`;
const FB_API_AD_ACCOUNTS = `${FB_API_BASE}/me/adaccounts`;
const FB_API_BUSINESSES = `${FB_API_BASE}/me/businesses`;
const FB_API_ACCOUNTS = `${FB_API_BASE}/me/accounts`; // fetches associated pages

const FB_REDIRECT_URI = `${process.env.HOST}/api/auth/facebook/callback`;

function generateFacebookAuthUrl(source_type, request_id, shop_id) {
    const clientId = process.env.FB_APP_ID;

    const scopes = [
        'ads_read',
        'read_insights',
        'ads_management',
        'business_management',
        'pages_read_engagement',
        'pages_show_list'
    ].join(',');

    // Using new config with only 3 scopes for Cava - ads_read, read_insights, business_management - on experiment basis
    const configId = shop_id == "***********" ? process.env.FB_CONFIG_ID_CAVA : process.env.FB_CONFIG_ID;

    const authUrl = `${FB_API_OAUTH}?` +
        `client_id=${clientId}` +
        `&config_id=${configId}` +
        `&redirect_uri=${encodeURIComponent(FB_REDIRECT_URI)}` +
        `&state=${JSON.stringify({
            request_id,
            source_type,
            shop_id
        })}` +
    // `&scope=${scopes}` +
        `&response_type=code`;

    return { authUrl };
}

async function debugFacebookToken(inputToken) {
    // The app access token is in the format: {app-id}|{app-secret}
    const appAccessToken = `${process.env.FB_APP_ID}|${process.env.FB_APP_SECRET}`;
    const url = `${FB_API_BASE}/debug_token`;

    try {
        const response = await axios.get(url, {
            params: {
                input_token: inputToken,
                access_token: appAccessToken
            }
        });

        return response.data?.data ?? {};
    } catch (error) {
        logger.error('debugFacebookToken Error:', error.response?.data || error.message);
        return null;
    }
}

async function handleOAuthCallback(state, code) {
    try {
        const { request_id, source_type, shop_id } = JSON.parse(state);
        const tokenRes = await axios.get(FB_API_ACCESS_TOKEN, {
            params: {
                client_id: process.env.FB_APP_ID,
                client_secret: process.env.FB_APP_SECRET,
                redirect_uri: FB_REDIRECT_URI,
                code
            }
        });

        const shortLivedToken = tokenRes.data.access_token;
        const longTokenRes = await axios.get(FB_API_ACCESS_TOKEN, {
            params: {
                grant_type: 'fb_exchange_token',
                client_id: process.env.FB_APP_ID,
                client_secret: process.env.FB_APP_SECRET,
                fb_exchange_token: shortLivedToken
            }
        });

        const longLivedToken = longTokenRes.data.access_token;

        const debugTokenRes = await debugFacebookToken(longLivedToken);

        const optionalData = {
            expiry_date: debugTokenRes.data_access_expires_at ?? 0,
            metadata: {
                token_expires_at: debugTokenRes.expires_at ?? '',
                data_access_expires_at: debugTokenRes.data_access_expires_at ?? ''
            }
        };

        const response = await secretmanager.saveCredentials(shop_id, source_type, longLivedToken, optionalData);
        if (!response.success) {
            logger.error('Failed to save credentials', { shop_id, source_type });
            return { error: 'Failed to save credentials', redirect_url: constructRedirectUrl(source_type, request_id, true) };
        }

        const payload = {
            action: "saveTokenMetadata",
            request_id: request_id,
            token_id: response.token_id,
        };

        await shop.pushAsyncTask(payload);

        const data = {
            token_id: response.token_id,
            status: INTEGRATION_STATUS_CONNECTED,
        };

        const done = await integrations.updatePartial(request_id, data);
        if (!done) {
            logger.error('Failed to update integration', { request_id });
            return { error: 'Failed to update integration', redirect_url: constructRedirectUrl(source_type, request_id, true) };
        }

        return { redirect_url: constructRedirectUrl(source_type, request_id, false) };
    } catch (err) {
        logger.error('handleOAuthCallback - OAuth callback error:', {
            message: err.message,
            responseData: err?.response?.data,
            responseStatus: err?.response?.status,
            stack: err.stack
        });

        return { error: 'Facebook auth failed', redirect_url: constructRedirectUrl(source_type, request_id, true) };
    }
}

async function fetchFacebookAdAccounts(accessToken) {
    try {
        const response = await axios.get(FB_API_AD_ACCOUNTS, {
            params: {
                access_token: accessToken,
                fields: 'id,name,account_status,owner,disable_reason,created_time,timezone_name'
            }
        });

        const businesses = await fetchFacebookBusinesses(accessToken);
        const businessData = businesses.data ?? [];

        const rawAccounts = response.data?.data || [];

        const adAccounts = rawAccounts.map(account => ({
            ...account,
            act_id: account.id,
            id: account.id.replace(/^act_/, ''),
            owner_info: {
                "name": businessData.find(business => business.id === account.owner)?.name,
                "id": businessData.find(business => business.id === account.owner)?.id
            }
        }));

        // Replace the original data array with the modified one
        const processedResponse = {
            ...response.data,
            data: adAccounts
        };

        return processedResponse;
    } catch (error) {
        logger.error('fetchFacebookAdAccounts - Error fetching ad accounts:', error.response?.data || error.message);
        return [];
    }
}

async function fetchFacebookBusinesses(accessToken) {
    try {
        const response = await axios.get(FB_API_BUSINESSES, {
            params: {
                access_token: accessToken,
                fields: 'id,name,created_time,permitted_roles'
            }
        });

        return response.data;
    } catch (error) {
        logger.error('fetchFacebookBusinesses - Error fetching business accounts:', error.response?.data || error.message);
        return [];
    }
}

async function getCampaignsInfo(accessToken, adAccountId) {
    const url = `${FB_API_BASE}/act_${adAccountId}/campaigns`;
    try {
        const response = await axios.get(url, {
            params: {
                access_token: accessToken,
                fields: 'id,name,status,effective_status',
                limit: 500
            }
        });

        return response.data?.data || [];
    } catch (error) {
        logger.error('getCampaignsInfo - Error fetching campaign statuses:', error.response?.data || error.message);
        return [];
    }
}

async function updateCampaign(access_token, campaign_id, new_status) {
    if (!campaign_id || !access_token || !['ACTIVE', 'PAUSED'].includes(new_status)) {
        return { error: true, message: 'Invalid campaign_id, access_token or new_status' };
    }

    try {
        const { data } = await axios.post(
            `${FB_API_BASE}/${campaign_id}`,
            null,
            {
                baseURL: FB_API_BASE,
                params: {
                    'access_token': access_token,
                    'status': new_status, // 'PAUSED' or 'ACTIVE'
                },
            }
        );
        return data;
    } catch (err) {
        logger.error('facebook.updateCampaign error : ', err);
        return { error: true, message: err.message };
    }
}

async function fetchFacebookPages(accessToken) {
    try {
        const response = await axios.get(FB_API_ACCOUNTS, {
            params: {
                access_token: accessToken,
                fields: 'id,name,category,category_list,access_token,fan_count,account_id'
            }
        });

        const pages = response.data?.data || [];

        // Create all API calls for all pages at once (truly concurrent)
        const allApiCalls = pages.flatMap(page => {
            const pageAccessToken = page.access_token;
            const pageId = page.id;

            return [
                // Profile info call
                getPageProfileInfo(pageId, pageAccessToken)
                    .then(profileInfo => ({ pageId, type: 'profile', data: profileInfo }))
                    .catch(error => {
                        logger.error(`Error fetching profile for page ${pageId}:`, error.message);
                        return { pageId, type: 'profile', data: null };
                    }),
                // Insights call
                getPageInsights(pageId, pageAccessToken)
                    .then(insights => ({ pageId, type: 'insights', data: insights }))
                    .catch(error => {
                        logger.error(`Error fetching insights for page ${pageId}:`, error.message);
                        return { pageId, type: 'insights', data: [] };
                    })
            ];
        });

        // Execute all API calls concurrently
        const allResults = await Promise.all(allApiCalls);

        // Group results by page and combine data
        const pagesWithData = pages.map(page => {
            const profileResult = allResults.find(result => result.pageId === page.id && result.type === 'profile');
            const insightsResult = allResults.find(result => result.pageId === page.id && result.type === 'insights');

            return {
                ...page,
                profile_info: profileResult?.data || null,
                insights: insightsResult?.data || []
            };
        });

        return pagesWithData;
    } catch (error) {
        logger.error('fetchFacebookPages - Error fetching pages:', error.response?.data || error.message);
        return [];
    }
}

async function getPageProfileInfo(pageId, pageAccessToken) {
    const url = `${FB_API_BASE}/${pageId}`;
    const fields = 'id,name,about,category,fan_count,followers_count,picture,cover,link,description';

    try {
        const response = await axios.get(url, {
            params: {
                access_token: pageAccessToken,
                fields
            }
        });

        return response.data;
    } catch (error) {
        logger.error('Error fetching page profile info:', error.response?.data || error.message);
        return null;
    }
}

async function getPageInsights(pageId, pageAccessToken) {
    const url = `${FB_API_BASE}/${pageId}/insights`;

    try {
        const response = await axios.get(url, {
            params: {
                access_token: pageAccessToken,
                metric: 'page_impressions'
            }
        });

        return response.data.data || [];
    } catch (error) {
        logger.error('Error fetching page insights:', error.response?.data || error.message);
        return [];
    }
}

export default {
    generateFacebookAuthUrl,
    handleOAuthCallback,
    fetchFacebookAdAccounts,
    fetchFacebookBusinesses,
    updateCampaign,
    getCampaignsInfo,
    fetchFacebookPages,
    getPageProfileInfo,
    getPageInsights
}
