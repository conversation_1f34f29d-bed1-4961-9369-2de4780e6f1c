import { createRequire } from "module";
const require = createRequire(import.meta.url);
const axios = require('axios');
import logger from '../logger.js';
const dayjs = require("dayjs");
var relativeTime = require('dayjs/plugin/relativeTime')
dayjs.extend(relativeTime);
import shop from "../shop.js";
import {
  SOURCE_SHOPIFY,
  SOURCE_GOOGLE_ANALYTICS,
  SOURCE_KLAVIYO,
  SOURCE_GOOGLE_ADS,
  SOURCE_FB,

  OSS_AIRBYTE_WORKSPACE_ID,
  OSS_API_URL,
  OSS_SHOPIFY_DESTINATION_ID,
  OSS_KLAVIYO_DESTINATION_ID,
  OSS_GOOGLE_ANALYTICS_DESTINATION_ID,
  OSS_GOOGLE_ADS_DESTINATION_ID,

  V1_OSS_AIRBYTE_WORKSPACE_ID,
  V1_OSS_API_URL,
  V1_OSS_SHOPIFY_DESTINATION_ID,
  V1_OSS_KLAVIYO_DESTINATION_ID,
  V1_OSS_GOOGLE_ANALYTICS_DESTINATION_ID,
  V1_OSS_GOOGLE_ADS_DESTINATION_ID,
  V1_OSS_FB_DESTINATION_ID,

  CONNECTION_CONFIG_SHOPIFY_DEFAULT,
  CONNECTION_CONFIG_GOOGLE_ANALYTICS_DEFAULT,
  CONNECTION_CONFIG_GOOGLE_ADS_DEFAULT,
  CONNECTION_CONFIG_KLAVIYO_DEFAULT,
  CONNECTION_CONFIG_FB_DEFAULT,
  V1_PLATFORM_OSS,
  PLATFORM_OSS
} from "./constants.js";
import googleIntegration from "./googleIntegration.js";
import facebookIntegration from "./facebookIntegration.js";
import secretmanager from "../secretmanager/index.js";
import TokenService from '../token.js';
import { getCachedIapToken } from "../gcp/iap.js";
import { logCurlFromApiInterceptorConfig } from "../util.js";


const getWorkspaceId = (platform) => {
    if (platform === V1_PLATFORM_OSS) {
        return V1_OSS_AIRBYTE_WORKSPACE_ID;
    } else if (platform === PLATFORM_OSS) {
        return OSS_AIRBYTE_WORKSPACE_ID;
    }
}

const getDestinationId = (platform, source_type) => {

    const destinationIds = {
        [PLATFORM_OSS]: {
            [SOURCE_SHOPIFY]: OSS_SHOPIFY_DESTINATION_ID,
            [SOURCE_KLAVIYO]: OSS_KLAVIYO_DESTINATION_ID,
            [SOURCE_GOOGLE_ANALYTICS]: OSS_GOOGLE_ANALYTICS_DESTINATION_ID,
            [SOURCE_GOOGLE_ADS]: OSS_GOOGLE_ADS_DESTINATION_ID
        },
        [V1_PLATFORM_OSS]: {
            [SOURCE_SHOPIFY]: V1_OSS_SHOPIFY_DESTINATION_ID,
            [SOURCE_KLAVIYO]: V1_OSS_KLAVIYO_DESTINATION_ID,
            [SOURCE_GOOGLE_ANALYTICS]: V1_OSS_GOOGLE_ANALYTICS_DESTINATION_ID,
            [SOURCE_GOOGLE_ADS]: V1_OSS_GOOGLE_ADS_DESTINATION_ID,
            [SOURCE_FB]: V1_OSS_FB_DESTINATION_ID
        }
    }

    if (!(platform in destinationIds)) {
        logger.error("oss.getDestinationId Invalid OSS platform", {platform});
        return null;
    }

    if (!(source_type in destinationIds[platform])) {
        logger.error("oss.getDestinationId Invalid OSS source type", {source_type});
        return null;
    }

    return destinationIds[platform][source_type];
}

// decides the destination dataset for a source_type
const getDestinationDataset = (platform, source_type, auto_id) => {

    let datasets = {
        [PLATFORM_OSS]: {
            [SOURCE_SHOPIFY]: `airbyte_kb_shopify`,
            [SOURCE_GOOGLE_ANALYTICS]: `zz_ga_${auto_id}`,
            [SOURCE_GOOGLE_ADS]: `zz_google_ads_${auto_id}`,
            [SOURCE_KLAVIYO]: `zz_klaviyo_${auto_id}`
        },
        [V1_PLATFORM_OSS]: {
            [SOURCE_SHOPIFY]: `zz_shopify_${auto_id}`,
            [SOURCE_GOOGLE_ANALYTICS]: `zz_ga_${auto_id}`,
            [SOURCE_GOOGLE_ADS]: `zz_google_ads_${auto_id}`,
            [SOURCE_KLAVIYO]: `zz_klaviyo_${auto_id}`,
            [SOURCE_FB]: `zz_meta_${auto_id}`
        }
    }

    if (!(platform in datasets)) {
        logger.error("oss.getDestinationDataset Invalid OSS platform", {platform});
        return null;
    }

    if (!(source_type in datasets[platform])) {
        logger.error("oss.getDestinationDataset Invalid OSS source type", {source_type});
        return null
    }

    return datasets[platform][source_type];
}

// Create an Axios instance with interceptors
const apiClient = axios.create({
    headers: {
        'Content-Type': 'application/json',
    },
});

apiClient.interceptors.request.use(async (config) => {

    if (config.platform === V1_PLATFORM_OSS) {
        config.baseURL = V1_OSS_API_URL;
    } else { // default to PLATFORM_OSS
        config.baseURL = OSS_API_URL;
    }

    const endpoint = config.url.replace(config.baseURL, '');
    const token = await getCachedIapToken(config.platform, `${config.baseURL}${endpoint}`);
    config.headers['Authorization'] = `Bearer ${token}`;

    // Check if cURL logging is enabled in the configuration
    if (!!config.logCurl) logCurlFromApiInterceptorConfig(config);

    return config;
}, (error) => {
    return Promise.reject(error);
});

// add a response interceptor to log error responses in detail
apiClient.interceptors.response.use((response) => {
    return response;
}, (error) => {
    if (error.response && error.response.data) {
        logger.error('API error response:', {
            status: error.response.status,
            headers: error.response.headers,
            data: error.response.data,
        });
    } else {
        logger.error('API error:', { error: error.message });
    }
    return Promise.reject(error);
});

// Function to test connection
async function testConnection(platform) {
    try {

        // const response = await apiClient.patch('/workspaces/60a37fe6-46e9-48c2-9bdf-f11eee3a69b2', {
        //     name: "Production Workspace"
        // });

        const response = await apiClient.get('/workspaces', {
            params: {
                includeDeleted: false,
                limit: 20,
                offset: 0,
            },
            logCurl: false,
            platform,
        });
        logger.info('Connection test response:', {data : response.data});
    } catch (err) {
        // Check if the error response has a data property with detailed information
        if (err.response && err.response.data) {
            // Log the detailed error message from the server response
            logger.error('Error testing connection:', {
                error: err.message,
                response: {
                    status: err.response.status,
                    headers: err.response.headers,
                    data: err.response.data,
                },
            });
        } else {
            // Fallback to logging the error without detailed response data
            logger.error('Error testing connection:', { error: err });
        }
    }
}

const getLatestJobs = async function (platform, connection_id, status = '', limit = 1) {

    let jobs = [];
    try {

        let params = {
            connectionId: connection_id,
            limit: limit,
            offset: '0',
            orderBy: 'createdAt|DESC'
        }

        if (status) {
            params.status = status;
        }

        let response = await apiClient.get('/jobs',{
            params: params,
            platform : platform
        });
        if (response.status == 200 && response.data && response.data.data && response.data.data.length > 0) {
            jobs = response.data.data
            return jobs
        }
        logger.error(`oss.getLatestJobs error: failed to get latest jobs`, {response: response.data})
        return jobs
    } catch (err) {
        logger.error(`oss.getLatestJobs error: ${err.message}`, {err});
        return jobs;
    }
}

const triggerConnectionSync = async function (platform, connection_id) {
    try {
        const payload = {
            jobType: 'sync',
            connectionId: connection_id
        }

        let response = await apiClient.post('/jobs', payload, {
            platform : platform
        })
        if (response.status == 200 && 'data' in response) {
            return true
        }
        logger.error("oss.triggerConnectionSync error", {connection_id})
        return false
    } catch (err) {
        logger.error(`oss.triggerConnectionSync error :`, {
            message : err.message ?? "",
            connection_id
        })
        return false
    }
}

const markConnectionInactive = async function (platform, connection_id) {
    try  {

        let response = await apiClient.patch('/connections/' + connection_id, {
            status: 'inactive',
        }, {
            platform : platform
        });

        if (response.status == 200) {
            return true
        }
        logger.error("oss.markConnectionInactive error", {connection_id})
        return false
    } catch (err) {
        logger.error(`oss.markConnectionInactive error :`, {
            message : err.message ?? "",
            connection_id
        })
        return false
    }
}

const getConnectionConfig = (source_type) => {
    if (source_type === SOURCE_SHOPIFY) {
        return CONNECTION_CONFIG_SHOPIFY_DEFAULT;
    } else if (source_type === SOURCE_GOOGLE_ANALYTICS) {
        return CONNECTION_CONFIG_GOOGLE_ANALYTICS_DEFAULT;
    } else if (source_type === SOURCE_GOOGLE_ADS) {
        return CONNECTION_CONFIG_GOOGLE_ADS_DEFAULT;
    } else if (source_type === SOURCE_KLAVIYO) {
        return CONNECTION_CONFIG_KLAVIYO_DEFAULT;
    } else if (source_type === SOURCE_FB) {
        return CONNECTION_CONFIG_FB_DEFAULT;
    } else {
        logger.error("oss.getConnectionConfig Invalid OSS source type", {source_type});
        return {};
    }
}

function getRandomCronExpression() {
    const hour = Math.floor(Math.random() * 24); // Random hour (0-23)
    const minute = Math.floor(Math.random() * 60); // Random minute (0-59)
    return `0 ${minute} ${hour} * * ?`;
}

const createConnection = async function (intg) {

    const {request_id, platform, source_type, airbyte_source_id, airbyte_destination_id} = intg;

    const destination_dataset = getDestinationDataset(platform, source_type, intg.auto_id);

    let payload = {
        namespaceDefinition: 'custom_format',
        namespaceFormat: destination_dataset,
        schedule: {scheduleType: 'cron', cronExpression: getRandomCronExpression()},
        dataResidency: 'eu',
        nonBreakingSchemaUpdatesBehavior: "propagate_columns",
        sourceId: airbyte_source_id,
        destinationId: airbyte_destination_id,
        status: 'active',
        configurations : getConnectionConfig(source_type)
    }

    try {
        let response = await apiClient.post('/connections', payload, {
            platform: platform
        })
        if (response.status == 200 && 'data' in response) {
            return {
                connection_id : response.data?.connectionId ?? "",
            };
        }
        logger.error("oss.createConnection error : ", {source_type, airbyte_source_id, airbyte_destination_id, request_id})
        return {error: "Failed to create connection"};
    } catch (err) {
        logger.error(`oss.createConnection error :`, {
            message : err.message ?? "",
            request_id,
            source_type,
            airbyte_source_id,
            airbyte_destination_id
        })
        return {error: err.message};
    }
}

async function generateSourceName(auto_id, source_type, shop_id) {
    const { myshopify_domain } = await shop.getShopById(shop_id);
    return `${auto_id}_${source_type}_${myshopify_domain.replace(".myshopify.com", "")}`;
}

const createSource = async function (intg) {

    const {
        auto_id,
        token_id,
        request_id,
        source_config,
        klaviyo_api_key,
        source_type,
        shop_id,
        platform,
        airbyte_workspace_id
    } = intg;

    const sourceName = await generateSourceName(auto_id, source_type, shop_id);
    let payload = {
        name : sourceName,
        workspaceId: airbyte_workspace_id,
    }

    // TODO - fix this - remove SOURCE_KLAVIYO by saving klaviyo_api_key in integration_tokens
    if (source_type === SOURCE_KLAVIYO) {
        payload.configuration = {
            sourceType: source_type,
            api_key: klaviyo_api_key
        }
    }  else if (source_type === SOURCE_SHOPIFY) {
        const { myshopify_domain } = await shop.getShopById(shop_id);
        var Token = new TokenService(shop_id)
        let access_token = await Token.getToken()
        payload.configuration = {
            sourceType: source_type,
            credentials: {
                auth_method: "oauth2.0",
                client_id: process.env.SHOPIFY_API_KEY,
                client_secret: process.env.SHOPIFY_API_SECRET,
                access_token: access_token
            },
            shop: myshopify_domain.replace(".myshopify.com", ""),
            start_date: "2015-01-01"
        }
    } else {
        const credentials = await secretmanager.getCredentials(token_id);
        if (credentials.error) {
            logger.error('oss.createSource: failed to retrieve credentials', { token_id, request_id });
            return {};
        }
        const { access_token, refresh_token } = credentials;
        let parsed_source_config;
        try {
            parsed_source_config = JSON.parse(source_config);
        } catch (err) {
            logger.error('oss.createSource: error parsing source_config', { err: err.message, source_config, request_id });
            return {};
        }

        if (!access_token || !parsed_source_config || (source_type !== SOURCE_FB && !refresh_token)) {
            logger.error('oss.createSourcePayload: missing required parameters', { request_id });
            return {};
        }

        if (source_type === SOURCE_GOOGLE_ANALYTICS) {
            payload.configuration = {
                sourceType: source_type,
                credentials: {
                    auth_type: "Client",
                    client_id: process.env.GOOGLE_CLIENT_ID,
                    client_secret: process.env.GOOGLE_CLIENT_SECRET,
                    access_token: access_token,
                    refresh_token: refresh_token
                },
                property_ids: parsed_source_config.ids,
            }
        } else if (source_type === SOURCE_GOOGLE_ADS) {
            payload.configuration = {
                sourceType: source_type,
                credentials: {
                    developer_token: process.env.GOOGLE_ADS_DEVELOPER_TOKEN,
                    client_id: process.env.GOOGLE_CLIENT_ID,
                    client_secret: process.env.GOOGLE_CLIENT_SECRET,
                    access_token: access_token,
                    refresh_token: refresh_token
                },
                customer_id: parsed_source_config.ids[0],
            }
        } else if (source_type === SOURCE_FB) {
            payload.configuration = {
                sourceType: source_type,
                credentials: {
                    auth_type: "Client",
                    client_id: process.env.FB_APP_ID,
                    client_secret: process.env.FB_APP_SECRET,
                    access_token: access_token
                },
                account_ids: parsed_source_config.ids
            }
        } else {
            logger.error('oss.createSource: invalid source type', { source_type, request_id });
            return {};
        }
    }

    try {
        const response = await apiClient.post('/sources', payload, {
            platform: platform
        });
        if (response.status === 200 && 'data' in response) {
            return {
                source_id : response.data?.sourceId ?? "",
            };
        }

        logger.error('oss.createSource: error creating source on OSS platform', { payload, request_id });
        return {};
    } catch (err) {
        logger.error('oss.createSource: error creating source on OSS platform', { message: err.message ?? "", payload, request_id });
        return {};
    }
}

async function handleConnect(source_type, request_id, shop_id) {

  switch (source_type) {
    case SOURCE_GOOGLE_ADS:
    case SOURCE_GOOGLE_ANALYTICS:
      const response = googleIntegration.getGoogleAuthUrl(source_type, request_id, shop_id);

      if (response.error) {
        return { error: response.error };
      }

      return { url: response.authUrl ?? "", source_type, request_id };

    case SOURCE_FB:
      const fbResponse = facebookIntegration.generateFacebookAuthUrl(source_type, request_id, shop_id);
      return { url: fbResponse.authUrl ?? "", source_type, request_id };

    default:
      return { url: "", source_type, request_id };
  }
}

export default {
    testConnection,
    handleConnect,
    getWorkspaceId,
    getDestinationId,
    createSource,
    markConnectionInactive,
    triggerConnectionSync,
    getLatestJobs,
    createConnection
};