import logger from '../logger.js';
import { createRequire } from "module";
const require = createRequire(import.meta.url);
import { google } from 'googleapis';
const axios = require('axios');
import { SOURCE_GOOGLE_ANALYTICS, SOURCE_GOOGLE_ADS, GOOGLE_ANALYTICS_SCOPES, GOOGLE_ADS_SCOPES } from "./constants.js";
import { INTEGRATION_STATUS_CONNECTED } from "./constants.js";
import integrations from './integrations.js';
import secretmanager from '../secretmanager';
import shop from '../shop.js';
import { constructRedirectUrl } from './util.js';


function getOAuthClient() {
    const oauth2Client = new google.auth.OAuth2(
        process.env.GOOGLE_CLIENT_ID,
        process.env.GOOGLE_CLIENT_SECRET,
        `${process.env.HOST}/api/auth/google/callback`
    );

    return oauth2Client;
}

function getGoogleAuthUrl(source_type, request_id, shop_id) {
    let scopes;
    if (source_type === SOURCE_GOOGLE_ANALYTICS) {
        scopes = GOOGLE_ANALYTICS_SCOPES;
    } else if (source_type === SOURCE_GOOGLE_ADS) {
        scopes = GOOGLE_ADS_SCOPES;
    } else {
        logger.error("googleIntegration.getGoogleAuthUrl invalid source_type", {source_type});
        return {error: 'Unsupported source type'};
    }
    const queryString = new URLSearchParams({
        request_id,
        source_type,
        shop_id
    }).toString();
    const oauth2Client = getOAuthClient();
    const authUrl = oauth2Client.generateAuthUrl({
        access_type: 'offline',
        prompt: 'consent',
        scope: scopes,
        state: queryString
      });
    return {authUrl};
}

async function getGoogleAnalyticsPropertyIds(oauth2Client) {
    try {
        const analyticsAdmin = google.analyticsadmin('v1beta');
        const accountsResponse = await analyticsAdmin.accounts.list({
          auth: oauth2Client
        });

        const accounts = accountsResponse.data.accounts || [];
        let propertiesInfo = [];
        for (const account of accounts) {
          const propertiesResponse = await analyticsAdmin.properties.list({
            filter: `parent: ${account.name}`,
            auth: oauth2Client
          });

          const properties = propertiesResponse.data?.properties ?? [];
          const propertiesMapped = properties.map(property => ({
            name: property.displayName ?? "",
            id: property.name?.split('/')?.[1] ?? ""
          }));

          propertiesInfo.push(...propertiesMapped);
        }

        return {data: propertiesInfo};
      } catch (error) {
        logger.error('Error retrieving Google Analytics properties', {message : error.message ?? ""});
        return {error: 'Failed to retrieve Google Analytics properties'};
      }
}

async function getGoogleAdsCustomerIds(oauth2Client) {
      try {
        await oauth2Client.refreshAccessToken();
        const new_access_token = oauth2Client.credentials?.access_token ?? "";

        if (!new_access_token) {
            logger.error('Error refreshing access token');
            return {error: 'Something went wrong. Please try again'};
        }

        const developerToken = process.env.GOOGLE_ADS_DEVELOPER_TOKEN;
        const response = await axios.get('https://googleads.googleapis.com/v20/customers:listAccessibleCustomers', {
            headers: {
                'Authorization': `Bearer ${new_access_token}`,
                'developer-token': developerToken,
                'Content-Type': 'application/json',
            },
        });

        if (response.status !== 200) {
            logger.error('Error retrieving Google Ads customers', {
                status: response.status,
                statusText: response.statusText,
                data: response.data,
            });
            return {error: 'Failed to retrieve Google Ads customers'};
        }
        const data = response.data;
        if (!data || !data.resourceNames) {
            logger.error('Error retrieving Google Ads customers', {data : data});
            return {error: 'Failed to retrieve Google Ads customers'};
        }

        const customerIds = data.resourceNames.map(name => name.split('/')[1]).filter(Boolean);

        // Now retrieve the account name (descriptiveName) for each customer ID
        const customerDetails = await Promise.all(
            customerIds.map(async (customerId) => {

                let customerData = {
                    id: customerId,
                    name: "",
                    manager: false,
                    test_account: false
                };

                try {
                    const customerResponse = await axios.post(
                        `https://googleads.googleapis.com/v20/customers/${customerId}/googleAds:search`,
                        {
                            query: 'SELECT customer.id, customer.resource_name, customer.descriptive_name, customer.manager, customer.test_account FROM customer'
                        },
                        {
                            headers: {
                                'Authorization': `Bearer ${new_access_token}`,
                                'developer-token': developerToken,
                                'Content-Type': 'application/json',
                            },
                        }
                    );

                    if (customerResponse.status !== 200) {
                        logger.error('Error Google Ads customer details', {
                            id: customerId,
                            status: customerResponse.status,
                            statusText: customerResponse.statusText,
                            data: customerResponse.data,
                        });
                        return customerData;
                    }

                    customerData.name = customerResponse.data?.results?.[0]?.customer?.descriptiveName ?? "";
                    customerData.manager = customerResponse.data?.results?.[0]?.customer?.manager ?? false;
                    customerData.test_account = customerResponse.data?.results?.[0]?.customer?.testAccount ?? false;

                    return customerData;
                } catch (error) {
                    logger.error('Error Google Ads customer details', {message : error.message ?? "", id: customerId}); 
                    return customerData
                }
            })
        );

        return {data: customerDetails};
      } catch (error) {
        logger.error('Error retrieving Google Ads customers', {message : error.message ?? ""});
        return {error: error};
      }
}

async function getGoogleData (credentials) {
    const { access_token, refresh_token } = credentials;
    const token = { 
        access_token,
        refresh_token,
        scope: credentials.scope,
        token_type: credentials.token_type,
        expiry_date: credentials.expiry_date
    };
   
    const oauth2Client = getOAuthClient();
    oauth2Client.setCredentials(token);
    if (credentials.integration_type === SOURCE_GOOGLE_ANALYTICS) {
        return await getGoogleAnalyticsPropertyIds(oauth2Client);
    } else if (credentials.integration_type === SOURCE_GOOGLE_ADS){
        return await getGoogleAdsCustomerIds(oauth2Client);
    } else {
        logger.error("googleIntegration.getGoogleData invalid source_type", {source_type : credentials.integration_type});
        return {error: 'Unsupported source type'};
    }
}

async function handleOAuthCallback(state, code) {
    try {
        const stateParams = new URLSearchParams(state);
        const request_id = stateParams.get('request_id');
        const source_type = stateParams.get('source_type');
        const shop_id = stateParams.get('shop_id');

        if (!request_id || !source_type || !shop_id) {
            logger.error('Missing parameters in state', {state});
            return { error: 'Missing parameters in state' };
        }

        const oauth2Client = getOAuthClient();
        const { tokens } = await oauth2Client.getToken(code);
        if (!tokens || !tokens.access_token || !tokens.refresh_token || !tokens.expiry_date || !tokens.scope || !tokens.token_type) {
            logger.error('Error getting tokens', {shop_id, tokens});
            return { error: "Error getting tokens" };
        }

        const optionalData = {
            refresh_token: tokens.refresh_token,
            expiry_date: tokens.expiry_date,
            scope: tokens.scope,
            token_type: tokens.token_type,
        };

        const response = await secretmanager.saveCredentials(shop_id, source_type, tokens.access_token, optionalData);
        if (!response.success) {
            logger.error('Failed to save credentials', {shop_id, source_type});
            return { error: 'Failed to save credentials', redirect_url: constructRedirectUrl(source_type, request_id, true) };
        }

        const data = {
            token_id: response.token_id,
            status: INTEGRATION_STATUS_CONNECTED,
        };

        const done = await integrations.updatePartial(request_id, data);
        if (!done) {
            logger.error('Failed to update integration', {request_id});
            return { error: 'Failed to update integration', redirect_url: constructRedirectUrl(source_type, request_id, true) };
        }

        const payload = {
            action: "saveTokenMetadata",
            request_id: request_id,
            token_id: response.token_id,
        };

        await shop.pushAsyncTask(payload);

        return { success: true, redirect_url: constructRedirectUrl(source_type, request_id, false) };
    } catch (error) {
        logger.error('Error handling OAuth callback', {shop_id, message: error.message ?? ""});
        return { error: error.message };
    }
}

export default {
    getGoogleData,
    getOAuthClient,
    getGoogleAuthUrl,
    handleOAuthCallback
}