import { cache } from '../redis.js';
import { createRequire } from "module";
const require = createRequire(import.meta.url);
const { GoogleAuth } = require('google-auth-library');
const jwt = require('jsonwebtoken');
import logger from '../logger.js';

const IAP_TOKEN_CACHE_KEY = 'iap_token_cache_key_v1';
const dayjs = require("dayjs");
var relativeTime = require('dayjs/plugin/relativeTime')
dayjs.extend(relativeTime);
import { V1_PLATFORM_OSS } from "../airbyte/constants.js";
import { CUBE_PLATFORM_OSS_VM } from "../cube/constants.js";

/**
 * Valid platforms for IAP token
 * - V1_PLATFORM_OSS,
 * - PLATFORM_OSS,
 * - CUBE_PLATFORM_OSS_VM,
 */
// Function to get a cached token or create a new one if expired
export async function getCachedIapToken(platform, endpoint) {
    let token;
    let cacheKey = `${IAP_TOKEN_CACHE_KEY}_${platform}`;
    if (process.env.NODE_ENV === 'production') {
        token = await cache.get(cacheKey);
        if (!token) {
            token = await getIdToken(platform);
            await cache.set(cacheKey, token, 3600); // Cache for 1 hour
        }
    } else {
        cacheKey = `${cacheKey}_${endpoint}`
        token = await cache.get(cacheKey);
        if (true || !token) {
            token = await createSignedJwtUsingServiceAccountKey(endpoint);
            await cache.set(cacheKey, token, 3600); // Cache for 1 hour
        }
    }
    return token;
}

// Obtain an OIDC token for the default service account on Cloud run
async function getIdToken(platform) {
    const auth = new GoogleAuth();

    // default to PLATFORM_OSS
    let iapClientId = process.env.IAP_CLIENT_ID;
    if (platform === V1_PLATFORM_OSS) {
        iapClientId = process.env.V1_IAP_CLIENT_ID;
    } else if (platform === CUBE_PLATFORM_OSS_VM) {
        iapClientId = process.env.CUBE_VM_IAP_CLIENT_ID;
    }

    // Get ID token client
    const client = await auth.getIdTokenClient(iapClientId);

    // Obtain the ID token
    const idToken = await client.getRequestHeaders().then(headers => headers.Authorization.split(' ')[1]);

    logger.info(`IAP token: ${idToken}`);
    return idToken;
}

// Function to create a signed JWT using service account file
// This is used for local development
// Service account JWTs are scoped to the audience provided. The audience should be the URL of the IAP-secured resource. For example, https://example.com. JWTs with an audience of https://example.com deny access to subdomains of the path.
async function createSignedJwtUsingServiceAccountKey(endpoint) {

    let serviceAccount;
    if (process.env.IAP_SERVICE_ACCOUNT_KEY_DEV) {
        serviceAccount = JSON.parse(process.env.IAP_SERVICE_ACCOUNT_KEY_DEV);
    } else {
        throw new Error('No IAP service account found. Set IAP_SERVICE_ACCOUNT_KEY_DEV env variable.');
    }
    logger.info(`Creating signed JWT for ${endpoint}`);

    // Define the JWT payload
    const iat = Math.floor(Date.now() / 1000);
    const exp = iat + 3600; // JWT valid for 1 hour
    const payload = {
        iss: serviceAccount.client_email,
        sub: serviceAccount.client_email,
        aud: endpoint,
        iat: iat,
        exp: exp,
    };

    // Sign the JWT
    const token = jwt.sign(payload, serviceAccount.private_key, { algorithm: 'RS256', header: { kid: serviceAccount.private_key_id } });
    return token;
}
