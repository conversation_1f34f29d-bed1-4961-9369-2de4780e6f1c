import mysql from "../database.js";
import logger from '../logger.js';
import { cache } from "../redis.js";
import crypto from "crypto";
import integrations from "../airbyte/integrations";
import shop from "../shop.js";
import {sendWeeklyReport as sendSlackWeeklyReport} from "../slack/index.js";
import {sendWeeklyReport as sendEmailWeeklyReport} from '../emails/weekly-report.js';
import {prepareWeeklyReport} from '../cube/data.js';

export const STATUS_PENDING = 0;
export const STATUS_RUNNING = 1;
export const STATUS_FINISHED = 2;
export const STATUS_FAILURE = 3;

export const KLAVIYO_RFM_TAGGING = "klaviyo_rfm_tagging";
export const KLAVIYO_WEEKLY_SYNC = "klaviyo_weekly_sync";
export const SLACK_WEEKLY_REPORT = "slack_weekly_report";
export const EMAIL_WEEKLY_REPORT = "email_weekly_report";
import { SOURCE_FB, SOURCE_KLAVIYO, V1_PLATFORM_OSS } from "../airbyte/constants.js";

import klaviyoService from "../klaviyo/index.js";

import parser from 'cron-parser';
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
dayjs.extend(utc)
dayjs.extend(timezone)

// Inserts a new job into the jobs table and queue it for processing
async function submit(shop_id, job_type, job_source, job_destination, options = {}) {
    let job_id = crypto.randomUUID();
    let source = '{}';
    let destination = '{}';
    try {
        source = JSON.stringify(job_source);
        destination = JSON.stringify(job_destination);
    } catch (err) {
        logger.error('submit: Invalid JSON', err);
        return {error: "Invalid JSON. Please check your input."};
    }

    const data = {
        job_id,
        shop_id,
        job_type,
        source,
        destination,
        status: STATUS_PENDING,
    }

    // options for scheduled jobs
    if (!!options.is_scheduled) {

        if (!options.cron_expression) {
            return {error: "Cron expression is required for scheduled jobs."};
        }

        if (!options.next_run_at) {
            return {error: "Next run time is required for scheduled jobs."};
        }

        data.is_scheduled = options.is_scheduled ? 1 : 0;
        data.cron_expression = options.cron_expression;
        data.next_run_at = options.next_run_at;
    }

    try {
        await mysql.query("INSERT INTO jobs SET ?", data);
        await cache.del(`shop_jobs_v2_${shop_id}`);
        if (!options.is_scheduled) {
            // if not scheduled, push to queue for immediate processing
            await shop.pushAsyncTask({action: "automation", job_id : job_id})
        }
        return {job_id};
    } catch (err) {
        logger.error('submit: Query execution error', err);
        return {error: "Something went wrong. Please try again."};
    }
};


async function run({job_id}) {

    logger.info('run: Received job', {job_id});

    let job = await getJob(job_id);
    if (!job || !job.job_id) {
        logger.error('run: Invalid job id', {job_id});
        return false
    }

    if (!job.active_flag) {
        logger.error('run: Inactive job', {job_id, active_flag: job.active_flag});
        return false;
    }

    if (job.status == STATUS_RUNNING) {
        logger.error('run: Job already running', {job_id, status: job.status});
        return false;
    }

    if (!job.is_scheduled && job.retry_count >= job.max_retries) {
        logger.error('run: Max retries reached', {job_id, retry_count: job.retry_count, max_retries: job.max_retries});
        await updatePartial(job_id, {
            status : STATUS_FAILURE,
            status_message : "Max retries reached",
            error_reason : "Max retries reached"
        });
        return false;
    }

    let shopObj = await shop.getShopById(job.shop_id);
    if (!shopObj || !shopObj.shop_id || !shopObj.active_flag) {
        logger.error('run: Invalid/ Inactive shop id', {job_id, shop_id: job.shop_id});
        return false;
    }


    // Scheduled jobs - check if next_run_at is in future and skip
    // Updated - Run scheduled job on demand if tried to run before next_run_at
    // used for klaviyo_weekly_sync job
    if (false && job.is_scheduled) {
        if (dayjs.tz(job.next_run_at, shop.DATA_TIME_ZONE).unix() > dayjs().unix()) {
            let waitHours = (dayjs.tz(job.next_run_at, shop.DATA_TIME_ZONE).unix() - dayjs().unix()) / 3600;
            logger.info('run: Skipping scheduled job', {
                job_id,
                next_run_at: job.next_run_at,
                waitHours : waitHours
            });
            return false;
        }
    }

    try {

        logger.info('run: Starting job', {job_id});

        await updatePartial(job_id, {
            status : STATUS_RUNNING,
            retry_count : job.retry_count + 1,
            last_run_at : dayjs().tz(shop.DATA_TIME_ZONE).toDate(),
            error_reason : "",
            status_message : ""
        });

        let result = {}
        switch (job.job_type) {
            case KLAVIYO_RFM_TAGGING:
                result = await runKlaviyoRFMTagging(job);
                break;
            case SLACK_WEEKLY_REPORT:
                result = await sendWeeklyReportJob(job);
                break;
            case EMAIL_WEEKLY_REPORT:
                result = await sendWeeklyReportJob(job);
                break;
            case KLAVIYO_WEEKLY_SYNC:
                result = await runKlaviyoRFMTagging(job);
                break;
            default:
                logger.error('run: Invalid job type', {job_id});
                return false;
        }

        logger.info('run: Job completed', {job_id, result});

        let updateData = {
            status : result.status,
            error_reason : result.error_reason ?? "",
            status_message : result.status_message ?? "",
            metadata: result.metadata ? JSON.stringify(result.metadata) : "{}"
        }

        if (job.is_scheduled) {
            let interval = parser.parseExpression(job.cron_expression, {
                tz: shopObj.iana_timezone
            });
        
            // Now interval.next() is next Monday 9 AM for shop local tz (ex. America/New_York) in UTC
            // convert it into IST using dayjs to store in DB
            let next_run_at = interval.next().toDate();
            updateData.next_run_at = dayjs.tz(next_run_at, shop.DATA_TIME_ZONE).format("YYYY-MM-DD HH:mm:ss");
        }

        await updatePartial(job_id, updateData);
        return updateData.status == STATUS_FINISHED;
    } catch (error) {
        await updatePartial(job_id, {
            status : STATUS_FAILURE,
            status_message : "Something went wrong",
            error_reason : error.message
        });
        logger.error('run: Error running job', {job_id, error});
        return false;
    }
}

async function sendWeeklyReportJob(job) {
    try {
        let shopObj = await shop.getShopById(job.shop_id)
        if (!shopObj || !shopObj.shop_id || !shopObj.active_flag) {
            logger.error("sendWeeklyReportJob: Shop not found or inactive", {shop_id: job.shop_id})
            return {
              status: STATUS_FAILURE,
              status_message: "Shop not found or inactive",
            };
        }
  
        let intgs = await integrations.getIntegrations(job.shop_id, SOURCE_FB)
        intgs = intgs[SOURCE_FB] ?? [];
  
        // TODO - there can be multiple facebook accounts connected
        let intg = intgs.length > 0 ? intgs[0] : {};
  
        let recipient = {
            shop_id: shopObj.shop_id,
            iana_timezone: shopObj.iana_timezone,
            myshopify_domain: shopObj.myshopify_domain,
            name : shopObj.name,
            job_id: job.job_id,
            fb_integration_id: intg.auto_id ?? "",
            useFBOSSDataset: intg.platform == V1_PLATFORM_OSS,
        }
  
        let content = await prepareWeeklyReport([recipient])
        if (!content[recipient.shop_id]) {
            logger.error("sendWeeklyReportJob: Data missing for shop", recipient)
            return {
              status: STATUS_FAILURE,
              status_message: "Data missing for shop",
            };
        }

        let sendResult = {}
        switch (job.job_type) {
            case EMAIL_WEEKLY_REPORT:
                sendResult = await sendEmailWeeklyReport(recipient.shop_id, content[recipient.shop_id]);
                break;
            case SLACK_WEEKLY_REPORT:
                sendResult = await sendSlackWeeklyReport(recipient.shop_id, content[recipient.shop_id]);
                break;
            default:
                logger.error('sendWeeklyReportJob: Invalid job type', {job_id});
                return {
                    status: STATUS_FAILURE,
                    status_message: "Invalid job type",
                    error_reason: "Invalid job type"
                };
        }
  
        if (sendResult.error) {
            logger.error("sendWeeklyReportJob: Error", {error : sendResult.error});
            return {
              status: STATUS_FAILURE,
              status_message: "Error sending report",
              error_reason: sendResult.error
            };
        }
  
        return {
          status: STATUS_FINISHED,
          status_message: "Report sent successfully",
        };
    } catch (err) {
        logger.error("sendWeeklyReportJob: Error", {error : err.message ?? ""});
        return {
          status: STATUS_FAILURE,
          status_message: "Error sending report",
          error_reason: err.message ?? ""
        };
    }
}

async function getScheduledJobs(job_type) {
    var jobs = [];

    try {
        let result = await mysql.query(`
            SELECT DISTINCT j.job_id
            FROM jobs j
            WHERE j.job_type = ?
                AND j.is_scheduled = 1
                AND j.next_run_at <= NOW()
                AND j.active_flag = 1
                AND j.status != 1
            ORDER BY j.next_run_at;
        `, [job_type]);
        jobs = result
    } catch (err) {
        logger.error('getScheduledJobs error : ', err)
    }

    return jobs;
  }

async function runJobType(job_type) {

    let jobs = await getScheduledJobs(job_type);
    if (jobs.length == 0) {
        return {
            totalJobs: 0,
            successfulJobs: 0
        }
    }

    // TODO - Alternatively - create cloud tasks for each job to be executed if runJobType has too many jobs and it's taking more than 1 hour to complete

    // Split jobs into chunks of size 10
    let chunkSize = 10;
    let chunks = [];
    for (let i = 0; i < jobs.length; i += chunkSize) {
        chunks.push(jobs.slice(i, i + chunkSize));
    }

    // Run each chunk in sequence, but jobs within a chunk in parallel
    let successfulJobs = 0;
    for (let chunk of chunks) {
        let promises = chunk.map(job => run({job_id: job.job_id}));
        let results = await Promise.all(promises);

        // Increment successfulJobs by the number of successful jobs in the current chunk
        successfulJobs += results.filter(result => result === true).length;
    }

    return {
        totalJobs: jobs.length,
        successfulJobs,
    }
}

async function runKlaviyoRFMTagging(job) {

    try {
        job.source = JSON.parse(job.source);
        job.destination = JSON.parse(job.destination);
    } catch (error) {
        return {
            status: STATUS_FAILURE,
            status_message: "Invalid or missing data",
            error_reason: "Invalid job data"
        };
    }

    const { shop_id, rfm_segment, period } = job.source;
    const { integration_request_id } = job.destination;


    let intg = await integrations.getIntegrationRequest(integration_request_id)
    let klaviyoAPIKey = intg?.klaviyo_api_key;
    if (!klaviyoAPIKey) {
        return {
            status: STATUS_FAILURE,
            status_message: "Klaviyo API Key not found",
            error_reason: "Klaviyo API Key not found"
        };
    }

    const result = await klaviyoService.syncRFMSegment(shop_id, rfm_segment, period, klaviyoAPIKey);

    let jobResult = {
        status: STATUS_FINISHED,
        status_message: "RFM Tagging completed. It may take some time to reflect in Klaviyo.",
        error_reason: ""
    }

    if (result.error) {
        jobResult = {
            status: STATUS_FAILURE,
            status_message: result.error,
            error_reason: result.error,
        };
    }

    if (result.klaviyo_job_ids) {
        jobResult.metadata = {klaviyo_job_ids: result.klaviyo_job_ids};
    }

    return jobResult;
}

async function getJob(job_id) {

    let key = `job_v2_${job_id}`;
    let job = await cache.get(key);

    if (!!job) {
        return job;
    }

    try {
        const query = "SELECT * FROM jobs WHERE job_id = ?";
        const result = await mysql.query(query, job_id);
        job = result[0];
        if(job && job.job_id){
            await cache.set(key, job);
        }
        return job;
    } catch (err) {
        logger.error('getJob: Query execution error', err);
        return {};
    }
}

async function getJobs(shop_id, job_type = '') {

    let key = `shop_jobs_v2_${shop_id}`;
    let jobs = await cache.get(key);

    if (!!jobs) {
        if (job_type) {
            return jobs.filter(job => job.job_type === job_type);
        }
        return jobs;
    }

    try{
        const query = "SELECT * FROM jobs WHERE shop_id = ? AND active_flag = 1 ORDER BY row_created_at DESC";
        const result = await mysql.query(query, shop_id);

        if (result.length > 0) {
            await cache.set(key, result)
        }

        if (job_type) {
            return result.filter(job => job.job_type === job_type);
        }

        return result;
    } catch (err) {
        logger.error('getJobs: Query execution error', err);
        return [];
    }
}

async function updatePartial (job_id, partial_configuration) {
    let job = await getJob(job_id);
    if(!job || !job.job_id){
        return false;
    }

    delete job.row_updated_at;
    delete job.row_created_at;

    let newJob = Object.assign(job, partial_configuration);
    try {
        await mysql.query("UPDATE jobs SET ? WHERE job_id = ?", [newJob, job_id]);
        await cache.del(`job_v2_${job_id}`);
        await cache.del(`shop_jobs_v2_${job.shop_id}`);
        return true;
    } catch (err) {
        logger.error('updatePartial: Query execution error', err);
        return false;
    }
}

async function makeWeeklyReportJob(shop_id, job_type) {

    let shopObj = await shop.getShopById(shop_id)
    if (!shopObj || !shopObj.shop_id || !shopObj.active_flag) {
      logger.error("makeWeeklyReportJob: Shop not found or inactive")
      return false;
    }

    // using empty job destination
    // destination data for weekly reports is picked from slack access tokens or email settings
    let job_destination = {};

    const job_source = {
      shop_id
    };


    let jobOptions = {
      is_scheduled: true,
      cron_expression : "0 9 * * 1" // Monday 9 AM by default
    }

    try {
      var interval = parser.parseExpression(jobOptions.cron_expression, {
        tz: shopObj.iana_timezone
      });

      // Now interval.next() is next Monday 9 AM for America/New_York in UTC
      // convert it into IST using dayjs to store in DB
      let next_run_at = interval.next().toDate();
      let next_run_at_ist = dayjs.tz(next_run_at, shop.DATA_TIME_ZONE).format("YYYY-MM-DD HH:mm:ss");

      jobOptions.next_run_at = next_run_at_ist;
    } catch (err) {
      logger.error('makeWeeklyReportJob: Error parsing cron expression: ', {error : err.message ?? ""});
      return false;
    }

    const res = await submit(
      shop_id,
      job_type,
      job_source,
      job_destination,
      jobOptions
    );

    if (res.error) {
      logger.error("makeWeeklyReportJob: error occurred while submiting job for slack", {error : res.error})
      return false;
    }

    return true;
}

async function makeKlaviyoWeeklySyncJob(shop_id, request_id) {
    let shopObj = await shop.getShopById(shop_id)
    let jobSource = {
      shop_id : shop_id,
      rfm_segment: "all",
    }
  
    let jobDestination = {
      integration_request_id: request_id
    }
  
    let jobOptions = {
      is_scheduled: true,
      cron_expression : "0 9 * * 1"
    }
  
    try {
      var interval = parser.parseExpression(jobOptions.cron_expression, {
        tz: shopObj.iana_timezone
      });
  
      // Now interval.next() is next Monday 9 AM for America/New_York in UTC
      // convert it into IST using dayjs to store in DB
      let next_run_at = interval.next().toDate();
      let next_run_at_ist = dayjs.tz(next_run_at, shop.DATA_TIME_ZONE).format("YYYY-MM-DD HH:mm:ss");
  
      jobOptions.next_run_at = next_run_at_ist;
    } catch (err) {
      logger.error('makeKlaviyoWeeklySyncJob: Error parsing cron expression: ', {error : err.message ?? ""});
      return false;
    }
  
    let res = await submit(
        shop_id,
        KLAVIYO_WEEKLY_SYNC,
        jobSource,
        jobDestination,
        jobOptions
    );

    if (res.error || !res.job_id) {
      logger.error("makeKlaviyoWeeklySyncJob: something went wrong while creating weekly klaviyo sync job", {error : res?.error ?? "", job_id : res?.job_id ?? ""});
      return false;
    }

    return true;
}

async function handleIntegrationDisconnect(request_id) {
    let intg = await integrations.getIntegrationRequest(request_id)
    if (intg.source_type == SOURCE_KLAVIYO) {
        let isDisabled = await disableKlaviyoJobs(intg.shop_id)
        return isDisabled
    }

    return true;
}

async function disableKlaviyoJobs(shop_id) {
    let jobs = await getJobs(shop_id, KLAVIYO_RFM_TAGGING);
    for (let job of jobs) {
        let done = await updatePartial(job.job_id, {active_flag: 0});
        if (!done) {
            logger.error("disableKlaviyoJobs: Error disabling job", {job_id: job.job_id});
            return false;
        }
    }

    jobs = await getJobs(shop_id, KLAVIYO_WEEKLY_SYNC);
    for (let job of jobs) {
        let done = await updatePartial(job.job_id, {active_flag: 0});
        if (!done) {
            logger.error("disableKlaviyoJobs: Error disabling job", {job_id: job.job_id});
            return false;
        }
    }

    return true;
}

async function disableAllJobs(shop_id) {
    let jobs = await getJobs(shop_id);
    let promises = jobs.filter(job => (job.is_scheduled || job.status != STATUS_FINISHED)).map(async (job) => {
        let done = await updatePartial(job.job_id, {active_flag: 0});
        if (!done) {
            logger.error("disableAllJobs: Error disabling job", {job_id: job.job_id});
            return false;
        }
        return true;
    });

    let results = await Promise.all(promises);
    return results.every(result => result === true);
}

export default {
    STATUS_PENDING,
    STATUS_RUNNING,
    STATUS_FINISHED,
    STATUS_FAILURE,
    KLAVIYO_RFM_TAGGING,
    run,
    submit,
    getJob,
    getJobs,
    updatePartial,
    makeWeeklyReportJob,
    makeKlaviyoWeeklySyncJob,
    handleIntegrationDisconnect,
    runJobType,
    disableAllJobs
}