import { createRequire } from "module";
const require = createRequire(import.meta.url);
const dayjs = require("dayjs");
const Dinero = require('dinero.js')
import numeral from 'numeral';
import axios from "axios";
import { cache, redis } from '../redis.js';
import logger from "../logger.js";
import quarterOfYear from "dayjs/plugin/quarterOfYear";
import isoWeek from "dayjs/plugin/isoWeek";
dayjs.extend(quarterOfYear);
dayjs.extend(isoWeek);

const TIME_FRAMES = {
    // "hour" : {
    //     short: "hr",
    //     granularity: "hour",
    //     full : "Hour",
    //     full_label : "hourly",
    //     row_name : (dt) => dt.format("DD MMM HH:mm"),
    //     format : (dt) => dt.format("YYYY-MM-DD HH"),
    //     add : (dt) => dt.add(1, 'hour'),
    // },
    "day" : {
        short: "day",
        granularity: "day",
        full : "Daily",
        full_label : "daily",
        row_name : (dt) => dt.format("DD MMM YYYY"),
        row_name_short : (dt) => dt.format("DD MMM"),
        format : (dt) => dt.format("YYYY-MM-DD"),
        add : (dt) => dt.add(1, 'day'),
    },
    "week" : {
        short: "wk",
        granularity: "week",
        full : "Week",
        full_label : "weekly",
        row_name : (dt) => dt.isoWeekday(1).format(`Wk ${dt.isoWeek()}: DD MMM`),
        row_name_short : (dt) => `${dt.isoWeekday(1).format(`DD MMM`)}`,
        format : (dt) => dt.isoWeekday(1).format("YYYY-MM-DD"),
        add : (dt) => dt.add(1, 'week'),
    },
    "month" : {
        short: "mo",
        granularity: "month",
        full : "Month",
        full_label : "monthly",
        row_name : (dt) => dt.format("MMM YYYY"),
        row_name_short : (dt) => `${dt.format(`MMM`)}`,
        format : (dt) => dt.format("YYYY-MM"),
        add : (dt) => dt.add(1, 'month'),
    },
    "quarter" : {
        short: "qtr",
        granularity: "quarter",
        full : "Quarter",
        full_label : "quarterly",
        row_name : (dt) => [
            `Jan-Mar ${dt.format("YYYY")}`,
            `Apr-Jun ${dt.format("YYYY")}`,
            `Jul-Sept ${dt.format("YYYY")}`,
            `Oct-Dec ${dt.format("YYYY")}`,
        ][dt.quarter()-1],
        row_name_short : (dt) => `Q${dt.quarter()}`,
        format : (dt) => `${dt.format("YYYY")}-${String("0" + dt.quarter()).slice(-2)}`,
        add : (dt) => dt.add(1, 'quarter'),
    },
    "year" : {
        short: "yr",
        granularity: "year",
        full : "Year",
        full_label : "yearly",
        row_name : (dt) => dt.format("YYYY"),
        row_name_short : (dt) => dt.format("YYYY"),
        format : (dt) => dt.format("YYYY"),
        add : (dt) => dt.add(1, 'year'),
    }
}



// function to convert currency using dinero.js
var convertCurrencyUSD = async (amount, from) => {
    if (from == "USD" || !from) {
        return amount
    }

    let currencyRates = await getExchangeRates();

    if (!currencyRates  || !currencyRates.rates || !currencyRates.rates[from]) {
        return amount
    }

    const rates = {
        rates: {
            [from]: 1/(currencyRates.rates[from]),
        }
    }

    let dn = await Dinero({ amount: amount }).convert(from, {
        endpoint: new Promise(resolve => resolve(rates))
    });

    return dn.getAmount()
}


// Function to convert currency using exchange rates
const convertCurrency = async (amount, fromCurrency, toCurrency = "USD") => {
    if (fromCurrency === toCurrency || !fromCurrency) {
        return amount;
    }

    const currencyRates = await getExchangeRates();

    if (!currencyRates || !currencyRates.rates || !currencyRates.rates[fromCurrency]) {
        return amount;
    }

    const fromRate = currencyRates.rates[fromCurrency];
    const toRate = currencyRates.rates[toCurrency] || 1;

    const convertedAmount = (amount / fromRate) * toRate;

    return convertedAmount;
};

// Function to convert currency using Dinero.js
const convertCurrencyDinero = async (amount, fromCurrency, toCurrency = "USD") => {
    if (fromCurrency === toCurrency || !fromCurrency) {
        return amount;
    }

    const currencyRates = await getExchangeRates();

    if (!currencyRates || !currencyRates.rates || !currencyRates.rates[fromCurrency]) {
        return amount;
    }

    const rates = {
        rates: {
            [fromCurrency]: 1 / currencyRates.rates[fromCurrency],
            [toCurrency]: currencyRates.rates[toCurrency] || 1,
        }
    };

    const dineroAmount = Dinero({ amount: amount * 100, currency: fromCurrency });
    const convertedDinero = await dineroAmount.convert(toCurrency, {
        endpoint: new Promise(resolve => resolve(rates))
    });

    return convertedDinero.getAmount() / 100;
};

export function createTimeFrameBuckets(start_date, end_date, granularity) {

    if (!TIME_FRAMES[granularity]) {
        return {}
    }

    var handler = TIME_FRAMES[granularity]
    var bucketsMap = {}

    var currentDate = dayjs(start_date)
	var endDate = dayjs(end_date)
    let index = 0;
	while (handler.format(currentDate) <= handler.format(endDate)) {
        let key = handler.format(currentDate)
        bucketsMap[key] = {
            start : dayjs(currentDate).format("YYYY-MM-DD"),
            id : handler.format(currentDate),
            name : handler.row_name(currentDate),
            short: handler.row_name_short(currentDate),
            index : index
        }
		currentDate = handler.add(currentDate)
        index++
	}

	return bucketsMap
}



// function to get call exchange rate api with a cache ttl of 1 hour 
var getExchangeRates = async () => {
    var cacheKey = 'exchange_rates';
    var fromCache = await cache.get(cacheKey);
    if (fromCache !== null) {
        return fromCache
    }

    try {
        const url = "https://openexchangerates.org/api/latest.json?app_id=********************************&prettyprint=true&show_alternative=false";
        const res = await axios.get(url);

        if (res.data) {
            await cache.set(cacheKey, res.data, 3600)
        }

        return res.data
    } catch (e) {
        logger.error("Error fetching exchange rates", e)
    }

    return {}
}



var validate  = (shop_id, start_date, end_date, time_frame = null)  => {

    var errors = [];
    if (Array.isArray(shop_id)) {
        if (shop_id.length === 0) {
            errors.push("Shop array is empty");
        }
    } else if (!shop_id) {
        errors.push("Could not identify shop");
    }

	if (dayjs(start_date) > dayjs(end_date)) {
		errors.push("Invalid Duration");
    }

    return {is_valid : (errors.length == 0), errors}
}


var moneyFormatter = (curr = "") => {
    return (x) => {

        if (curr == "" || !curr) {
            return numeral(x).format("0.[00]a")
        }

        if (x == null || x == undefined || isNaN(x)) {
            return numeral(0).format("0.[00]a")
        }

        // TODO
        if (!Number.isInteger(x)) {
            x = parseFloat(x).toFixed(1);
        }

        // Testing phase
        if (curr == "INR") {
            return rupeeShort(x)
        }

        return Dinero({amount : 100*x, currency: curr}).toFormat("$0,0")
    }
}


function rupeeShort(value) {

    if (isNaN(value)) {
        return value
    }

    var currencySymbol = '₹';
    if (value == null) {
        return '';
    }
    var InrRSOut = value;
    InrRSOut = Math.round(InrRSOut * 10) / 10;
    var RV = "";
    if (InrRSOut > 0 && InrRSOut < 1000) {
        RV = InrRSOut.toString();
    }
    else if (InrRSOut >= 1000 && InrRSOut < 10000) {
        RV = InrRSOut.toString();
    }
    else if (InrRSOut >= 10000 && InrRSOut < 100000) {
        var f1 = InrRSOut.toString().substring(0, 2);
        var f2 = InrRSOut.toString().substring(2, 5);
        RV = f1 + "," + f2;

    }
    else if (InrRSOut >= 100000 && InrRSOut < 1000000) {
        var f1 = InrRSOut.toString().substring(0, 1);
        var f2 = InrRSOut.toString().substring(1, 3);
        if (f2 == "00") {
            RV = f1 + " Lacs";
        }
        else {
            RV = f1 + "." + f2 + " Lacs";
        }
    }
    else if (InrRSOut >= 1000000 && InrRSOut < 10000000) {
        var f1 = InrRSOut.toString().substring(0, 2);
        var f2 = InrRSOut.toString().substring(2, 4);
        if (f2 == "00") {
            RV = f1 + " Lacs";
        }
        else {
            RV = f1 + "." + f2 + " Lacs";
        }
    }
    else if (InrRSOut >= 10000000 && InrRSOut < 100000000) {
        var f1 = InrRSOut.toString().substring(0, 1);
        var f2 = InrRSOut.toString().substring(1, 3);
        if (f2 == "00") {
            RV = f1 + " Cr";
        }
        else {
            RV = f1 + "." + f2 + " Cr";
        }
    }
    else if (InrRSOut >= 100000000 && InrRSOut < 1000000000) {
        var f1 = InrRSOut.toString().substring(0, 2);
        var f2 = InrRSOut.toString().substring(2, 4);
        if (f2 == "00") {
            RV = f1 + " Cr";
        }
        else {
            RV = f1 + "." + f2 + " Cr";
        }
    }
    else if (InrRSOut >= 1000000000 && InrRSOut < 10000000000) {
        var f1 = InrRSOut.toString().substring(0, 3);
        var f2 = InrRSOut.toString().substring(3, 5);
        if (f2 == "00") {
            RV = f1 + " Cr";
        }
        else {
            RV = f1 + "." + f2 + " Cr";
        }
    }
    else if (InrRSOut >= 10000000000) {
        var f1 = InrRSOut.toString().substring(0, 4);
        var f2 = InrRSOut.toString().substring(6, 8);
        if (f2 == "00") {
            RV = f1 + " Cr";
        }
        else {
            RV = f1 + "." + f2 + " Cr";
        }
    }
    else {
        RV = InrRSOut.toString();
    }
    return currencySymbol + RV;
}

export default {
    TIME_FRAMES,
    validate,
    createTimeFrameBuckets,
    moneyFormatter,
    getExchangeRates,
    convertCurrency,
    convertCurrencyDinero,
    convertCurrencyUSD
}