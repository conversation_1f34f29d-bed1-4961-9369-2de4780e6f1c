import dotenv from 'dotenv';
import { createRequire } from "module";
const require = createRequire(import.meta.url);
import logger from "../services/logger.js";

dotenv.config();
import {SESSION_COOKIE_NAME, STATE_COOKIE_NAME} from "@shopify/shopify-api";
import shopify from "../shopify.js";
import shop from '../services/shop.js';
import user from '../services/user.js';
const jwt = require('jsonwebtoken');
import admin from '../firebase-config.js';

// userAuthMiddleware()  is used where only user session is required - otherwise 401
// userAuthMiddleware(true) is used where user session is optional - but then shopify session is required - otherwise 401
export const userAuthMiddleware = function (optional = false) {
    return async function (req, res, next) {
      // idToken is JWT token that comes from the client app
      let idToken = req.headers["AuthIdToken"] ?? req.headers["authidtoken"] ?? req.headers["Authidtoken"];
  
      // custom auth token is JWT token that are used for all non-user invocations (ex. from cloud scheduler)
      const customAuthToken = req.headers["CustomAuthToken"] ?? req.headers["customauthtoken"];
  
      if (!idToken && !customAuthToken) {
        if (optional) {
          return next();
        } else {
          return res.status(401).send('Unauthorized');
        }
      }
  
      try {
        if (idToken) {
          const decodedToken = await admin.auth().verifyIdToken(idToken);
          if (decodedToken) {
            res.locals.user = decodedToken;
          }
        } else if (customAuthToken) {
          const decodedToken = jwt.verify(customAuthToken, process.env.CUSTOM_JWT_SECRET, {'algorithm':'HS256'});
          if (decodedToken) {
            res.locals.user = decodedToken;
          }
        }
      } catch (err) {
        logger.error(`userAuth error - ${err}`, {headers : req.headers})
        if (optional) {
          return await next();
        } else {
          return res.status(401).send('Unauthorized');
        }
      }
  
      await next();
    }
  }
  


// function to read a particular cookie from express request object
function getCookie(req, name) {
    try {
      var value = "; " + req.headers.cookie;
      var parts = value.split("; " + name + "=");
      if (parts.length == 2) {
        return parts.pop().split(";").shift();
      } else {
        return ""
      }
    } catch (err) {
      logger.error("getCookie error - ", err)
      return ""
    }
  }
  
  // authMiddleware - used in most of the APIs to authenticate the request
  // atleast one should be present - user session or shopify session
  export const authMiddleware = [userAuthMiddleware(true), async function (req, res, next) {
    let authedUser = res.locals.user ?? {}
    let shopifySessionCookie = getCookie(req, SESSION_COOKIE_NAME)
    if (!authedUser || !authedUser.uid) {
      // if no user session found, shopify session is mandatory
      return await shopify.validateAuthenticatedSession()(req, res, next)
    } else if (shopifySessionCookie !== "") {
      // both user and shopify session is valid
      return await shopify.validateAuthenticatedSession()(req, res, next)
    } else {
      // only user session is valid - this user may be a random user as well
      // but we'll allow the request to go through resolve* middlewares for authorization
      return await next();
    }
  }];
  
  
  // resolveMiddlware - used in most of the APIs to resolve the request data - like user data and shop data
  // atleast selected data shop from user session or a shopify session should be present
  export const resolveSelectedShop = [user.resolveUser, shop.resolveShop, async function (req, res, next) {
    // if user data not saved or shop data not saved - resolveSelectedShop will throw 401
    if (!req.body.resolved_login_id) {
      return res.status(401).send('Unauthorized');
    }
  
    // if no shop data found, resolveSelectedShop will throw 401
    let isShopDataPresent = !!req.body.shop && !!req.body.shop.shop_id;
    if (!isShopDataPresent) {
      return res.status(401).send('Unauthorized');
    }
  
    return await next()
  }]
  
  // for /api/login - there can be a case when user is logged in but there's no store mapped to the user
  // In this case - we can still allow the user to login and then show the onboarding screen
  export const resolveUserShop = [user.resolveUser, shop.resolveShop, async function (req, res, next) {
  
    if (!req.body.resolved_login_id) {
      return res.status(401).send('Unauthorized');
    }
  
    return await next()
  }]

  
// Middleware to redirect to auth for shopify auth flow
export const shopifyAuthRedirectMiddleware = async (req, res, next) => {

  const shop = shopify.api.utils.sanitizeShop(req.query.shop, false) || undefined;

  let sessionId = undefined;
  try {
    sessionId = await shopify.api.session.getCurrentId({
      isOnline: false,
      rawRequest: req,
      rawResponse: res,
    });
  } catch (error) {
    logger.error(`Error when getting current session ID: ${error.message}`, { 
      shop,
      path: req.path 
    });
  }

  // Case 1: Session not present but shop is present - go to /api/auth?shop={shop}
  if (!sessionId && shop && shop !== 'undefined') {
    logger.info("Redirecting to auth for shop (no session)", { shop })
    return res.redirect(`/api/auth?shop=${shop}`);
  }

  let session = undefined;
  if (sessionId) {
    try {
      session = await shopify.config.sessionStorage.loadSession(sessionId);
    } catch (error) {
      logger.error(`Error when loading session from storage: ${error.message}`, { sessionId });
    }
  }

  // Case 2: Session present, check if session shop is different than query param shop
  if (session && shop && session.shop !== shop) {
    logger.info("Redirecting to auth for shop (session shop mismatch)", { 
      sessionShop: session.shop, 
      queryShop: shop 
    });
    return res.redirect(`/api/auth?shop=${shop}`);
  }

  return next();
};