import {
    Body,
    Container,
    Column,
    Head,
    <PERSON>ing,
    Button,
    Html,
    Img,
    Link,
    Preview,
    Row,
    Section,
    Text,
  } from '@react-email/components';
  import * as React from 'react';
  
  const baseUrl = "https://storage.googleapis.com/datadrew-public";
  
  export const verificationEmail = ({verificationLink}) => (
    <Html>
      <Head />
      <Preview>Verify your email address</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={logoContainer}>
            <Img
                alt="Datadrew Analytics"
                style={{width: "300px", height: "69px"}}
                src={`${baseUrl}/dd-logo-trans.png`}
                width="49"
                height="21"
            />
          </Section>
          <Heading style={h1}>
            Verify your email address
            </Heading>
          <Text style={heroText}>
            Please verify your email address to complete your Datadrew account
            setup.
          </Text>

        <Section style={buttonContainer}>
            <Button pY={11} pX={23} style={button} href={verificationLink}>
                Click to Verify
            </Button>
        </Section>
  
          <Text style={text}>
            If you didn't request this email, there's nothing to worry about - you
            can safely ignore it.
          </Text>
  
          <Section>
            <Link
              style={footerLink}
              href="https://www.datadrew.io/privacy-policy"
              target="_blank"
              rel="noopener noreferrer"
            >
              Policy center
            </Link>
            &nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;
            <Link
              style={footerLink}
              href="https://help.datadrew.io"
              target="_blank"
              rel="noopener noreferrer"
            >
              Help center
            </Link>
            <Text style={footerText}>
                  Copyright © 2024 Datadrew, All rights reserved.
              </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
  
  export default verificationEmail;
  

  const buttonContainer = {
  padding: '27px 0 27px',
};


  const footerText = {
    fontSize: '12px',
    color: '#b7b7b7',
    lineHeight: '15px',
    textAlign: 'left',
    marginBottom: '50px',
  };
  
  const footerLink = {
    color: '#b7b7b7',
    textDecoration: 'underline',
  };
  
  const footerLogos = {
    marginBottom: '32px',
    paddingLeft: '8px',
    paddingRight: '8px',
    width: '100%',
  };
  
  const socialMediaIcon = {
    display: 'inline',
    marginLeft: '32px',
  };
  
  const main = {
    backgroundColor: '#ffffff',
    margin: '0 auto',
    fontFamily:
      "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  };
  
  const container = {
    maxWidth: '600px',
    margin: '0 auto',
  };
  
  const logoContainer = {
    marginTop: '32px',
  };
  
  const h1 = {
    color: '#1d1c1d',
    fontSize: '36px',
    fontWeight: '700',
    margin: '30px 0',
    padding: '0',
    lineHeight: '42px',
  };
  
  const heroText = {
    fontSize: '17px',
    lineHeight: '24px',
    marginBottom: '15px',
  };
  
  const codeBox = {
    background: 'rgb(245, 244, 245)',
    borderRadius: '4px',
    marginRight: '50px',
    marginBottom: '30px',
    padding: '43px 23px',
  };

  const button = {
    backgroundColor: '#5e6ad2',
    borderRadius: '3px',
    fontWeight: '600',
    color: '#fff',
    fontSize: '15px',
    textDecoration: 'none',
    textAlign: 'center',
    display: 'block',
  };
  
  const confirmationCodeText = {
    fontSize: '30px',
    textAlign: 'center',
    verticalAlign: 'middle',
  };
  
  const text = {
    color: '#000',
    fontSize: '14px',
    lineHeight: '24px',
  };
  