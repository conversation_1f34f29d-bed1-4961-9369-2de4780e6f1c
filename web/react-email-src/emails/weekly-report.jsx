import {
    <PERSON>,
    <PERSON><PERSON>,
    Con<PERSON>er,
    <PERSON>,
    <PERSON>r,
    Html,
    Img,
    <PERSON>,
    <PERSON><PERSON>,
    Row,
    Column,
    Preview,
    Section,
    Text,
  } from '@react-email/components';
  import {GrMoney} from 'react-icons/gr';
  import {BsFillCartCheckFill, BsPersonFillAdd} from 'react-icons/bs';
  import {MdPeopleAlt} from 'react-icons/md';
  import {AiOutlineFacebook, AiFillDollarCircle} from 'react-icons/ai';
  import * as React from 'react';
  
  const baseUrl = "https://storage.googleapis.com/datadrew-public";
  const iconStyle = {
    fontSize:"25px",
    color: "#344767",
  }

  const metrics = [
    [{
        label: "Sales",
        value: "total_price_formatted",
        icon : "https://storage.googleapis.com/datadrew-public/icon-bag-money.png",
        diff : "total_price_diff",
    },
    {
      label: "Facebook Ad Spend",
      value: "fb_spend_formatted",
      icon : "https://storage.googleapis.com/datadrew-public/fb-icon.png",
      diff : "fb_spend_diff",
    }],
    [{
      label: "Orders",
      value: "order_count_formatted",
      icon : "https://storage.googleapis.com/datadrew-public/orders.png",
      diff : "order_count_diff",
    },{
        label: "AOV",
        value: "aov_formatted",
        icon : "https://storage.googleapis.com/datadrew-public/hand-money.png",
        diff : "aov_diff",
    }],
    [{
        label: "New Customers",
        value: "new_cust_count_formatted",
        icon : "https://storage.googleapis.com/datadrew-public/new-customers.png",
        diff : "new_cust_count_diff",
    },
    {
        label: "Returning Customers",
        value: "returning_cust_count_formatted",
        icon : "https://storage.googleapis.com/datadrew-public/returning-customer.png",
        diff : "returning_cust_count_diff",
    }]
  ]
  
  export const WeeklyReport = (props) => {

    let appLink = `https://app.datadrew.io?shop=${props.myshopify_domain}&utm_source=EmailReports&utm_medium=Email&utm_campaign=Weekly+Report`;

    return (
      <Html>
        <Head />
        <Preview>Your weekly report for {props.domain}</Preview>
        <Body style={main}>
          <Container style={container}>
            <Section style={box}>
              <Container key="logo">
              <Link href={appLink}>
              <Img
                  alt="Datadrew Analytics"
                  style={{width: "250px", height: "68px", margin: "auto"}}
                  src={`${baseUrl}/dd-logo-full.png`}
                  width="49"
                  height="21"
              />
              </Link>
              </Container>
              <Hr style={hr} />
              <Container style={{width: "100%"}} key="heading">
              <Heading as="h1" style={{color:"white", margin: "auto", textAlign: 'center'}}>Your Weekly</Heading>
                  <Heading as="h1" style={{color:"white", margin:"auto", textAlign: 'center'}}>performance report</Heading>
                  <Img alt="weekly report" style={{width: "150px", height: "150px", margin: "auto", marginTop: "20px", marginBottom: "20px"}} src={`${baseUrl}/report-illustration.png`} /> 
                  <Text style={{color: "#a0a3bd", fontWeight:"bold", fontSize:"18px", textAlign: 'center'}}>{`${props.week_start} - ${props.week_end}`}</Text>
                  <Text style={{color: "#a0a3bd", textAlign: 'center'}}><i>compared to {`${props.prev_week_start} - ${props.prev_week_end}`}</i></Text>
              </Container>
              <Container style={{width: "100%"}} key="metrics">
              {
                  metrics.map((r, ind) => {
                      return (
                          <Row key={ind}>
                              {r.map((m, ind) => {
                                  let diffColor = "#aecef7";
                                  let textColor = "#095bc6";
                                  if (props[m.diff] > 0) {
                                      diffColor = "#bce2be";
                                      textColor = "#339537";
                                  } else if (props[m.diff] < 0) {
                                    diffColor = "#fcd3d0";
                                    textColor = "#f61200";
                                  }
                                  return (
                                      <Column key={m.label}>
                                          <Container style={metric} key={m.label}>
                                              <Row key={m.label}>
                                                  <Column style={{width: "40px"}}>
                                                  <Img
                                                      alt={m.label}
                                                      style={{width: "48", height: "48px", marginRight: "10px"}}
                                                      src={m.icon}
                                                  /></Column>
                                                  <Column style={{textAlign: "left"}}><Text style={{fontSize: "15px", fontWeight: "normal", margin: 0}}><b>{m.label}</b></Text></Column>
                                              </Row>
                                              <Text style={{fontSize: "20px", fontWeight: "normal", margin: "20px", textAlign: "center"}}>{props[m.value] ?? 0}</Text>
                                              <Section style={{textAlign: "center"}}>
                                              <Text style={{padding: "3px", paddingLeft: "10px", paddingRight: "10px", fontSize: "13px", color: textColor, display: "inline", fontWeight: "normal", backgroundColor: diffColor, borderRadius: "5px"}}>
                                                  {!props[m.diff] ? "-" : `${props[m.diff]}%`}
                                              </Text>
                                              </Section>
                                          </Container>
                                      </Column>
                                  )
                              })}
                          </Row>
                      )
                  })
              }
              </Container>

              <Section style={section} key="dashboard">
                <Button
                      pX={10}
                      pY={20}
                      style={button}
                      href={appLink}
                  >
                      📊 View Your Dashboard
                  </Button>
              </Section>

              <Section key="questions" style={{
                      borderRadius: 5,
                      color: "white",
                      width: "80%",
                      padding: "15px",
                      backgroundColor: "rgba(255,255,255,0.2)"
                  }}>
                  <Text style={{fontSize:"20px"}}>
                      Got Questions?
                  </Text>
                  <Text style={{fontSize: "13px", lineHeight: "18px", color: "white"}}>
                      If you have any questions or want us to include additional metrics in your weekly report, simply reply to this email or book your Insights walkthrough sessions with our growth expert.
                  </Text>
                  <Section style={sectionSection} key="book-session">
                  <Button
                        pX={10}
                        pY={15}
                        style={sectionButton}
                        href={"https://calendly.com/sumit-growth/30min"}
                    >
                      Schedule a call to learn more
                    </Button>
                </Section>
              </Section>
              <Hr style={hr} />
              <Text style={footer}>
                  Copyright © 2024 Datadrew, All rights reserved.
                  <br/>
                  You can change settings for this email at <Link href="https://app.datadrew.io/settings?utm_source=EmailReports&utm_medium=Email&utm_campaign=Weekly+Report">https://app.datadrew.io/settings</Link>
              </Text>
            </Section>
          </Container>
        </Body>
      </Html>
    );
  };
  
  export default WeeklyReport;


  const metric = {
    borderRadius: 5,
    width: "250px",
    color: "#344767",
    padding: "20px",
    margin: "20px",
    backgroundColor: "rgba(255,255,255)"
};

  const main = {
    backgroundColor: 'transparent',
    fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif'
  };
  
  const container = {
    backgroundColor: '#151b37',
    margin: '0 auto',
    padding: '20px 0 48px',
    marginBottom: '64px',
  };
  
  const box = {
    padding: '0 48px',
  };
  
  const hr = {
    borderColor: '#e6ebf1',
    margin: '20px 0',
  };
  
  const paragraph = {
    color: '#fff',
  
    fontSize: '16px',
    lineHeight: '24px',
    textAlign: 'left',
  };
  
  const anchor = {
    color: '#556cd6',
  };

  const section = {
    padding: '24px',
    borderRadius: '5px',
    textAlign: 'center',
  };

  const sectionSection = {
    padding: '12px',
    borderRadius: '5px',
    textAlign: 'center',
  };
  
  const sectionButton = {
    fontSize: '14px',
    backgroundColor: '#095bc6',
    color: '#fff',
    lineHeight: 1.5,
    borderRadius: '0.5em',
    padding: '0.75em 1.5em',
  };
  
  const button = {
    backgroundColor: 'transparent',
    border: "1px solid #fff",
    borderRadius: '5px',
    color: '#fff',
    fontSize: '16px',
    fontWeight: 'normal',
    textDecoration: 'none',
    margin: "auto"
  };
  
  const footer = {
    color: '#8898aa',
    fontSize: '12px',
    textAlign: "center",
    lineHeight: '16px',
  };
