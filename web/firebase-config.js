import admin from 'firebase-admin';

// Initialize Firebase Admin SDK only if not already initialized
if (!admin.apps.length) {
    if (process.env.NODE_ENV !== 'production' && process.env.FIREBASE_SERVICE_ACCOUNT_KEY_DEV) {
        let serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY_DEV);
        admin.initializeApp({
            credential: admin.credential.cert(serviceAccount)
        });
    } else {
        admin.initializeApp();
    }
}

export default admin; 