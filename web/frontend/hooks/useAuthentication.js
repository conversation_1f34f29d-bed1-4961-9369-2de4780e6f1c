import { useState, useEffect } from "react";
import { toast } from "react-toastify";
import { app } from '@/firebase-config';
import { getAuth, signInWithEmailAndPassword, createUserWithEmailAndPassword } from 'firebase/auth';
import { getMessageForFirebaseError } from "@/layouts/authentication/util";
import { useCancellableAxios, useMaterialUIController } from "@/context";

/**
 * Custom hook for handling user authentication flow
 * Supports both login and registration with email identification
 * 
 * @param {Object} options - Configuration options
 * @param {Function} options.onSuccess - Callback when authentication succeeds
 * @param {boolean} options.enableTracking - Whether to enable analytics tracking
 * @param {Function} options.trackEvent - Function to track events (required if enableTracking is true)
 * @param {boolean} options.showSuccessToast - Whether to show automatic success toast (default: true)
 * @returns {Object} Authentication state and handlers
 */
export const useAuthentication = ({ 
  onSuccess = () => window.location.reload(), 
  enableTracking = false,
  trackEvent = null,
  showSuccessToast = true
} = {}) => {
  const [controller] = useMaterialUIController();
  const { loginConfig } = controller;
  const axiosInstance = useCancellableAxios();

  // State management
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [emailSubmitStart, setEmailSubmitStart] = useState(false);
  const [flowType, setFlowType] = useState(null);
  const [passwordSubmitStart, setPasswordSubmitStart] = useState(false);
  const [subHeadingText, setSubHeadingText] = useState("Please enter email to identify");
  
  // Prevent multiple success executions
  const [successExecuted, setSuccessExecuted] = useState(false);

  // Listen for authentication state changes
  useEffect(() => {
    const auth = getAuth(app);
    const unsubscribe = auth.onAuthStateChanged(user => {
      if (!!user && !!user.email && !successExecuted) {
        // Mark success as executed to prevent multiple calls
        setSuccessExecuted(true);
        
        // Track successful authentication if tracking is enabled
        if (enableTracking && trackEvent) {
          trackEvent("Authentication Success", {
            flow_type: flowType === "login" ? "existing_user" : flowType === "register" ? "new_user" : "identifying_user",
            method: "email_password"
          });
        }
        
        // Only show toast if showSuccessToast is true
        if (showSuccessToast) {
          toast.success("Successfully signed in!");
        }
        
        onSuccess();
      }
    });

    return () => unsubscribe();
  }, [flowType, enableTracking, trackEvent, onSuccess, showSuccessToast, successExecuted]);

  // Update sub-heading text based on flow type
  useEffect(() => {
    switch (flowType) {
      case "register":
        setSubHeadingText("Please create a new password");
        break;
      case "login":
        setSubHeadingText("Please enter your password");
        break;
      default:
        setSubHeadingText("Please enter email to identify");
        break;
    }
  }, [flowType]);

  // Handle email submission for user identification
  const handleEmailSubmit = async () => {
    if (emailSubmitStart) return;

    setEmailSubmitStart(true);
    
    try {
      const response = await axiosInstance.post("/api/identify-user/email-submit", {
        email,
        shop_id: loginConfig?.shop?.shop_id,
      });

      if (response.data && response.data.status) {
        setFlowType(response.data.flow);
        
        // Track email identification if tracking is enabled
        if (enableTracking && trackEvent) {
          trackEvent("Email Identified", {
            flow_type: response.data.flow,
            email_domain: email.split('@')[1]
          });
        }
      } else {
        toast.error("Something went wrong. Please try again.");
      }
    } catch (error) {
      toast.error(
        error.response?.data?.message || "Something went wrong. Please try again."
      );
    } finally {
      setEmailSubmitStart(false);
    }
  };

  // Handle user registration
  const handleRegisterOnPasswordSubmit = async (ev) => {
    ev.preventDefault();

    if (passwordSubmitStart) return;

    if (password !== confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    setPasswordSubmitStart(true);

    try {
      const auth = getAuth(app);
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      if (!user) {
        toast.error('Something went wrong. Please try again.');
        return;
      }

      const idToken = await user.getIdToken();

      const response = await axiosInstance.post("/api/create-user", {
        id_token: idToken
      });

      if (response.data && response.data.status) {
        toast.success('Registered Successfully');
        
        // Track successful registration if tracking is enabled
        if (enableTracking && trackEvent) {
          trackEvent("User Registered", {
            email_domain: email.split('@')[1]
          });
        }
        
        // Auto-login after registration
        handleLoginOnPasswordSubmit(ev);
      } else {
        toast.error('Something went wrong. Please try again.');
      }
    } catch (error) {
      const errorMessage = getMessageForFirebaseError(error);
      toast.error(errorMessage);
      
      // Track registration error if tracking is enabled
      if (enableTracking && trackEvent) {
        trackEvent("Registration Error", {
          error_code: error.code,
          error_message: errorMessage
        });
      }
    } finally {
      setPasswordSubmitStart(false);
    }
  };

  // Handle user login
  const handleLoginOnPasswordSubmit = async (ev) => {
    ev.preventDefault();
    
    if (passwordSubmitStart) return;

    setPasswordSubmitStart(true);
    
    try {
      const auth = getAuth(app);
      await signInWithEmailAndPassword(auth, email, password);
      
      // Track successful login attempt if tracking is enabled
      if (enableTracking && trackEvent) {
        trackEvent("Login Attempt", {
          email_domain: email.split('@')[1]
        });
      }
    } catch (error) {
      const errorMessage = getMessageForFirebaseError(error);
      toast.error(errorMessage);
      
      // Track login error if tracking is enabled
      if (enableTracking && trackEvent) {
        trackEvent("Login Error", {
          error_code: error.code,
          error_message: errorMessage
        });
      }
    } finally {
      setPasswordSubmitStart(false);
    }
  };

  // Handle password submission routing
  const handlePasswordSubmit = (ev) => {
    ev.preventDefault();
    
    if (flowType === "register") {
      handleRegisterOnPasswordSubmit(ev);
    } else if (flowType === "login") {
      handleLoginOnPasswordSubmit(ev);
    } else {
      toast.error('Something went wrong. Please try again.');
    }
  };

  // Reset to email identification step
  const handleEditEmail = () => {
    setFlowType(null);
    setPassword("");
    setConfirmPassword("");
    setSubHeadingText("Please enter email to identify");
  };

  // Reset all form state
  const resetForm = () => {
    setEmail("");
    setPassword("");
    setConfirmPassword("");
    setFlowType(null);
    setEmailSubmitStart(false);
    setPasswordSubmitStart(false);
    setSubHeadingText("Please enter email to identify");
  };

  return {
    // State
    email,
    password,
    confirmPassword,
    flowType,
    subHeadingText,
    emailSubmitStart,
    passwordSubmitStart,
    
    // Setters
    setEmail,
    setPassword,
    setConfirmPassword,
    
    // Handlers
    handleEmailSubmit,
    handlePasswordSubmit,
    handleEditEmail,
    resetForm,
    
    // Validation helpers
    isEmailValid: email !== "",
    isPasswordValid: password !== "",
    isConfirmPasswordValid: confirmPassword !== "" || !flowType?.includes("register"),
    isFormValid: (email !== "" && !flowType) || 
                 (flowType && password !== "" && 
                  (!flowType.includes("register") || confirmPassword !== ""))
  };
};
