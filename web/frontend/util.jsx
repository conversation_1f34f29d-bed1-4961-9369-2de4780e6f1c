import numeral from 'numeral';
import <PERSON>ero from "dinero.js";

export const getMetricFormatterFn = (format_type) => {
    return (val, currency = "") => {
      if (format_type == "currency") {
        return moneyFormatter(val, currency)
      } else if (format_type == "roas") {
        return numeral(val).format('0.0a') + "x"
      } else if (format_type == "percent") {
        return numeral(val).format('0.[00]a') + '%'
      } else if (format_type == "absolute") {
        return numeral(val).format('0,0')
      } else {
        return numeral(val).format('0.[00]a')
      }
    }
}

export function moneyFormatter(val, curr = "") {
    if (curr == "") {
        return numeral(val).format("0.[00]a")
    }

    if (curr == "INR") {
        return rupeeShort(val)
    } else {
        let shortFormat = numeral(val).format("0.[00]a")
        return Dinero({amount : 0, currency: curr}).toFormat("$0").replace("0", shortFormat)
    }
}

export function getColors(l) {
    var baseColors = [
        // mui colors 
        "#e91e63", "#7b809a", "#1A73E8", "#4CAF50", "#fb8c00", "#F44335", "#344767",
        "#f24272", "#d51453", "#f28488", "#e04e3a", "#5f656e", "#a2a6ab", "#69717f", "#c0c4c9", "#006ee3", "#3c85f4", "#4197ff", "#0d5eb6", "#388e3c", "#6ac259", "#7ed370", "#30a945", "#ffb232", "#f7a212", "#fca531", "#ffa828", "#f5594a", "#f32f29", "#f66960", "#f2382f",
        "#283852", "#202b46", "#405780", "#1f2d4b", "#e6e6e6", "#f5f5f5", "#cccccc", "#707070", "#000000", "#ffffff", "#ffd54f", "#ffc107", "#ffeb3b", "#ffff00", "#c0c000", "#008000", "#40a9ff", "#00bfff", "#00ffff", "#c0c0c0",
        "#808080", "#d3d3d3", "#fafafa", "#f0f0f0", "#ececec", "#2ecc71", "#00ff00",

        "#8884d8",
        "#82ca9d",
        "#eda16c",
        "#d43d51",
        "#97bd88",
        "#22f0e6",
        "#e30f91",
        "#7337e0",
        "#5c0543",
        "#5ae94c",
        "#184a20",
        "#73313b",
        "#52fb2c",
        "#9250e8",
        "#4f448c",
        "#a58d62",
        "#ffc658",
        "#c97add",
        "#748081",
        "#359cba",
        "#ec8596",
        "#e2f4f9",
        "#1a9b0a",
        "#c7e52b",
    ];

    while (l > baseColors.length) {
        baseColors.push(
            "#" + (0x1000000 + Math.random() * 0xffffff).toString(16).substr(1, 6)
        );
    }

    return baseColors;
};


export function rupeeShort(value) {

    if (isNaN(value)) {
        return value
    }

    var currencySymbol = '₹';
    if (value == null) {
        return '';
    }
    var InrRSOut = value;
    InrRSOut = Math.round(InrRSOut * 10) / 10;
    var RV = "";
    if (InrRSOut > 0 && InrRSOut < 1000) {
        RV = InrRSOut.toString();
    }
    else if (InrRSOut >= 1000 && InrRSOut < 10000) {
        RV = InrRSOut.toString();
    }
    else if (InrRSOut >= 10000 && InrRSOut < 100000) {
        var f1 = InrRSOut.toString().substring(0, 2);
        var f2 = InrRSOut.toString().substring(2, 5);
        RV = f1 + "," + f2;

    }
    else if (InrRSOut >= 100000 && InrRSOut < 1000000) {
        var f1 = InrRSOut.toString().substring(0, 1);
        var f2 = InrRSOut.toString().substring(1, 3);
        if (f2 == "00") {
            RV = f1 + " Lacs";
        }
        else {
            RV = f1 + "." + f2 + " Lacs";
        }
    }
    else if (InrRSOut >= 1000000 && InrRSOut < 10000000) {
        var f1 = InrRSOut.toString().substring(0, 2);
        var f2 = InrRSOut.toString().substring(2, 4);
        if (f2 == "00") {
            RV = f1 + " Lacs";
        }
        else {
            RV = f1 + "." + f2 + " Lacs";
        }
    }
    else if (InrRSOut >= 10000000 && InrRSOut < 100000000) {
        var f1 = InrRSOut.toString().substring(0, 1);
        var f2 = InrRSOut.toString().substring(1, 3);
        if (f2 == "00") {
            RV = f1 + " Cr";
        }
        else {
            RV = f1 + "." + f2 + " Cr";
        }
    }
    else if (InrRSOut >= 100000000 && InrRSOut < 1000000000) {
        var f1 = InrRSOut.toString().substring(0, 2);
        var f2 = InrRSOut.toString().substring(2, 4);
        if (f2 == "00") {
            RV = f1 + " Cr";
        }
        else {
            RV = f1 + "." + f2 + " Cr";
        }
    }
    else if (InrRSOut >= 1000000000 && InrRSOut < 10000000000) {
        var f1 = InrRSOut.toString().substring(0, 3);
        var f2 = InrRSOut.toString().substring(3, 5);
        if (f2 == "00") {
            RV = f1 + " Cr";
        }
        else {
            RV = f1 + "." + f2 + " Cr";
        }
    }
    else if (InrRSOut >= 10000000000) {
        var f1 = InrRSOut.toString().substring(0, 4);
        var f2 = InrRSOut.toString().substring(6, 8);
        if (f2 == "00") {
            RV = f1 + " Cr";
        }
        else {
            RV = f1 + "." + f2 + " Cr";
        }
    }
    else {
        RV = InrRSOut.toString();
    }
    return currencySymbol + RV;
}

export function stringToColor(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        hash = str.charCodeAt(i) + ((hash << 5) - hash);
    }

    let color = [0, 0, 0];
    for (let i = 0; i < 3; i++) {
        let value = (hash >> (i * 8)) & 0xFF;
        color[i] = (value + 200) / 2;  // Mix with white to create pastel
    }

    return `rgb(${color[0]}, ${color[1]}, ${color[2]})`;
}

export function formatCellValue(value, columnName = '', depth = 0) {
    // Prevent infinite recursion
    if (depth > 3) {
        return '[Complex Object]';
    }

    // Handle null or undefined
    if (value === null || value === undefined) {
        return '-';
    }

    // Handle primitive types first
    if (typeof value === 'string') {
        return value.trim() || '-';
    }

    if (typeof value === 'number') {
        // Handle special number cases
        if (!isFinite(value)) {
            return isNaN(value) ? 'NaN' : (value > 0 ? '∞' : '-∞');
        }
        
        // Format large integers with commas
        if (Number.isInteger(value) && Math.abs(value) >= 1000) {
            return value.toLocaleString();
        }
        
        // Format decimals to reasonable precision
        if (!Number.isInteger(value)) {
            return value.toFixed(2);
        }
        
        return value.toString();
    }

    if (typeof value === 'boolean') {
        return value ? 'Yes' : 'No';
    }

    if (typeof value === 'bigint') {
        return value.toString() + 'n';
    }

    if (typeof value === 'symbol') {
        return value.toString();
    }

    if (typeof value === 'function') {
        return '[Function]';
    }

    // Handle Date objects
    if (value instanceof Date) {
        return isNaN(value.getTime()) ? 'Invalid Date' : value.toLocaleDateString();
    }

    // Handle objects (including arrays)
    if (typeof value === 'object') {
        try {
            // Handle arrays
            if (Array.isArray(value)) {
                if (value.length === 0) return '[]';
                if (value.length > 5) {
                    return `[${value.slice(0, 3).map(item => formatCellValue(item, columnName, depth + 1)).join(', ')}... +${value.length - 3} more]`;
                }
                return `[${value.map(item => formatCellValue(item, columnName, depth + 1)).join(', ')}]`;
            }

            // Handle Map objects
            if (value instanceof Map) {
                return `Map(${value.size} items)`;
            }

            // Handle Set objects
            if (value instanceof Set) {
                return `Set(${value.size} items)`;
            }

            // Handle regular objects
            const keys = Object.keys(value);
            if (keys.length === 0) return '{}';

            // Strategy 1: Look for a 'value' property
            if (Object.prototype.hasOwnProperty.call(value, 'value') && typeof value.value !== 'object') {
                return formatCellValue(value.value, columnName, depth + 1);
            }

            // Strategy 2: Look for a property that matches the column name
            if (columnName && Object.prototype.hasOwnProperty.call(value, columnName) && typeof value[columnName] !== 'object') {
                return formatCellValue(value[columnName], columnName, depth + 1);
            }

            // Strategy 3: If object has only one key-value pair, show it
            if (keys.length === 1) {
                const key = keys[0];
                return `${key}: ${formatCellValue(value[key], columnName, depth + 1)}`;
            }

            // Fallback: Safe JSON stringification with truncation
            let jsonStrParts = [];
            try {
                // Create a safe version of the object to avoid circular references
                const safeObj = {};
                const maxProps = 3; // Limit properties to avoid overly long strings
                keys.slice(0, maxProps).forEach(key => {
                    const val = value[key];
                    if (typeof val === 'object' && val !== null) {
                        safeObj[key] = Array.isArray(val) ? `[${val.length} items]` : '[Object]';
                    } else {
                        safeObj[key] = val;
                    }
                });
                jsonStrParts = Object.entries(safeObj).map(([key, val]) => `${key}: ${val}`);
                if (keys.length > maxProps) {
                    jsonStrParts.push(`... +${keys.length - maxProps} more`);
                }
            } catch (e) {
                return '[Object - Cannot Display]';
            }

            return jsonStrParts.join('\n');

        } catch (error) {
            // Handle any errors in object processing
            console.warn('Error formatting cell value:', error);
            return '[Error Displaying Value]';
        }
    }

    // Fallback for any other type
    try {
        return String(value);
    } catch (e) {
        return '[Cannot Display]';
    }
}
