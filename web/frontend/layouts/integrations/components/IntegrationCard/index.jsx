import { useState, useEffect } from "react";
// @mui material components
import Card from "@mui/material/Card";
import Divider from "@mui/material/Divider";
import Icon from "@mui/material/Icon";
import MDAvatar from "@/components/MDAvatar";
import Dialog from '@mui/material/Dialog';
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import { useSearchParams } from 'react-router-dom'

// @mui material components
import Grid from "@mui/material/Grid";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDBadge from "@/components/MDBadge";
import MDTypography from "@/components/MDTypography";
import MDInput from "@/components/MDInput";
import MDButton from "@/components/MDButton";
import Switch from "@mui/material/Switch";
import { toast } from "react-toastify";
import CircularProgress from "@mui/material/CircularProgress";
// Material Dashboard 2 PRO React Components
import MDBadgeDot from "@/components/MDBadgeDot";

// Images
import logoFacebook from "@/assets/images/facebook-logo.png";
import logoGoogleAds from "@/assets/images/google-ads-logo.png";
// import logotiktokAds from "@/assets/images/tiktok-ads-logo.png";
import logoGoogleAnalytics from "@/assets/images/google-analytics-logo.png";

import {useCancellableAxios, tracker, useMaterialUIController, fetchIntegrations} from "@/context";
import {useTranslation} from "react-i18next";
import PaywallDialog from '@/components/Paywall';
import axios from "axios";
import premiumTag from "@/assets/images/premium-tag.png";
import { SOURCE_FB, SOURCE_GOOGLE_ADS, SOURCE_GOOGLE_ANALYTICS } from "@/layouts/dashboards/metrics/metadata";

const INTEGRATION_SOURCE_TYPES = {
    [SOURCE_FB] :  {
        title: "facebook-ads",
        desc: "integration.fb-desc",
        configure_desc: "configure-step-description",
        sync_desc: "sync-step-description",
        image: logoFacebook,
        feature: "facebook_ads_overview"
    },
    [SOURCE_GOOGLE_ANALYTICS] : {
        title: "integration.ga4",
        desc: "integration.ga-desc",
        configure_desc: "configure-desc-google-analytics",
        sync_desc: "sync-desc-google-analytics",
        image: logoGoogleAnalytics,
        feature: "google_analytics_overview"
    },
    [SOURCE_GOOGLE_ADS] : {
        title: "google-ads",
        desc: "integration.google-ads-desc",
        configure_desc: "configure-desc-google-ads",
        sync_desc: "sync-desc-google-ads",
        image: logoGoogleAds,
        feature: "google_ads_overview"
    },
    // "tiktok" : {
    //     title: "Tiktok Marketing",
    //     image: logotiktokAds
    // },
    // "klaviyo" : {},
    // "shopify" : {}
}

function Configure({integration, onConfigure, sourceType, loaderConfigureSource, setLoaderConfigureSource}) {
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop} = controller;
    const [loaderFetchingOptions, setLoaderFetchingOptions] = useState(false);
    const {t}  = useTranslation();
    const axiosInstance = useCancellableAxios();
    let sourceConfigured = integration.configured ?? false;
    const [options, setOptions] = useState([]);
    const [selectedOptions, setSelectedOptions] = useState([]);

    useEffect(() => {
        let reqData = { request_id: integration.request_id }
        if (!!selectedShop) {
            reqData.selectedShop = selectedShop;
        }

        setLoaderFetchingOptions(true);
        axiosInstance.post('/api/integrations/configure/options', reqData).then((res) => {
            if (res.data.error) {
                console.error(res.data.error);
                toast.error(t("something-went-wrong"));
                return;
            } else {
                setOptions(res.data.data ?? []);
            }
            setLoaderFetchingOptions(false);
        }).catch((err) => {
            console.error(err);
            !axios.isCancel(err) && toast.error(t("something-went-wrong"));
            setLoaderFetchingOptions(false);
        });
    }, [selectedShop]);

    const configureSource = () => {
        if (loaderConfigureSource) {
            return
        }

        if (sourceConfigured) {
            toast.error(t("source-already-configured"));
            return;
        }

        if (selectedOptions.length == 0){
            toast.error(t("select-option"));
            return;
        }

        let payloadOptions = [];
        if (options.length == 0) {
            let source_config_id = selectedOptions[0].trim().replace("act_", '');
            if (source_config_id.match(/^[0-9]+$/g) === null || source_config_id.length <= 6) {
                toast.error(t("integration.invalid-account-id"));
                return;
            }
            payloadOptions = [{id: selectedOptions[0], name: ""}];
        } else {
            payloadOptions = options.filter((item) => selectedOptions.indexOf(item.id) > -1);
        }

        let reqData = {
            request_id: integration.request_id,
            options: payloadOptions
        }

        if (!!selectedShop) {
            reqData.selectedShop = selectedShop;
        }

        setLoaderConfigureSource(true);
        axiosInstance.post('/api/integrations/configure', reqData).then((res) => {
            setLoaderConfigureSource(false);
            if ('error' in res.data) {
                toast.error(res.data.error);
            } else {
                tracker.event("Source Configured", {
                    source_type: sourceType
                });
                toast.success(t("source-configured-success"));
                onConfigure && onConfigure();
            }
        }).catch((err) => {
            setLoaderConfigureSource(false);
            !axios.isCancel(err) && toast.error(t("something-went-wrong"))
        });
    }

    if (!(sourceType in INTEGRATION_SOURCE_TYPES)) {
        return null;
    }

    return (
        <MDBox>
        <MDBox  textAlign="center" mx="auto" m={2}>
            <MDTypography variant="button" color="dark" fontWeight="regular" size="small" sx={{fontSize:"15px"}}>
                {t(INTEGRATION_SOURCE_TYPES[sourceType].configure_desc)}
            </MDTypography>
        </MDBox>
        <MDBox mt={2} mx={2}>
            <MDBox pb={2}>
                {loaderFetchingOptions && 
                    <MDBox mb={2} display="flex" alignItems="center" justifyContent="center">
                        <CircularProgress size={20} color="info" />
                    </MDBox>}
                {!loaderFetchingOptions && options.length > 0 && <MDBox mb={2}>
                    {options.map((item, index) => (
                        <MDBox display="flex" alignItems="center" mb={0.5} ml={-1.5} key={index}>
                        <MDBox mt={0.5}>
                            <Switch checked={selectedOptions.indexOf(item.id) > -1} onChange={() => {
                                if (selectedOptions.indexOf(item.id) > -1) {
                                    setSelectedOptions(selectedOptions.filter((el) => el !== item.id));
                                } else {
                                    setSelectedOptions([...selectedOptions, item.id]);
                                }
                            } } />
                        </MDBox>
                        <MDBox width="80%" ml={0.5}>
                            <MDTypography variant="button" fontWeight="bold" color="text">
                                {!!item.name ? `${item.name} (${item.id})` : item.id}
                            </MDTypography>
                            {sourceType == SOURCE_FB && <><br /> <MDTypography variant="button" fontWeight="regular" color="text">
                              {!!item.owner_info.name ? `${item.owner_info.name} (${item.owner_info.id})` : item.owner_info.id}
                            </MDTypography></>}
                        </MDBox>
                        </MDBox>
                    ))}
                </MDBox>}
                {!loaderFetchingOptions && options.length == 0 && <MDBox mb={2}>
                    <MDInput
                        type="text"
                        disabled={sourceConfigured}
                        onChange={(e) => {
                            setSelectedOptions([e.target.value]);
                        }}
                        value={selectedOptions[0] ?? ""}
                        label={t("account-id")}
                        placeholder="**************"
                        variant="standard"
                        fullWidth     
                    />
                </MDBox>}

                <MDButton
                    color="info"
                    fullWidth
                    disabled={sourceConfigured || selectedOptions.length == 0}
                    variant="gradient"
                    onClick={configureSource}
                >
                    {loaderConfigureSource
                        ? <CircularProgress size={20} color="white" />
                        : (sourceConfigured ? t("saved") : t("save"))
                    }
                </MDButton>
            </MDBox>
        </MDBox>
        </MDBox>
    );
}

function ViewIntegrationDetails({integration, sourceType}) {
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop} = controller;
    const {t}  = useTranslation();
    const [job, setJob] = useState(null);

    const axiosInstance = useCancellableAxios();

    let pollInterval = null;

    const pollJobStatus = (request_id) => {
        let reqData = {
            request_id: request_id,
        };
    
        if (!!selectedShop) {
            reqData.selectedShop = selectedShop;
        }
    
        const pollJobInterval = () => {
            axiosInstance.post("/api/job-status?poll=1", reqData)
                .then((response) => {
                    if (response.data && !!response.data) {
                        setJob(response.data);
                        if (!response.data.poll) {
                            clearInterval(pollInterval);
                            fetchIntegrations(dispatch, selectedShop)
                        }
                    }
                })
                .catch((err) => {
                    console.log("error", err);
                });
        };
    
        axiosInstance.post("/api/job-status", reqData)
            .then(function (response) {
                if (response.data && !!response.data) {
                    setJob(response.data);
                    if (response.data.poll) {
                        pollInterval = setInterval(pollJobInterval, 30000); // every 30 sec
                        setTimeout(() => {
                            clearInterval(pollInterval);
                        }, 3600000); // stop polling after 1 hour
                    }
                }
            })
            .catch((err) => {
                console.log("error", err);
            });
    };

    useEffect(() => {
        if (!!integration && !!integration.request_id) {
            pollJobStatus(integration.request_id);
        }
        return () => clearInterval(pollInterval);
    }, [integration, selectedShop]);

    let abJob = job ?? {};
    let color = {
        "succeeded" : "success",
        "cancelled" : "secondary",
        "failed" : "error",
        "running" : "success",
        "setting-conn" : "info",
    }[abJob.status] ?? "warning";
    let connectionStatus = {
        "succeeded" : "connected",
        "cancelled" : "disabled",
        "failed" : "integration.unable-to-sync",
        "running" : "integration.importing",
        "setting-conn" : "setting-conn",
    }[abJob.status] ?? "";
    let showProgress = {
        "running" : true,
        "setting-conn" : true,
    }[abJob.status] ?? false;

    return (
        <MDBox width="100%">
        {abJob.status == "failed" && <MDBox mb={2} p={1} minWidth="400px" sx={{
            overflow : "scroll",
            background : "#F443351c",
            border: "1.2px solid #F44335",
        }} borderRadius="lg">
            <MDTypography fontWeight="regular" size="small" sx={{fontSize: "13px"}}>
                {t("integration.sync-failed")}
            </MDTypography>
        </MDBox>}
        <MDBox alignItems="center" display="flex" flexDirection="column" justifyContent="center" height="100%" width="100%" minWidth="400px" sx={{overflow : "scroll"}} bgColor="grey-100" borderRadius="lg" p={1}>
            <MDBox display="flex" alignItems="center" justifyContent="space-between" width="100%" mb={0.4} >
                <MDBox>
                    <MDTypography variant="button" color="dark" fontWeight="regular" size="small">
                        {t("integration.connection-status")}:
                    </MDTypography>
                </MDBox>
                {!!connectionStatus &&  <MDBadge variant="contained" size="sm" color={color} badgeContent={
                            <>
                            {showProgress &&  <> {t(connectionStatus)} &nbsp;<CircularProgress size={15} color={color} /></>}
                            {!showProgress && <> {t(connectionStatus)} </>}
                            </>
                        } container />}
                {!connectionStatus && <CircularProgress size={15} color={color} />}
            </MDBox>
            <MDBox display="flex" alignItems="center" justifyContent="space-between" width="100%" mb={0.4}>
                <MDBox>
                    <MDTypography variant="button" color="dark" fontWeight="regular">
                        {t("integration.last-sync")}:
                    </MDTypography>
                </MDBox>
                <MDBadge variant="string" size="sm" color={"warning"} badgeContent={
                    <>
                        {abJob.start_time_display ?? "-"}
                    </>
                } container />
            </MDBox>

            <MDBox display="flex" alignItems="center" justifyContent="space-between" width="100%" mb={0.5}>
                <MDBox>
                    <MDTypography variant="button" color="dark" fontWeight="regular">
                        {t("integration.dt-connected")}:
                    </MDTypography>
                </MDBox>
                <MDBadge variant="string" size="sm" color={"secondary"} badgeContent={
                    <>
                        {integration.created_at_display ?? "-"}
                    </>
                } container />
            </MDBox>
        </MDBox>
        </MDBox>
    );
}


const IntegrationAccountDialog = NiceModal.create((props) => {

    const modal = useModal();

    const [loaderDisconnectSource, setLoaderDisconnectSource] = useState(false);
    const [loaderConfigureSource, setLoaderConfigureSource] = useState(false);
    const {integration, sourceType} = props;
    const [controller, dispatch] = useMaterialUIController();
    const [confirmDisconnectSource, setConfirmDisconnectSource] = useState(false);
    const {selectedShop} = controller;
    const {t} = useTranslation();

    const axiosInstance = useCancellableAxios();

    const disconnectSource = () => {

        if (loaderDisconnectSource) {
            return
        }

        if (!integration.request_id) {
            toast.error(t("something-went-wrong"))
            return;
        } 

        let payload = {
            request_id: integration.request_id,
        }

        if (!!selectedShop) {
            payload.selectedShop = selectedShop;
        }

        setLoaderDisconnectSource(true);
        axiosInstance.post('/api/integrations/disconnect', payload ).then((res) => {
            setLoaderDisconnectSource(false);;
            if ('error' in res.data) {
                toast.error(res.data.error);
            } else {
                toast.success(t("source-disconnected-success"));
                tracker.event("Source Disconnected", {
                    source_type : sourceType
                })
                modal.hide();
                fetchIntegrations(dispatch, selectedShop)
            }
        }).catch((err) => {
            console.log(err);
            !axios.isCancel(err) && toast.error(t("something-went-wrong"))
            setLoaderDisconnectSource(false);;
        });
    }

    const source_config_id = integration.source_config_id ?? "";
    const source_config_name = integration.source_config_name ?? "";
    let source_name = ""
    if (!!source_config_id && !!source_config_name) {
        source_name = `${source_config_name}`;
    } else if (!!source_config_id) {
        source_name = `#${source_config_id}`;
    } else {
        source_name = t("integration.finish-account-setup");
    }


    let mode = "";
    if (confirmDisconnectSource) {
        mode = "disconnect";
    } else if (integration && !integration.configured) {
        mode = "configure";
    } else if (integration && integration.configured) {
        mode = "view";
    } else {
        mode = "";
    }

    return (
        <Dialog
            open={modal.visible}
            onClose={() => {
                if (!loaderDisconnectSource && !loaderConfigureSource) {
                    modal.hide()
                }
            }}
            TransitionProps={{
                onExited: () => modal.remove(),
            }}
            maxWidth={mode == "disconnect" ? "xs" :"sm"}
        >
            <MDBox p={2}>
                <MDBox display="flex" alignItems="center" justifyContent="space-between">
                    <MDBox display="flex" alignItems="center">
                        <MDBox component="img" src={INTEGRATION_SOURCE_TYPES[sourceType].image} alt="logo" width="1.5rem" mr={1} />
                        <MDTypography variant="h6" fontWeight="regular">
                            {source_name}
                        </MDTypography>
                    </MDBox>
                    {mode == "configure" && <MDButton
                        size="small"
                        sx={{py:0}}
                        color="secondary"
                        variant="text"
                        onClick={() => setConfirmDisconnectSource(true)}
                    >
                        <LinkBreakIcon/>&nbsp;
                        {t("disconnect-btn")}
                    </MDButton>}
                </MDBox>
                <Divider />
                {mode == "configure" && <MDBox display="flex" justifyContent="space-between" alignItems="center">
                    <Configure
                        {...props}
                        onConfigure={() => {
                            modal.hide();
                            fetchIntegrations(dispatch, selectedShop)
                        }}
                        loaderConfigureSource={loaderConfigureSource}
                        setLoaderConfigureSource={setLoaderConfigureSource}
                    />
                </MDBox>}
                {mode == "view" && <><MDBox display="flex" justifyContent="space-between" alignItems="center">
                    <ViewIntegrationDetails {...props} />
                    </MDBox>
                    <Divider />
                    <MDBox display="flex" alignItems="center" justifyContent="center" width="100%" mb={1}>
                        <MDButton
                            size="small"
                            sx={{py:0}}
                            color="secondary"
                            variant="text"
                            onClick={() => setConfirmDisconnectSource(true)}
                        >
                            <LinkBreakIcon/>&nbsp;
                            {t("disconnect-btn")}
                        </MDButton>
                    </MDBox>
                </>}
                {mode == "disconnect" && <MDBox width="100%" mb={1}>
                    <MDBox>
                        <MDTypography variant="button" color="dark" fontWeight="medium">
                            {t("integration.disconnect-title")}
                        </MDTypography> 
                    </MDBox>
                    <MDBox>
                        <MDTypography variant="button" color="dark" fontWeight="regular">
                            {t("integration.disconnect-desc")}
                        </MDTypography>
                    </MDBox>
                    <Divider />
                    <MDBox display="flex" alignItems="center" justifyContent="center">
                        <MDBox width="100%" mr={1}>
                        <MDButton
                            fullWidth
                            size="small"
                            mr={1}
                            disabled={loaderDisconnectSource || loaderConfigureSource}
                            color="secondary"
                            variant="outlined"
                            onClick={() => setConfirmDisconnectSource(false)}
                        >
                            {t("cancel")}
                        </MDButton>

                        </MDBox>
                        <MDBox width="100%">
                        <MDButton
                            fullWidth
                            size="small"
                            disabled={loaderDisconnectSource || loaderConfigureSource}
                            color="error"
                            variant="contained"
                            onClick={() => disconnectSource(integration.request_id)}
                        >
                            {loaderDisconnectSource
                                ?  <CircularProgress size={20} color="white" />
                                : t("integration.disconnect-yes")
                            }
                        </MDButton>
                        </MDBox>
                    </MDBox>
                </MDBox>}
            </MDBox>
        </Dialog>
    );
});

const ActionRequiredIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            fill="#F44335"
            viewBox="0 0 256 256">
            <path d="M236.8,188.09,149.35,36.22h0a24.76,24.76,0,0,0-42.7,0L19.2,188.09a23.51,23.51,0,0,0,0,23.72A24.35,24.35,0,0,0,40.55,224h174.9a24.35,24.35,0,0,0,21.33-12.19A23.51,23.51,0,0,0,236.8,188.09ZM222.93,203.8a8.5,8.5,0,0,1-7.48,4.2H40.55a8.5,8.5,0,0,1-7.48-4.2,7.59,7.59,0,0,1,0-7.72L120.52,44.21a8.75,8.75,0,0,1,15,0l87.45,151.87A7.59,7.59,0,0,1,222.93,203.8ZM120,144V104a8,8,0,0,1,16,0v40a8,8,0,0,1-16,0Zm20,36a12,12,0,1,1-12-12A12,12,0,0,1,140,180Z">

            </path>
        </svg>
    )
}

const LinkBreakIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            fill="#7b809a"
            viewBox="0 0 256 256">
            <path d="M198.63,57.37a32,32,0,0,0-45.19-.06L141.79,69.52a8,8,0,0,1-11.58-11l11.72-12.29a1.59,1.59,0,0,1,.13-.13,48,48,0,0,1,67.88,67.88,1.59,1.59,0,0,1-.13.13l-12.29,11.72a8,8,0,0,1-11-11.58l12.21-11.65A32,32,0,0,0,198.63,57.37ZM114.21,186.48l-11.65,12.21a32,32,0,0,1-45.25-45.25l12.21-11.65a8,8,0,0,0-11-11.58L46.19,141.93a1.59,1.59,0,0,0-.13.13,48,48,0,0,0,67.88,67.88,1.59,1.59,0,0,0,.13-.13l11.72-12.29a8,8,0,1,0-11.58-11ZM216,152H192a8,8,0,0,0,0,16h24a8,8,0,0,0,0-16ZM40,104H64a8,8,0,0,0,0-16H40a8,8,0,0,0,0,16Zm120,80a8,8,0,0,0-8,8v24a8,8,0,0,0,16,0V192A8,8,0,0,0,160,184ZM96,72a8,8,0,0,0,8-8V40a8,8,0,0,0-16,0V64A8,8,0,0,0,96,72Z">
            </path>
        </svg>
    )
}

// Custom styles for IntegrationCard
function IntegrationCard({ request_id, sourceType, sourceIntegrations }) {

    const [sourceAdsMenu, setSourceAdsMenu] = useState(null);
    const [loaderConnectSource, setLoaderConnectSource] = useState(false);
    const [hoveredIndex, setHoverIndex] = useState(null);
    const {t} = useTranslation();
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, shopConfig} = controller;
    const axiosInstance = useCancellableAxios();

    const openSourceAdsMenu = (event) => setSourceAdsMenu(event.currentTarget);
    const closeSourceAdsMenu = () => setSourceAdsMenu(null);

    const [searchParams, setSearchParams] = useSearchParams();
    let integrationRequestDialog = searchParams.get('request_id')
    let integrationRequestFilter = request_id ?? "";

    useEffect(() => {
        if (!!integrationRequestDialog) {
            for (let intg of sourceIntegrations) {
                if (intg.request_id == integrationRequestDialog) {
                    NiceModal.show(IntegrationAccountDialog, {integration : intg, sourceType})
                    return;
                }
            }
        }
    }, [integrationRequestDialog]);


    let filteredSourceIntegrations = sourceIntegrations;
    if (!!integrationRequestFilter) {
        filteredSourceIntegrations = sourceIntegrations.filter((intg) => intg.request_id == integrationRequestFilter);
    }

    let source = INTEGRATION_SOURCE_TYPES[sourceType] ?? {};
    let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;
    let isSubscriptionInActive = isSubscriptionEnabled && !(shopConfig.planDetails?.features?.[source.feature] ?? false);
    let blockConnectMultipleAccounts = isSubscriptionEnabled && !(shopConfig.planDetails?.features?.connect_multiple_accounts ?? false);

    const connectSource = () => {
        if (loaderConnectSource) {
            return
        }

        // if (sourceConnected) {
        //     toast.error(t("source-already-connected"));
        //     return;
        // }

        if (isSubscriptionInActive) {
            tracker.event("Paywall", {feature : source.feature});
            NiceModal.show(PaywallDialog, {feature : source.feature})
            return;
        }

        if (blockConnectMultipleAccounts && sourceIntegrations.length != 0) {
            tracker.event("Paywall", {feature : "connect_multiple_accounts"});
            NiceModal.show(PaywallDialog, {feature : "connect_multiple_accounts"})
            return;
        }

        let reqData = {source_type: sourceType};

        if (!!selectedShop) {
            reqData.selectedShop = selectedShop;
        }

        tracker.event("Source Connection Started", {
            source_type: sourceType
        });
        setLoaderConnectSource(true);

        axiosInstance.post('/api/integrations/connect', reqData).then((res) => {
            if ('error' in res.data) {
                toast.error(res.data.error);
                setLoaderConnectSource(false);
            } else {
                window.location.href = res.data.url;
            }
        }).catch((err) => {
            console.log(err);
            !axios.isCancel(err) && toast.error(t("something-went-wrong"))
            setLoaderConnectSource(false);
        });
    }

    const renderMenu = (state, close) => (
        <Menu
            anchorEl={state}
            anchorOrigin={{ vertical: "top", horizontal: "left" }}
            transformOrigin={{ vertical: "top", horizontal: "right" }}
            open={Boolean(state)}
            onClose={close}
            keepMounted
        >
            <MenuItem onClick={() => {close();connectSource();}}>{t("add-new-account")}</MenuItem>
        </Menu>
    );

    let dropdown= false && {
        action: openSourceAdsMenu,
        menu: renderMenu(sourceAdsMenu, closeSourceAdsMenu),
    }

    return (
        <Card>
            <MDBox p={2}>
            <MDBox display="flex" alignItems="center">
                <MDAvatar
                    src={source.image}
                    alt={source.title}
                    size="lg"
                    variant="rounded"
                    sx={{
                        p: 1,
                        mt: 1,
                        borderRadius: ({ borders: { borderRadius } }) => borderRadius.xl,
                        background : "#49a3f11c",
                        border: "1px solid #49a3f1",
                    }}
                />
                <MDBox ml={2} mt={1} lineHeight={0}>
                    <MDTypography variant="h6" textTransform="capitalize" fontWeight="regular">
                        {t(source.title)}
                    </MDTypography>
                    {source.desc && <MDTypography variant="caption" fontWeight="regular">{t(source.desc)}</MDTypography>}
                </MDBox>
                {dropdown && (
                    <MDTypography
                        color="secondary"
                        onClick={dropdown.action}
                        sx={{
                        ml: "auto",
                        mt: 1,
                        alignSelf: "flex-start",
                        // py: 1.25,
                        }}
                    >
                        <Icon fontSize="default" sx={{ cursor: "pointer", fontWeight: "bold" }}>
                        more_vert
                        </Icon>
                    </MDTypography>
                )}
                {dropdown.menu}
            </MDBox>

            {/* <MDBox my={2} lineHeight={1}>
                <MDTypography variant="button" fontWeight="light" color="text">
                {description}
                </MDTypography>
            </MDBox> */}
            <Divider />

            <Grid container spacing={3}>

            {filteredSourceIntegrations.map((intg, ind) => {

                const source_config_id = intg.source_config_id ?? "";
                const source_config_name = intg.source_config_name ?? "";
                let source_name = ""
                if (!!source_config_id && !!source_config_name) {
                    source_name = `${source_config_name}`;
                } else if (!!source_config_id) {
                    source_name = `#${source_config_id}`;
                } else {
                    source_name = t("new");
                }

                let color = "success";
                let badgeContent = t("connected");
                let showBadge = false;
                if (intg.connected && !intg.configured) {
                    color = "error";
                    badgeContent = t("setup");
                    showBadge = true;
                } else if (intg.connected && intg.configured && !intg.synced) {
                    color = "warning";
                    badgeContent = t("view-details");
                    showBadge = true;
                }

                return (
                    <Grid
                        item
                        xs={12}
                        md={6}
                        key={ind}
                    >
                        <MDBox
                            variant="gradient"
                            borderRadius="xl"
                            shadow="md"
                            pl={1.4}
                            display="flex"
                            alignItems="center"
                            justifyContent="space-between"
                            height="3rem"
                            sx={{
                                border: "1px solid rgba(0, 0, 0, .125)",
                                cursor: "pointer",
                                "&:hover": {
                                    backgroundColor: "rgba(0, 0, 0, .01)"
                                }
                            }}
                            onMouseEnter={() => {
                                setHoverIndex(ind);
                            }}
                            onMouseLeave={() => {
                                setHoverIndex(null);
                            }}
                            onClick={() => NiceModal.show(IntegrationAccountDialog, {integration : intg, sourceType})}
                        >
                            <MDTypography variant="button" fontWeight="regular" sx={{overflow: "hidden", textOverflow: "ellipsis", whiteSpace: "nowrap"}}>
                                {source_name}
                            </MDTypography>
                            {hoveredIndex !== ind && showBadge && <MDBox
                                component="i"
                                display="inline-block"
                                width={"0.5rem"}
                                height={"0.5rem"}
                                borderRadius="50%"
                                bgColor={color}
                                variant={"contained"}
                                mr={2}
                            />}
                            {hoveredIndex == ind && <MDBadgeDot badgeContent={badgeContent} color={color} px={1} size="sm"/>}
                            {/* <MDBox mt={0.8} mr={1}><ActionRequiredIcon/></MDBox> */}
                        </MDBox>
                    </Grid>
                )
            })}

            <Grid item xs={12} md={6} key={"connect-src"}>
                <MDBox
                    shadow="md"
                    p={1} 
                    sx={{
                        cursor: "pointer",
                        borderRadius: ({ borders: { borderRadius } }) => borderRadius.xl,
                        background : "#49a3f11c",
                        border: "1px solid #49a3f1"
                    }}
                    onClick={connectSource}
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                    height="3rem"
                >
                    <MDTypography variant="caption" fontWeight="medium" textTransform="uppercase" sx={{color: "#49a3f1"}}>
                        {loaderConnectSource
                            ? <CircularProgress size={20} color="info" />
                            : (sourceIntegrations.length == 0 ?  t("connect") : t("add-new-account"))
                        }
                    </MDTypography>
                    {blockConnectMultipleAccounts && sourceIntegrations.length > 0 && <MDBox component="img" src={premiumTag} alt="Brand" width="1rem" mx={0.8} />}
                </MDBox>
            </Grid>
            </Grid>
            </MDBox>
        </Card>
    );
}

export default IntegrationCard;
