import React, { useMemo, useState, useEffect } from "react";

import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import DataTable from "@/examples/Tables/DataTable";
import MDButton from "@/components/MDButton";
import DashboardLayout from "@/examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "@/examples/Navbars/DashboardNavbar";
import { Skeleton, Card, Grid, Tooltip, Typography, Box } from "@mui/material";
import EmptyChart from '@/components/EmptyChart';
import DatePickerAnt from '@/components/Filters/DatePickerFilter';
import { useTranslation } from "react-i18next";
import { useMaterialUIController, useCancellableAxios } from "@/context";
import dayjs from "dayjs";
import { getMetricFormatterFn } from "@/util";
import { toast } from "react-toastify";

const CampaignCell = ({ name, sku }) => {
  return (
    <Box sx={{
      display: 'flex',
      alignItems: 'center',
      width: '100%',
      maxWidth: '100%',
      overflow: 'hidden'
    }}>
      <MDBox
        display="flex"
        alignItems="center"
        pr={{ xs: 1, md: 2 }}
        sx={{
          cursor: "pointer",
          width: '100%',
          maxWidth: '100%',
          overflow: 'hidden'
        }}
      >
        <Box sx={{
          minWidth: 0, // Allow text to shrink
          overflow: 'hidden'
        }}>
          <Tooltip title={name} placement="top" arrow>
            <MDTypography
              color="info"
              variant="button"
              fontWeight="regular"
              display="block"
              sx={{
                fontSize: { xs: '0.75rem', md: '0.875rem' },
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                maxWidth: '100%'
              }}
            >
              {/* Mobile: shorter text, Desktop: longer text */}
              <Box component="span" sx={{ display: { xs: 'inline', md: 'none' } }}>
                {name.length > 15 ? name.substring(0, 15) + "..." : name}
              </Box>
              <Box component="span" sx={{ display: { xs: 'none', md: 'inline' } }}>
                {name.length > 30 ? name.substring(0, 30) + "..." : name}
              </Box>
            </MDTypography>
          </Tooltip>
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{
              fontSize: { xs: '0.6rem', md: '0.75rem' },
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              maxWidth: '100%',
              display: 'block'
            }}
          >
            ID: {sku}
          </Typography>

        </Box>
      </MDBox>
    </Box>
  );
};

const PageCell = ({ page }) => {
  const tooltipContent = (
    <Box sx={{ p: 1, maxWidth: 300 }}>
      <MDTypography variant="h6" color="white" mb={1}>
        {page.name}
      </MDTypography>

      {page.profile_info?.about && (
        <MDTypography variant="body2" color="white" mb={2} sx={{ opacity: 0.9 }}>
          {page.profile_info.about}
        </MDTypography>
      )}
    </Box>
  );

  return (
    <Box sx={{
      display: 'flex',
      alignItems: 'center',
      width: '100%',
      maxWidth: '100%',
      overflow: 'hidden'
    }}>
      <MDBox
        display="flex"
        alignItems="center"
        pr={{ xs: 1, md: 2 }}
        sx={{
          cursor: "pointer",
          width: '100%',
          maxWidth: '100%',
          overflow: 'hidden'
        }}
      >
        <Box sx={{
          minWidth: 0, // Allow text to shrink
          overflow: 'hidden'
        }}>
          <Tooltip
            title={tooltipContent}
            placement="top"
            arrow
            componentsProps={{
              tooltip: {
                sx: {
                  bgcolor: 'rgba(0, 0, 0, 0.9)',
                  '& .MuiTooltip-arrow': {
                    color: 'rgba(0, 0, 0, 0.9)',
                  },
                },
              },
            }}
          >
            <MDTypography
              color="info"
              variant="button"
              fontWeight="regular"
              display="block"
              sx={{
                fontSize: { xs: '0.75rem', md: '0.875rem' },
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                maxWidth: '100%'
              }}
            >
              {/* Mobile: shorter text, Desktop: longer text */}
              <Box component="span" sx={{ display: { xs: 'inline', md: 'none' } }}>
                {page.name.length > 15 ? page.name.substring(0, 15) + "..." : page.name}
              </Box>
              <Box component="span" sx={{ display: { xs: 'none', md: 'inline' } }}>
                {page.name.length > 30 ? page.name.substring(0, 30) + "..." : page.name}
              </Box>
            </MDTypography>
          </Tooltip>
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{
              fontSize: { xs: '0.6rem', md: '0.75rem' },
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              maxWidth: '100%',
              display: 'block'
            }}
          >
            ID: {page.id}
          </Typography>
        </Box>
      </MDBox>
    </Box>
  );
};

const FacebookCampaigns = () => {
  const [controller] = useMaterialUIController();
  const { selectedShop, selectedFilters = {} } = controller;
  const { start_date, end_date } = selectedFilters;
  const { t } = useTranslation();

  const [campaigns, setCampaigns] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loadingPages, setLoadingPages] = useState(false)
  const [pages, setPages] = useState([]);
  const axiosInstance = useCancellableAxios();

  const toggleCampaignStatus = async (campaign) => {
    const newStatus = campaign.campaign_status === 'ACTIVE' ? 'PAUSED' : 'ACTIVE';
    setLoading(true);
    try {
      const response = await axiosInstance.post('/api/facebook-campaigns/update', {
        account_id: campaign.account_id,
        campaign_id: campaign.campaign_id,
        status: newStatus
      })
      if (response.data && !response.data.error) {
        setCampaigns(prevCampaigns => prevCampaigns.map(c => c.campaign_id === campaign.campaign_id ? { ...c, campaign_status: newStatus } : c))
        toast.success('Campaign status updated successfully');
      }
    } catch (error) {
      console.error('Error updating campaign status:', error);
      toast.error('Error updating campaign status');
    } finally {
      setLoading(false);
    }
  }

  const fetchCampaigns = async () => {
    if (!start_date || !end_date) return;

    setLoading(true);
    try {
      const reqData = {
        start_date: dayjs(start_date).format("YYYY-MM-DD"),
        end_date: dayjs(end_date).format("YYYY-MM-DD"),
        selectedShop: selectedShop
      };

      const response = await axiosInstance.post('/api/facebook-campaigns', reqData)

      if (response.data && !response.data.error) {
        const allData = response.data.data?.items || []
        setCampaigns(allData)
      } else {
        setCampaigns([])
      }

    } catch (error) {
      setCampaigns([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCampaigns();
  }, [start_date, end_date, selectedShop]);

  const formattedData = useMemo(() => {

    let formatter = getMetricFormatterFn("absolute")
    return campaigns.map((campaign, index) => ({
      ...campaign,
      campaign_name_text: campaign.campaign_name,
      campaign_cell: (
        <CampaignCell
          name={campaign.campaign_name || ''}
          sku={campaign.campaign_id || 'N/A'}
        />
      ),
      impressions: formatter(campaign.impressions),
      clicks: formatter(campaign.clicks)
    }));
  }, [campaigns]);

  const getAllColumns = () => {
    let allColumns = [
      {
        Header: 'Campaign Name',
        accessor: 'campaign_cell',
        width: '20%',
        className: 'sticky-column', // Add sticky class
      },
      { Header: 'Campaign Spend', accessor: 'spend', width: '100px' },
      { Header: 'Campaign Impressions', accessor: 'impressions', width: '100px' },
      { Header: 'Campaign Clicks', accessor: 'clicks', width: '100px' },
      { Header: 'Campaign Revenue', accessor: 'website_purchase_value', width: '100px' },
      {
        Header: 'Campaign Status',
        accessor: 'campaign_status',
        width: '100px',
        Cell: ({ value }) => (
          <MDTypography
            variant="caption"
            color={value === 'ACTIVE' ? 'success' : 'secondary'}
            sx={{
              fontWeight: 'bold',
              textTransform: 'uppercase'
            }}
          >
            {value}
          </MDTypography>
        )
      },
      {
        Header: 'Campaign Actions',
        accessor: 'actions',
        width: '120px',
        Cell: ({ row }) => (
          <>
            {['ACTIVE', 'PAUSED'].includes(row.original.campaign_status) && (
              <MDButton
                // variant={row.original.campaign_status === 'ACTIVE' ? 'contained' : 'outlined'}
                variant="outlined"
                color={row.original.campaign_status === 'ACTIVE' ? 'secondary' : 'success'}
                size="small"
                onClick={() => toggleCampaignStatus(row.original)}
                sx={{ minWidth: '80px' }}
              >
                {row.original.campaign_status === 'ACTIVE' ? 'Pause' : 'Resume'}
              </MDButton>
            )}
          </>
        )
      }
    ]

    return allColumns
  }

  const skeletonTableData = useMemo(() => {
    const skeletonRows = Array.from({ length: 10 }, (_, index) => ({
      campaign_cell: <Skeleton variant="text" width={240} height={20} />,
      spend: <Skeleton variant="text" width={80} height={20} />,
      impressions: <Skeleton variant="text" width={80} height={20} />,
      clicks: <Skeleton variant="text" width={80} height={20} />,
      website_purchase_value: <Skeleton variant="text" width={80} height={20} />,
      campaign_status: <Skeleton variant="text" width={80} height={20} />,
      actions: <Skeleton variant="text" width={80} height={20} />
    }));

    return {
      columns: getAllColumns(),
      rows: skeletonRows
    };
  }, []);

  const displayTableData = useMemo(() => {
    return {
      columns: getAllColumns(),
      rows: formattedData
    };
  }, [formattedData]);

  const fetchPages = async () => {
    setLoadingPages(true);
    try {
      const reqData = {
        selectedShop: selectedShop
      };
      const response = await axiosInstance.post('/api/facebook-pages', reqData)


      if (response.data && !response.data.error) {
        const allData = response.data.data?.pages || []
        setPages(allData)
      } else {
        setPages([])
      }

    } catch (error) {
      setPages([]);
    } finally {
      setLoadingPages(false);
    }
  };

  useEffect(() => {
    fetchPages();
  }, [selectedShop]);

  const getAllPagesColumns = () => {
    let allColumns = [
      {
        Header: 'Page Name',
        accessor: 'page_cell',
        width: '20%',
        className: 'sticky-column', // Add sticky class
      },
      { Header: 'Category', accessor: 'category', width: '100px' },
      { Header: 'Fan Count', accessor: 'fan_count', width: '100px' },
      { Header: 'Followers', accessor: 'followers_count', width: '100px' },
      { Header: 'Day Impressions', accessor: 'day_impressions', width: '120px' },
      { Header: '7 Day Impressions', accessor: 'week_impressions', width: '140px' },
      { Header: '28 Day Impressions', accessor: 'month_impressions', width: '140px' }
    ]

    return allColumns
  }

  const skeletonPagesData = useMemo(() => {
    const skeletonRows = Array.from({ length: 10 }, (_, index) => ({
      page_cell: <Skeleton variant="text" width={240} height={20} />,
      category: <Skeleton variant="text" width={80} height={20} />,
      fan_count: <Skeleton variant="text" width={80} height={20} />,
      followers_count: <Skeleton variant="text" width={80} height={20} />,
      day_impressions: <Skeleton variant="text" width={80} height={20} />,
      week_impressions: <Skeleton variant="text" width={80} height={20} />,
      month_impressions: <Skeleton variant="text" width={80} height={20} />
    }));

    return {
      columns: getAllPagesColumns(),
      rows: skeletonRows
    };
  }, []);

  const displayPagesData = useMemo(() => {
    const getLatestInsightValue = (insights, period) => {
      const insight = insights?.find(insight => insight.period === period);
      if (insight?.values && insight.values.length > 0) {
        return insight.values[insight.values.length - 1].value;
      }
      return 0;
    };

    const formattedPages = pages.map((page) => {
      const dayImpressions = getLatestInsightValue(page.insights, 'day');
      const weekImpressions = getLatestInsightValue(page.insights, 'week');
      const monthImpressions = getLatestInsightValue(page.insights, 'days_28');

      return {
        ...page,
        page_cell: <PageCell page={page} />,
        followers_count: page.profile_info?.followers_count?.toLocaleString() || '0',
        day_impressions: dayImpressions.toLocaleString(),
        week_impressions: weekImpressions.toLocaleString(),
        month_impressions: monthImpressions.toLocaleString()
      };
    });

    return {
      columns: getAllPagesColumns(),
      rows: formattedPages
    };
  }, [pages]);

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <MDBox mb={3} mt={2}>
        <Card elevation={0} mb={4} my={4}>
          <Grid container spacing={3} className="report-config-panel" direction="row" justifyContent="flex-start" alignItems="center" px={2} pb={2}>
            <Grid item sx={{ minWidth: '200px' }}>
              <MDTypography variant="button" sx={{ fontSize: "13px" }}>
                {t("time-period")}
              </MDTypography>
              <DatePickerAnt report="product-performance" />
            </Grid>
          </Grid>
        </Card>
      </MDBox>
      <MDBox mt={1.5} mb={15}>
        <Card sx={{ overflow: "visible" }}>
          <MDBox px={1.6} pt={1} pb={3}>
            {/* Heading */}
            <MDBox mb={2} ml={3} mt={3}>
              <MDTypography variant="h5" fontWeight="medium">
                My Campaigns
              </MDTypography>
            </MDBox>

            {/* Table section */}
            {!loading && displayTableData.rows.length === 0 ? (
              <EmptyChart />
            ) : (
              <DataTable
                table={loading ? skeletonTableData : displayTableData}
                entriesPerPage={true}
                canSearch={true}
                showTotalEntries={true}
                isSorted={!loading}
                pagination={true}
                hiddenColumns={['campaign_name_text']}
              />
            )}

          </MDBox>
        </Card>
      </MDBox>
      <MDBox mt={1.5} mb={15}>
        <Card sx={{ overflow: "visible" }}>
          <MDBox px={1.6} pt={1} pb={3}>
            {/* Heading */}
            <MDBox mb={2} ml={3} mt={3}>
              <MDTypography variant="h5" fontWeight="medium">
                My Facebook Pages
              </MDTypography>
            </MDBox>

            {/* Table section */}
            {!loadingPages && displayPagesData.rows.length === 0 ? (
              <EmptyChart />
            ) : (
              <DataTable
                table={loadingPages ? skeletonPagesData : displayPagesData}
                entriesPerPage={false}
                canSearch={true}
                showTotalEntries={true}
                isSorted={!loading}
                pagination={false}
              />
            )}

          </MDBox>
        </Card>
      </MDBox>
    </DashboardLayout>
  )
}

export default FacebookCampaigns;