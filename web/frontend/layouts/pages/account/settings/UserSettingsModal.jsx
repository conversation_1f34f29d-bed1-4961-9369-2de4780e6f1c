import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import { tracker } from "@/context";

// @mui material components
import Dialog from "@mui/material/Dialog";
import Grid from "@mui/material/Grid";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import Divider from "@mui/material/Divider";
import Avatar from "@mui/material/Avatar";
import IconButton from "@mui/material/IconButton";

// @phosphor-icons/react imports
import { 
  XIcon as CloseIcon,
  UserIcon,
  BellIcon,
  LinkIcon,
  GearIcon,
  UsersIcon,
  ShieldIcon,
  IdentificationCardIcon as IdentityIcon,
  QuestionIcon,
  CreditCardIcon as BillingIcon,
  ChatCircleIcon as ChatIcon,
  LockKeyIcon as ChangePasswordIcon
} from '@phosphor-icons/react';

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";

// Settings page components
import ChangePassword from "@/layouts/pages/account/settings/components/ChangePassword";
import HelpSupport from "@/layouts/pages/account/settings/components/HelpSupport";
import Profile from "@/layouts/pages/account/settings/components/Profile";

// Context
import { useMaterialUIController } from "@/context";

const UserSettingsModal = NiceModal.create(({ onExit }) => {
  const modal = useModal();
  const [activeTab, setActiveTab] = useState("profile");
  const [controller] = useMaterialUIController();
  const { user, loginConfig } = controller;
  const { t } = useTranslation();

  // Track modal open
  useEffect(() => {
    tracker.event("User Settings Modal Opened", { source: "navbar_menu" });
  }, []);

  const handleClose = () => {
    tracker.event("User Settings Modal Closed", { source: "user_settings" });
    modal.hide();
    if (onExit) {
      onExit();
    }
  };

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    tracker.event("User Settings Tab Changed", { tab });
  };

  const renderSidebarItem = (id, label, icon, section = null) => {
    const isActive = activeTab === id;
    const IconComponent = icon;
    const translatedLabel = t(label) || label;
    
    return (
      <ListItem key={id} disablePadding>
        <ListItemButton
          selected={isActive}
          onClick={() => handleTabChange(id)}
        sx={{
            borderRadius: 1,
        mx: 0.75,
        mb: 0.5,
        py: 0.75,
        '&.Mui-selected': {
            backgroundColor: 'rgba(0, 0, 0, 0.06)',
            '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.1)',
            },
        },
        '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.04)',
        },
            }}
          >
            <ListItemIcon sx={{ minWidth: 32 }}>
              <IconComponent 
                size={18} 
                style={{ color: "#344767", fontWeight: '500'}}
              />
            </ListItemIcon>
            <ListItemText
              primary={
                <MDTypography 
                  variant="caption" 
                  color="dark"
                  sx={{ fontSize: '0.875rem', fontWeight: isActive ? '500' : '400' }}
                >
                  {translatedLabel}
                </MDTypography>
              }
            />
        </ListItemButton>
      </ListItem>
    );
  };

  const renderContent = () => {
    switch (activeTab) {
      case "profile":
        return <Profile />;
      case "change-password":
        return <ChangePassword />;
      case "support":
        return <HelpSupport onFinish={handleClose} />;
      default:
        return <MDBox >Not implemented</MDBox>;
    }
  };

  return (
    <Dialog
      open={modal.visible}
      onClose={handleClose}
      TransitionProps={{
        onExited: () => modal.remove(),
      }}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          height: '60vh',
          maxHeight: '60vh',
          borderRadius: '12px',
          overflow: 'hidden'
        }
      }}
    >
      <MDBox display="flex" height="100%">
                  {/* Left Sidebar */}
          <MDBox 
            width={210} 
            bgcolor={'#f9f8f7 !important'} 
            borderRight={1} 
            borderColor={'rgba(0,0,0,0.06)'}
            display="flex"
            flexDirection="column"
            sx={{
                backgroundColor: '#f9f8f7 !important',
            }}
          >

          {/* Navigation */}
          <MDBox flex={1} overflow="auto" py={1}>
            <List dense>
              {/* Account Section */}
              <MDBox mb={1.5}>
                <MDTypography 
                  variant="overline" 
                  sx={{ 
                    color: '#6b7280',
                    fontWeight: "600",
                    fontSize: '0.70rem',
                    px: 2,
                    py: 1,
                    textTransform: 'uppercase',
                    letterSpacing: '0.5px'
                  }}
                >
                  {t("account") || "Account"}
                </MDTypography>
                {renderSidebarItem("profile", t("profile"), UserIcon)}
                {renderSidebarItem("change-password", t("change-password"), ChangePasswordIcon)}
              </MDBox>

              <Divider sx={{ my: 1.5, mx: 2, borderColor: 'rgba(0,0,0,0.06)' }} />
{/* 
              <MDBox mb={1.5}>
                <MDTypography 
                  variant="overline" 
                  sx={{ 
                    color: '#6b7280',
                    fontWeight: "600",
                    fontSize: '0.70rem',
                    px: 2,
                    py: 1,
                    textTransform: 'uppercase',
                    letterSpacing: '0.5px'
                  }}
                >
                  {t("workspace") || "Workspace"}
                </MDTypography>
                {renderSidebarItem("general", t("general"), GearIcon)}
                {renderSidebarItem("people", t("people"), UsersIcon)}
              </MDBox> */}

              {/* <Divider sx={{ my: 1.5, mx: 2, borderColor: 'rgba(0,0,0,0.06)' }} /> */}

              {/* Support Section */}
              <MDBox mb={1.5}>
                <MDTypography 
                  variant="overline" 
                  sx={{ 
                    color: '#6b7280',
                    fontWeight: "600",
                    fontSize: '0.70rem',
                    px: 2,
                    py: 1,
                    textTransform: 'uppercase',
                    letterSpacing: '0.5px'
                  }}
                >
                  {t("help-center") || "Help Center"}
                </MDTypography>
                {renderSidebarItem("support", t("support"), QuestionIcon)}
              </MDBox>
            </List>
          </MDBox>
        </MDBox>

        {/* Right Content Area */}
        <MDBox flex={1} display="flex" flexDirection="column">
          {/* Header */}
          <MDBox 
            p={2} 
            borderBottom={1} 
            borderColor={'rgba(0,0,0,0.06)'}
            display="flex"
            alignItems="center"
            justifyContent="space-between"
          >
            <MDTypography variant="h6" fontWeight="medium" color="secondary">
              {t(activeTab) || activeTab}
            </MDTypography>
            <IconButton onClick={handleClose} size="small">
              <CloseIcon size={20} />
            </IconButton>
          </MDBox>

          {/* Content */}
          <MDBox flex={1} overflow="auto" p={2}>
            {renderContent()}
          </MDBox>
        </MDBox>
      </MDBox>
    </Dialog>
  );
});

export default UserSettingsModal;
