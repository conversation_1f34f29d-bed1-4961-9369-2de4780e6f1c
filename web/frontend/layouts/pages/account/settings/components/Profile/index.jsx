import React, { useState } from "react";
// @material-ui core components
import Card from "@mui/material/Card";
import Grid from "@mui/material/Grid";
import Autocomplete from "@mui/material/Autocomplete";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDButton from "@/components/MDButton";
import CircularProgress from "@mui/material/CircularProgress";

// Settings page components
import FormField from "@/layouts/pages/account/components/FormField";
import { useTranslation } from "react-i18next";
import { fetchShopConfig, tracker, useMaterialUIController, axiosInstance, fetchLoginConfig } from "@/context";
import { toast } from "react-toastify";

function Profile() {
  const [controller, dispatch] = useMaterialUIController();
  const { loginConfig, shopConfig } = controller;
  const { userData } = loginConfig;
  const { shop } = shopConfig;

  const { t } = useTranslation();
  const [fullName, setFullName] = useState(userData?.onboard_name ?? "");
  const [userRole, setUserRole] = useState(userData?.onboard_user_type ?? "");
  const [phone, setPhone] = useState(userData?.onboard_phone ?? "");
  const [saveLoader, setSaveLoader] = useState(false);

  // User role options from the screenshot
  const roleOptions = [
    { label: "Ad Buyer", value: "ad_buyer" },
    { label: "Agency Owner", value: "agency_owner" },
    { label: "Brand Owner", value: "brand_owner" },
    { label: "Marketing Manager", value: "marketing_manager" },
    { label: "Retention Marketer", value: "retention_marketer" },
    { label: "Operations", value: "operations" },
    { label: "Finance/Accounting", value: "finance_accounting" },
    { label: "Inventory/Supply Chain", value: "inventory_supply_chain" },
    { label: "Other", value: "other" }
  ];

  let canSave = (fullName != userData?.onboard_name && fullName != "")
    || userRole != userData?.onboard_user_type
    || phone != userData?.onboard_phone;

  const saveSettings = () => {
    if (saveLoader) {
      return;
    }

    setSaveLoader(true);
    
    const updateData = {
      onboard_name: fullName,
      onboard_user_type: userRole,
      onboard_phone: phone,
      onboard_email: userData?.email || userData?.onboard_email || ""
    };

    axiosInstance
      .post("/api/user/onboarding", updateData)
      .then(function (response) {
        if (response && response.data.status) {
          tracker.event("Profile Settings Updated", {
            name_updated: fullName != userData?.onboard_name,
            role_updated: userRole != userData?.onboard_user_type,
            phone_updated: phone != userData?.onboard_phone
          });
          toast.success(t("saved-successfully"));
        //   // Refresh user data
          fetchLoginConfig(dispatch, false);
        }
        setSaveLoader(false);
      })
      .catch((err) => {
        console.log("error", err);
        toast.error(t("something-went-wrong"));
        setSaveLoader(false);
      });
  };

  return (

      <MDBox component="form" p={3}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <FormField
              label={t("full-name")}
              value={fullName}
              onChange={(event) => {
                setFullName(event.target.value);
              }}
            />
          </Grid>
          
          <Grid item xs={12}>
            <Autocomplete
              onChange={(event, value) => {
                setUserRole(value?.value || "");
              }}
              options={roleOptions}
              value={roleOptions.find((option) => option.value === userRole) ?? null}
              getOptionLabel={(option) => {
                return option.label ?? "";
              }}
              isOptionEqualToValue={(option, value) => {
                return option.value === value.value;
              }}
              renderInput={(params) => (
                <FormField 
                  {...params} 
                  label={t("user-type-label")}
                  InputLabelProps={{ shrink: true }}
                />
              )}
            />
          </Grid>
          
          <Grid item xs={12}>
            <FormField
              disabled={true}
              label={t("work-email")}
              value={userData?.email || ""}
              inputProps={{ type: "email" }}
            />
          </Grid>
          
          <Grid item xs={12}>
            <FormField
              value={phone}
              onChange={(event) => {
                setPhone(event.target.value);
              }}
              label={t("phone-number")}
              placeholder="+40 735 631 620"
              inputProps={{ type: "tel" }}
            />
          </Grid>

        </Grid>
        
        {canSave && (
          <MDBox display="flex" justifyContent="flex-end" mt={3}>
            <MDButton size="medium" variant="gradient" color="dark" onClick={saveSettings}>
              {saveLoader ? <CircularProgress size={20} color="white" /> : t("save") || "Save"}
            </MDButton>
          </MDBox>
        )}
      </MDBox>

  );
}

export default Profile;
