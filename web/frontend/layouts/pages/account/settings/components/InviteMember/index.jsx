import React, { useEffect } from "react";
import { Link } from "react-router-dom";
import NiceModal from "@ebay/nice-modal-react";
import { useTranslation } from "react-i18next";
import { toast } from "react-toastify";
import { getAuth, sendPasswordResetEmail } from 'firebase/auth';
import { app } from '@/firebase-config';

// @mui material components
import Card from "@mui/material/Card";
import Icon from "@mui/material/Icon";
import CircularProgress from '@mui/material/CircularProgress';

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDButton from "@/components/MDButton";
import MDInput from "@/components/MDInput";

// Shared authentication components
import { useAuthentication } from "@/hooks/useAuthentication";
import AuthenticationForm from "@/components/AuthenticationForm";
import { getMessageForFirebaseError } from "@/layouts/authentication/util";

import { useMaterialUIController, setSelectedShop, tracker } from "@/context";
import { InviteMemberDialog } from "@/layouts/pages/account/settings/components/Members/invite-member";

// Shop flow identification component using shared authentication
function ShopFlowInvite() {
  const [controller, dispatch] = useMaterialUIController();
  const { loginConfig, selectedShop, shopConfig } = controller;
  const loginShop = loginConfig?.shop;
  const { t } = useTranslation();
  const customerEmail = shopConfig?.shop?.onboard_email || shopConfig?.shop?.customer_email;
  console.log("customerEmail", customerEmail);

  const handlePasswordReset = async () => {
    if (!auth.email || !auth.email.includes('@')) {
      toast.error(t("please-enter-email-first"));
      return;
    }
    
    try {
      const authInstance = getAuth(app);
      await sendPasswordResetEmail(authInstance, auth.email);
      toast.success(t("password-reset-email-sent"));
    } catch (error) {
      toast.error(getMessageForFirebaseError(error.code || error.message));
    }
  };

  // Initialize authentication hook 
  const auth = useAuthentication({
    onSuccess: () => {
      // Track successful authentication before reloading
      tracker.event("Workspace Setup Completed", { 
        shop: selectedShop || "unknown",
        flow_type: auth.flowType === "login" ? "existing_user" : auth.flowType === "register" ? "new_user" : "identifying_user",
        source: "invite_member_settings"
      });
      window.location.reload();
    },
    enableTracking: true,
    trackEvent: tracker.event,
    showSuccessToast: true
  });

  // Set email once if available from shopConfig
  if (customerEmail && !auth.email) {
    auth.setEmail(customerEmail);
  }

  // Set selected shop when email is identified
  useEffect(() => {
    if (auth.flowType && loginShop?.myshopify_domain) {
      setSelectedShop(dispatch, loginShop.myshopify_domain);
    }
  }, [auth.flowType, loginShop, dispatch]);

  return (
    <Card sx={{ width: "auto", margin: "auto" }}>
      <MDBox pt={4} pb={1} px={3}>
        {/* Header */}
        <MDBox textAlign="center" mb={4}>
          <MDTypography variant="h4" fontWeight="bold" mb={1}>
            {t("finish-setting-up-workspace")}
          </MDTypography>
          <MDTypography variant="body2" color="text" mb={1}>
            {t("connect-email-invite-team-description")}
          </MDTypography>
        </MDBox>

        {/* Content based on identification status */}
        {!auth.flowType ? (
          <MDBox>
            <MDBox mb={2}>
              <MDInput
                type="email"
                label={t("email")}
                value={auth.email}
                onChange={(e) => auth.setEmail(e.target.value)}
                fullWidth
                placeholder={t("email-placeholder")}
              />
            </MDBox>
            
            <MDBox mt={3}>
              <MDButton 
                variant="gradient" 
                color="info" 
                fullWidth 
                onClick={auth.handleEmailSubmit}
                disabled={auth.emailSubmitStart}
              >
                {auth.emailSubmitStart ? (
                  <CircularProgress size={20} color="inherit" />
                ) : (
                  t("continue")
                )}
              </MDButton>
            </MDBox>
          </MDBox>
        ) : (
          <MDBox>
            <AuthenticationForm
              auth={auth}
              emailPlaceholder="<EMAIL>"
              showEmailEditIcon={true}
            />
            
            {auth.flowType === 'login' && (
              <MDBox mt={2} textAlign="center">
                <Link 
                  onClick={handlePasswordReset}
                  style={{ 
                    cursor: 'pointer', 
                    textDecoration: 'underline',
                    color: 'inherit'
                  }}
                >
                  <MDTypography variant="caption" color="text">
                    {t("forgot-password")}
                  </MDTypography>
                </Link>
              </MDBox>
            )}
          </MDBox>
        )}
      </MDBox>
    </Card>
  );
}

// User flow invite component - opens existing workspace invite modal
function UserFlowInvite() {
  const [controller] = useMaterialUIController();
  const { loginConfig, selectedShop, selectedWorkspaceId } = controller;

  // Get current workspace and shop info - use the same logic as workspace members
  const activeWorkspace = (loginConfig?.workspaces ?? []).find(
    (w) => w.workspace_id === selectedWorkspaceId
  );

  // Get all shops from the active workspace (same as workspace members implementation)
  const workspaceShops = activeWorkspace?.shops ?? [];

  // Find the current shop from workspace shops
  const currentShop = workspaceShops.find(
    (shop) => shop.myshopify_domain === selectedShop
  );

  const handleInviteMember = () => {
    // Open the existing workspace invite modal with current shop context
    NiceModal.show(InviteMemberDialog, {
      shops: workspaceShops,
      workspaceId: selectedWorkspaceId,
      isSettingsFlow: true,
      currentShopDomain: selectedShop,
      fetchWorkspaceDetails: () => {
        // Just close the modal, no need to refresh the page
        // The invitation was successful and user will be notified via toast
      },
    });
  };

  return (
    <Card>
      <MDBox p={3}>
        <MDBox mb={3}>
          <MDTypography variant="h5" fontWeight="medium">
            Invite Team Member
          </MDTypography>
          <MDTypography variant="body2" color="text" mt={1}>
            Invite a team member to access <strong>{currentShop?.name}</strong>{" "}
            in your workspace.
          </MDTypography>
        </MDBox>

        <MDButton
          variant="gradient"
          color="info"
          onClick={handleInviteMember}
          startIcon={<Icon>person_add</Icon>}
        >
          Invite Team Member
        </MDButton>
      </MDBox>
    </Card>
  );
}

// Main component that decides which flow to show
function InviteMember() {
  const [controller] = useMaterialUIController();
  const { loginConfig } = controller;

  const isShopFlow = !loginConfig.userData || !loginConfig.userData.email;

  if (isShopFlow) {
    return <ShopFlowInvite />;
  } else {
    return <UserFlowInvite />;
  }
}

export default InviteMember;
