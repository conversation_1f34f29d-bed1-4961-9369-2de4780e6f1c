// @mui material components
import Card from "@mui/material/Card";
import Divider from "@mui/material/Divider";
import { Link } from "react-router-dom";

// @phosphor-icons/react imports
import { 
  ChatCircleIcon,
  QuestionIcon,
  CalendarIcon,
  GearIcon,
  ArrowRightIcon
} from '@phosphor-icons/react';

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";

import {useTranslation} from "react-i18next";
import {tracker} from "@/context";
import { useLocation } from "react-router-dom";

function HelpSupport({ onFinish }) {
  const {t} = useTranslation();
  const location = useLocation();
  const { pathname } = location;

  const actionButtonStyles = {
    "cursor" : "pointer",
    "& .material-icons-round": {
      transform: `translateX(0)`,
      transition: "all 200ms cubic-bezier(0.34,1.61,0.7,1.3)",
    },

    "&:hover .material-icons-round, &:focus .material-icons-round": {
      transform: `translateX(4px)`,
    },
  };

  const supportItems = [
    {
      icon: ChatCircleIcon,
      title: t("live-chat"),
      description: t("live-chat-description"),
      action: t("chat-now"),
      onClick: () => {
        tracker.intercom.show();
        onFinish();
      },
      type: "button"
    },
    {
      icon: QuestionIcon,
      title: t("help-center"),
      description: t("help-center-description"),
      action: t("see-more"),
      href: "https://help.datadrew.io",
      onClick: () => {
        tracker.mixpanel.track('Clicked HelpCenter', {page: pathname, source: "settings"});
        onFinish();
      },
      type: "link"
    },
    {
      icon: CalendarIcon,
      title: t("book-call"),
      description: t("book-a-call-description"),
      action: t("see-more"),
      to: "/book-call",
      onClick: () => {
        tracker.mixpanel.track('Clicked BookCall', {page: pathname, source: "settings"});
        onFinish();
      },
      type: "router"
    },
    {
      icon: GearIcon,
      title: t("support"),
      description: "<EMAIL>",
      action: t("write-to-us"),
      href: "mailto:<EMAIL>",
      onClick: () => {
        tracker.mixpanel.track('Clicked Support', {page: pathname, source: "settings_support_email"});
        onFinish();
      },
      type: "link"
    }
  ];

  const renderActionButton = (item) => {
    const commonProps = {
      variant: "button",
      color: "info",
      fontWeight: "regular",
      sx: {
        ...actionButtonStyles,
        fontSize: '0.75rem',
        textTransform: 'none',
        padding: '4px 8px',
        borderRadius: '4px',
        '&:hover': {
          backgroundColor: 'rgba(0,0,0,0.04)',
        }
      },
      onClick: item.onClick
    };

    if (item.type === "button") {
      return (
        <MDTypography {...commonProps}>
          {item.action}
          &nbsp;
          <ArrowRightIcon size={14} weight="bold" style={{ verticalAlign: "middle", display: "inline-block" }} />
        </MDTypography>
      );
    } else if (item.type === "link") {
      return (
        <MDTypography
          component="a"
          href={item.href}
          target="_blank"
          {...commonProps}
        >
          {item.action}
          &nbsp;
          <ArrowRightIcon size={14} weight="bold" style={{ verticalAlign: "middle", display: "inline-block" }} />
        </MDTypography>
      );
    } else if (item.type === "router") {
      return (
        <MDTypography
          component={Link}
          to={item.to}
          {...commonProps}
        >
          {item.action}
          &nbsp;
          <ArrowRightIcon size={14} weight="bold" style={{ verticalAlign: "middle", display: "inline-block" }} />
        </MDTypography>
      );
    }
  };

  return (
    <MDBox>
      <MDBox mb={2}>
        <MDTypography variant="body2" color="text" fontWeight="regular" sx={{ fontSize: '0.875rem', lineHeight: 1.4 }}>
          {t("support-description")}
        </MDTypography>
      </MDBox>

      <MDBox>
        {supportItems.map((item, index) => (
          <MDBox key={index}>
            <MDBox
              display="flex"
              justifyContent="space-between"
              alignItems="center"
              py={1.5}
              px={0}
            >
              <MDBox display="flex" alignItems="center" flex={1}>
                <MDBox 
                  textAlign="center" 
                  width={36}
                  height={36}
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  borderRadius="50%"
                  bgcolor="rgba(0,0,0,0.04)"
                  mr={1.5}
                >
                  <item.icon size={18} color="#344767" weight="regular" />
                </MDBox>
                <MDBox flex={1} display="flex" flexDirection="column" justifyContent="center">
                  <MDTypography 
                    display="block" 
                    variant="button" 
                    fontWeight="regular" 
                    color="dark"
                    mb={0.25}
                  >
                    {item.title}
                  </MDTypography>
                  <MDTypography 
                    variant="caption" 
                    color="text"
                  >
                    {item.description}
                  </MDTypography>
                </MDBox>
              </MDBox>
              <MDBox ml={1.5}>
                {renderActionButton(item)}
              </MDBox>
            </MDBox>
            {index < supportItems.length - 1 && (
              <Divider sx={{ borderColor: 'rgba(0,0,0,0.06)', my: 0.5 }} />
            )}
          </MDBox>
        ))}
      </MDBox>
    </MDBox>
  );
}

export default HelpSupport;
