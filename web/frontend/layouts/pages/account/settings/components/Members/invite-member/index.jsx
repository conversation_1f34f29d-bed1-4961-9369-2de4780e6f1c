import React, { useState } from 'react'
import { toast } from 'react-toastify'
import Dialog from '@mui/material/Dialog'
import { useNavigate } from 'react-router-dom'

// Material Dashboard 2 PRO React components
import Card from '@mui/material/Card'
import MDBox from '@/components/MDBox'
import MDTypography from '@/components/MDTypography'
import MDButton from '@/components/MDButton'
import bgImage from '@/assets/images/bg-sign-in-basic.jpeg'
import BasicLayout from '@/layouts/authentication/components/BasicLayout'
import NiceModal, { useModal } from '@ebay/nice-modal-react'
import MDInput from '@/components/MDInput'
import CircularProgress from '@mui/material/CircularProgress'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'

import { useTranslation } from 'react-i18next'

import Select from '@mui/material/Select'
import MenuItem from '@mui/material/MenuItem'
import FormControl from '@mui/material/FormControl'
import FormGroup from '@mui/material/FormGroup'
import FormControlLabel from '@mui/material/FormControlLabel'
import Checkbox from '@mui/material/Checkbox'
import { useCancellableAxios } from '@/context'
import { WORKSPACE_ROLE_ADMIN, WORKSPACE_ROLE_MEMBER } from '@/constants';

function InviteTeamMember(props) {
  return (
    <BasicLayout image={bgImage}>
      <MDBox
        height='100vh'
        display='flex'
        flexDirection='column'
        justifyContent='center'
        alignItems='center'
      >
        <InviteTeamMemberRoot {...props} signOutOption={true} />
      </MDBox>
    </BasicLayout>
  )
}

export const InviteMemberDialog = NiceModal.create(props => {
  const modal = useModal()
  return (
    <Dialog
      open={modal.visible}
      onClose={modal.hide}
      TransitionProps={{
        onExited: () => modal.remove()
      }}
    >
      <InviteTeamMemberRoot {...props} signOutOption={false} />
    </Dialog>
  )
})

function InviteTeamMemberRoot(props) {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const [name, setName] = useState('')
  const [email, setEmail] = useState('')
  const [role, setRole] = useState('Member')
  const [selectedShops, setSelectedShops] = useState([])
  const [loading, setLoading] = useState(false)

  const axiosInstance = useCancellableAxios()

  const { shops, workspaceId, fetchWorkspaceDetails, isSettingsFlow = false, currentShopDomain } = props

  // For settings flow, filter shops to only the current shop
  const filteredShops = React.useMemo(() => {
    if (isSettingsFlow && currentShopDomain) {
      // Filter to only the current shop for settings flow
      return shops.filter(shop => shop.myshopify_domain === currentShopDomain);
    }
    return shops; // Return all shops for workspace flow
  }, [shops, isSettingsFlow, currentShopDomain]);

  // For settings flow, auto-select the current shop
  React.useEffect(() => {
    if (isSettingsFlow && filteredShops.length > 0) {
      setSelectedShops([filteredShops[0].shop_id]);
    }
  }, [isSettingsFlow, filteredShops]);

  const handleShopSelection = shopId => {
    if (selectedShops.includes(shopId)) {
      setSelectedShops(prev => prev.filter(id => id !== shopId))
    } else {
      setSelectedShops(prev => [...prev, shopId])
    }
  }

  const handleEmailChange = (e) => {
    setEmail(e.target.value);
  };

  const handleSubmit = async ev => {
    ev.preventDefault()

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      toast.error(t('Please enter a valid email address'))
      return
    }

    setLoading(true)
    try {
      // Use Member role and current shop for settings flow
      const finalRole = isSettingsFlow ? WORKSPACE_ROLE_MEMBER : role;
      const finalShopIds = isSettingsFlow
        ? (filteredShops.length > 0 ? [filteredShops[0].shop_id] : [])
        : (role === WORKSPACE_ROLE_ADMIN ? shops.map(s => s.shop_id) : [...selectedShops]);

      await axiosInstance
        .post('/api/send-invitation', {
          name,
          email,
          role: finalRole,
          workspace_id: workspaceId,
          shop_ids: finalShopIds
        })
        .then(res => {
          if (res.data.status === true) {
            // Only refresh workspace details on successful invitation
            if (fetchWorkspaceDetails) {
              fetchWorkspaceDetails();
            }
            toast.success(t('send-invitation-success'));
            NiceModal.hide(InviteMemberDialog)
          } else {
            // Show specific error message if available
            toast.error(res.data.error || t('send-invitation-failed'))
          }
        })
        .catch(err => {
          console.error(err)
          toast.error(t('send-invitation-failed'))
        })
    } catch (error) {
      console.error('Error inviting team member:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card>
      <MDBox m={4} display='flex' flexDirection='column' alignItems='center' justifyContent='center'>
        <MDTypography variant='h4' gutterBottom>
          {t('invite-team-member-heading')}
        </MDTypography>
        <MDTypography variant="body2" textAlign='center' justifyContent='center'>
          {isSettingsFlow
            ? `Invite a team member to access ${filteredShops[0]?.shop_name || filteredShops[0]?.name || 'this shop'} in your workspace.`
            : t('invite-team-member-description')
          }
        </MDTypography>


        <MDBox
          component='form'
          role='form'
          onSubmit={handleSubmit}
          width='100%'
          mt={4}
        >
          <MDBox mb={4}>
            <MDInput
              fullWidth
              label={t('name')}
              // value={name}
              onChange={e => setName(e.target.value)}
            />
          </MDBox>
          <MDBox mb={4}>
            <MDInput
              fullWidth
              type='email'
              label={t('email')}
              value={email}
              onChange={handleEmailChange}
            />
          </MDBox>
          {/* Hide role selection for settings flow */}
          {!isSettingsFlow && (
            <MDBox mb={4}>
              <FormControl fullWidth sx={{ minWidth: '100%', height: '56px' }}>
                <Select
                  value={role || ''}
                  onChange={e => setRole(e.target.value)}
                  displayEmpty
                  IconComponent={ExpandMoreIcon}
                  sx={{
                    height: '48px',
                    display: 'flex',
                    alignItems: 'center',
                    '& .MuiSelect-icon': {
                      marginRight: '10px',
                      display: 'block !important'
                    },
                  }}
                >
                  <MenuItem value={WORKSPACE_ROLE_ADMIN}>{t('admin')}</MenuItem>
                  <MenuItem value={WORKSPACE_ROLE_MEMBER}>{t('member')}</MenuItem>
                </Select>
              </FormControl>
            </MDBox>
          )}

          {/* Hide shop selection for settings flow */}
          {!isSettingsFlow && role === 'Member' && (
            <MDBox mb={4}>
              <MDTypography variant='button'>{t('select-shops')}</MDTypography>
              <FormGroup>
                {shops.map(shop => (
                  <FormControlLabel
                    key={shop.shop_id}
                    control={
                      <Checkbox
                        checked={selectedShops.includes(shop.shop_id)}
                        onChange={() => handleShopSelection(shop.shop_id)}
                      />
                    }
                    label={shop.shop_name || shop.name}
                  />
                ))}
              </FormGroup>
            </MDBox>
          )}
          {!isSettingsFlow && role === WORKSPACE_ROLE_ADMIN && (
            <MDBox mb={4}>
              <MDTypography variant="body2" textAlign='center' justifyContent='center' sx={{ color: 'text.secondary', fontSize: '0.8rem' }}>{t('invite-admin-disclaimer')}</MDTypography>
            </MDBox>
          )}
          <MDBox mt={3}>
            <MDButton
              fullWidth
              color='info'
              type='submit'
              size='large'
              disabled={loading}
            >
              {loading ? (
                <CircularProgress size={20} color='white' />
              ) : (
                t('send-invite')
              )}
            </MDButton>
          </MDBox>

          {/* Footer info strip for settings flow */}
          {isSettingsFlow && (
            <MDBox
              mt={3}
              p={2.5}
              sx={{
                backgroundColor: "#f8f9fa",
                borderRadius: 2,
                border: "1px solid #e9ecef",
                textAlign: "center"
              }}
            >
              <MDBox display="flex" flexDirection="column" alignItems="center" gap={1}>
                <MDTypography variant="body2" color="text" fontWeight="medium">
                  Need to manage access to multiple shops?
                </MDTypography>
                <MDButton
                  variant="outlined"
                  color="info"
                  size="small"
                  onClick={() => {
                    NiceModal.hide(InviteMemberDialog);
                    navigate("/workspace-settings");
                  }}
                  sx={{
                    fontSize: '0.8rem',
                    px: 2,
                    py: 0.5,
                    borderRadius: 1.5
                  }}
                >
                  Manage Workspace Members
                </MDButton>
              </MDBox>
            </MDBox>
          )}
        </MDBox>
      </MDBox>
    </Card>
  )
}

export default InviteTeamMember
