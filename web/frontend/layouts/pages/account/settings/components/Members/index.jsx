import { useMemo } from "react";
import { toast } from 'react-toastify';
import { useState } from "react";
import Popper from "@mui/material/Popper";
import Paper from "@mui/material/Paper";
import ClickAwayListener from "@mui/material/ClickAwayListener";
import ConfirmationDialog from '@/components/ConfirmationDialog';
import Card from "@mui/material/Card";
import MDButton from "@/components/MDButton";
import MDAvatar from "@/components/MDAvatar";
import Icon from "@mui/material/Icon";
import DeleteIcon from '@mui/icons-material/Delete';
import DataTable from "@/examples/Tables/DataTable";
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import NiceModal from '@ebay/nice-modal-react';
import PersonTwoTone from '@mui/icons-material/PersonTwoTone';
import { useCancellableAxios, useMaterialUIController } from "@/context";
import { useTranslation } from "react-i18next";
import PersonAddTwoToneIcon from '@mui/icons-material/PersonAddTwoTone';
import { InviteMemberDialog } from './invite-member';
import { stringToColor } from "@/util";
import { EditMemberDialog } from './edit-member';
import { Chip } from "@mui/material";
import { WORKSPACE_ROLE_OWNER, WORKSPACE_ROLE_ADMIN } from "@/constants";

function EmailCell({ text, email, hasActions, isInvited = false, isCurrentUser = false }) {
    const EmailContent = () => (
        <MDBox display="flex" alignItems="center" pr={2}>
            <MDBox mr={2}>
                <MDAvatar sx={{ bgcolor: stringToColor(text) }} size="md">
                    <PersonTwoTone fontSize="small" />
                </MDAvatar>
            </MDBox>
            <MDBox>
                <MDTypography variant="button" fontWeight="medium" sx={{ maxWidth: hasActions ? "150px" : "200px" }} >
                    {text}&nbsp;
                </MDTypography>
                {isCurrentUser && <Chip label="Current User" variant="outlined" size="small" sx={{ ml: 1, fontSize: "0.6rem", height: "18px", px: 0.5, borderColor: "green", color: "green" }} />}
                {isInvited && <Chip label="Invited" variant="outlined" size="small" sx={{ ml: 1, fontSize: "0.6rem", height: "18px", px: 0.5, borderColor: "darkgrey", color: "darkgrey" }} />}
                <MDBox>
                    <MDTypography variant="body2" color="secondary"
                        sx={{
                            fontSize: "0.75rem",
                            fontWeight: 300,
                            lineHeight: 1.25,
                            letterSpacing: "0.00938em",
                            paddingTop: "0.25em",
                        }}
                    >
                        {email}&nbsp;
                    </MDTypography>
                </MDBox>
            </MDBox>
        </MDBox>
    );

    return isInvited ? (
        <div style={{ color: "grey", opacity: 0.6 }}>
            <EmailContent />
        </div>
    ) : (
        <EmailContent />
    );
}

function RoleCell({ text, isCurrentUser = false, isInvited = false, workspaceUser = null, workspaceShops = null, fetchWorkspaceDetails = null }) {
    if (isCurrentUser || isInvited) {
        return (
            <PlainTextCell text={text} isInvited={isInvited} />
        )
    }

    const handleEditMember = () => {
        NiceModal.show(EditMemberDialog, {
            workspaceUser: workspaceUser,
            workspaceShops: workspaceShops,
            fetchWorkspaceDetails: fetchWorkspaceDetails
        })
    }

    return (
        <MDBox display="flex" alignItems="center" pr={2}>
            <MDButton color="secondary" variant="text" component="a" onClick={() => handleEditMember()} sx={{ fontSize: "13px", padding: "0px" }} size="small" target="_blank" rel="noreferrer">
                <MDTypography sx={{ padding: "0px" }} variant="button" fontWeight="medium" >
                    {text}&nbsp;<Icon fontSize="medium">edit_outlined</Icon>
                </MDTypography>
            </MDButton>
        </MDBox>
    )
}

function PlainTextCell({ text, isInvited = false }) {
    const TextContent = () => (
        <MDBox display="flex" alignItems="center" pr={2}>
            <MDTypography
                variant="button"
                fontWeight="medium"
            >
                {text}&nbsp;
            </MDTypography>
        </MDBox>
    );

    return isInvited ? (
        <div style={{ color: "grey", opacity: 0.6 }}>
            <TextContent />
        </div>
    ) : (
        <TextContent />
    );
}

function StoresCell(props) {
    const { t } = useTranslation();
    const { role, shop_ids = [], workspace_shops = [] } = props || {};

    // Handle both array and comma-separated string formats
    const safeShopIds = Array.isArray(shop_ids) 
        ? shop_ids 
        : typeof shop_ids === 'string' 
            ? shop_ids.split(',').map(id => id.trim())
            : [];

    const text =
        [WORKSPACE_ROLE_OWNER, WORKSPACE_ROLE_ADMIN].includes(role)
            ? t("all-stores")
            : safeShopIds.length === 1
                ? `1 ${t("store")}`
                : `${safeShopIds.length} ${t("stores")}`;

    let filteredShops = workspace_shops && workspace_shops.length > 0 ?
        workspace_shops.filter(shop => safeShopIds.includes(shop.shop_id)) : [];

    const [anchorEl, setAnchorEl] = useState(null);

    const handleMouseEnter = (event) => {
        if (filteredShops.length > 0) {
            setAnchorEl(event.currentTarget);
        }
    };

    const handleMouseLeave = () => {
        setAnchorEl(null);
    };

    const open = Boolean(anchorEl);

    return (
        <>
            <MDTypography
                variant="button"
                fontWeight="medium"
                sx={{ cursor: "pointer", color: "inherit", textDecoration: "none" }}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
            >
                {text}
            </MDTypography>

            <Popper open={open} anchorEl={anchorEl} placement="bottom-start">
                <ClickAwayListener onClickAway={handleMouseLeave}>
                    <Paper sx={{ p: 1, borderRadius: 2, boxShadow: 3 }}>
                        <MDBox>
                            {filteredShops.length > 0 ? (
                                filteredShops.map(shop => (
                                    <MDTypography key={shop.id} variant="body2" sx={{ p: 0.5 }}>
                                        {shop.name}
                                    </MDTypography>
                                ))
                            ) : (
                                <MDTypography variant="body2" sx={{ p: 0.5 }}>
                                    No shops available
                                </MDTypography>
                            )}
                        </MDBox>
                    </Paper>
                </ClickAwayListener>
            </Popper>
        </>
    );
}


function WorkspaceMembersRoot({ workspaceDetails, fetchWorkspaceDetails }) {

    const { t } = useTranslation();
    const { users, pending_invitations, shops } = workspaceDetails || {};
    const axiosInstance = useCancellableAxios();

    const [controller, dispatch] = useMaterialUIController();
    const { loginConfig, selectedWorkspaceId } = controller;

    if (!shops || shops.length == 0) {
        return null
    }

    // Make sure users is an array before using find/filter
    const currentWorkspaceUser = Array.isArray(users) ?
        users.find(wu => wu.user_id === loginConfig.user?.uid) :
        undefined;

    // Filter out the current user from the list to display them first
    let filteredWorkspaceUsers = Array.isArray(users) ?
        users.filter(wu => wu.user_id !== loginConfig.user?.uid) :
        [];

    if (currentWorkspaceUser) {
        filteredWorkspaceUsers = [currentWorkspaceUser, ...filteredWorkspaceUsers];
    }

    const revokeInvitation = (pending_invitation) => {
        NiceModal.show(ConfirmationDialog, {
            title: t("are-you-sure"),
            message: t("revoke-invitation-warning"),
            confirmColor: "error",
            onConfirm: () => {
                axiosInstance.post('/api/update-invitation', {
                    invitation_id: pending_invitation.id,
                    status: "revoked"
                }).then((res) => {
                    if (res.data.status) {
                        fetchWorkspaceDetails();
                        toast.success(t("revoke-invitation-success"));
                    } else {
                        toast.error(t("revoke-invitation-failed"));
                    }
                }).catch((err) => {
                    console.error(err);
                    toast.error(t("revoke-invitation-failed"));
                })

                NiceModal.hide(ConfirmationDialog);
            },
            onCancel: () => {
                NiceModal.hide(ConfirmationDialog);
            }
        })
    }

    let dataTableData = useMemo(() => {

        const hasActions = Array.isArray(pending_invitations) && pending_invitations.length > 0;

        const allRows = [
            ...filteredWorkspaceUsers.map(wu => ({
                email: <EmailCell text={wu.user_name ? wu.user_name : wu.user_email} hasActions={hasActions} isCurrentUser={wu.user_id === loginConfig.user?.uid} />,
                role: <RoleCell text={wu.role} workspaceUser={wu} workspaceShops={shops} fetchWorkspaceDetails={fetchWorkspaceDetails} isCurrentUser={wu.user_id === loginConfig.user?.uid} />,
                stores: <StoresCell role={wu.role?.toLowerCase()} shop_ids={wu.shop_ids} workspace_shops={shops} />,
                status: <PlainTextCell text={wu.status == 1 ? t("active") : t("inactive")} />,
                ...hasActions ? [{ action: null }] : []
            })),
            ...hasActions ? pending_invitations.map(pi => ({
                email: <EmailCell text={pi.name} email={pi.email} hasActions={hasActions} isInvited={true} />,
                role: <RoleCell text={pi.role} isInvited={true} />,
                stores: <StoresCell role={pi.role?.toLowerCase()} shop_ids={pi.shop_ids} workspace_shops={shops} />,
                status: <PlainTextCell text={t("invite-sent")} isInvited={true} />,
                action: <MDButton
                    variant="outlined"
                    color="dark"
                    size="small"
                    onClick={() => revokeInvitation(pi)}
                >
                    <DeleteIcon color="dark" fontSize="20px" /> &nbsp;
                    {t("revoke")}
                </MDButton>
            })) : []
        ];

        return {
            columns: [
                { Header: t("name-email"), accessor: "email", align: "left" },
                { Header: t("role"), accessor: "role", align: "left" },
                { Header: t("access"), accessor: "stores", align: "left" },
                // { Header: t("status"), accessor: "status", align: "left" },
                ...hasActions ? [{ Header: t("action"), accessor: "action", align: "center" }] : []
            ],
            rows: allRows
        };
    }, [filteredWorkspaceUsers, pending_invitations, shops]);

    return (
        <Card sx={{ boxShadow: "none" }}>

        <MDBox pt={1} pb={2} px={2} lineHeight={1.25}>
            <DataTable
                table={dataTableData}
                entriesPerPage={false}
                showTotalEntries={false}
                isSorted={false}
                />
        </MDBox>
    </Card>
    );
}

function WorkspaceMembers(props) {
    const { t } = useTranslation();
    const [controller] = useMaterialUIController();
    const { loginConfig, selectedWorkspaceId } = controller;
    const { workspaceDetails } = props;
    const { shops } = workspaceDetails || {};

    const inviteTeamMember = () => {
        NiceModal.show(InviteMemberDialog, {
            shops: shops,
            workspaceId: selectedWorkspaceId,
            fetchWorkspaceDetails: props.fetchWorkspaceDetails
        })
    }

    return (
        <Card id="workspace-members">
            <MDBox 
                p={3} 
                lineHeight={1}
                display="flex"
                justifyContent="space-between"
                alignItems={{ xs: "flex-start", sm: "center" }}
                flexDirection={{ xs: "column", sm: "row" }}
            >
                <MDBox>
                    <MDBox mb={1}>
                        <MDTypography variant="h5">
                            {t("members-settings-title")}
                        </MDTypography>
                    </MDBox>
                    <MDTypography variant="button" color="text">
                        {t("members-settings-description")}
                    </MDTypography>
                </MDBox>
                {[WORKSPACE_ROLE_OWNER, WORKSPACE_ROLE_ADMIN].includes(loginConfig?.userData?.role) && (
                    <MDBox
                        display="flex"
                        justifyContent="flex-end"
                        alignItems="center"
                        width={{ xs: "100%", sm: "auto" }}
                        mt={{ xs: 2, sm: 0 }}
                    >
                        <MDButton
                            variant="outlined"
                            color="dark"
                            size="small"
                            onClick={inviteTeamMember}
                        >
                            <PersonAddTwoToneIcon color="dark" sx={{ fontSize: "20px !important" }} /> &nbsp;
                            {t("invite-team-member")}
                        </MDButton>
                    </MDBox>
                )}
            </MDBox>
            <WorkspaceMembersRoot {...props} />
        </Card>
    );
}

export default WorkspaceMembers;
