import { useEffect, useMemo, useState } from "react";
import MuiLink from "@mui/material/Link";
import {Link} from "react-router-dom";

// @mui material components
import Card from "@mui/material/Card";
import Grid from "@mui/material/Grid";
import Icon from "@mui/material/Icon";
import Tooltip from "@mui/material/Tooltip";
import Divider from "@mui/material/Divider";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDAvatar from "@/components/MDAvatar";
import MDInput from "@/components/MDInput";
import MDButton from "@/components/MDButton";
import IdentificationModal from '@/components/IdentificationModal';

// Images
import logoFacebook from "@/assets/images/facebook-logo.png";
import klaviyoLogo from '@/assets/images/logos/klaviyo-logo.svg';
import logoShopify from "@/assets/images/shopify-logo.svg";
import {useTranslation} from "react-i18next";

import NiceModal from '@ebay/nice-modal-react';
import DisconnectDialog from "@/layouts/integrations/components/DisconnectDialog";
import CircularProgress from "@mui/material/CircularProgress";
import {toast} from "react-toastify";

// Material Dashboard 2 PRO React components
import { useMaterialUIController, tracker, useCancellableAxios } from "@/context";

import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import { SOURCE_FB, SOURCE_KLAVIYO } from "@/layouts/dashboards/metrics/metadata";


function SingleFacebookAccount({integration, disconnectSource, loaderDisconnectSource, accountSelector, accountOptions}) {

  const {t} = useTranslation();
  const [controller] = useMaterialUIController();
  const { darkMode } = controller;

  let fbConnected = integration.connected ?? false;
  let seeFBDashboard = integration.synced ?? false;
  let fbConfiguredAccountId = integration.source_config_id ?? "";
  let fbAccountName = !!integration.source_config_name ? integration.source_config_name : t("new");

  return (
    <MDBox ml={2} pl={6} pt={2} lineHeight={1}>
      <MDBox bgColor={darkMode ? "grey-900" : "grey-100"} borderRadius="lg">
        <MDBox>
        <MDBox
          display="flex"
          justifyContent="space-between"
          alignItems={{ xs: "flex-start", sm: "center" }}
          flexDirection={{ xs: "column", sm: "row" }}
          py={1}
          pl={{ xs: 1, sm: 2 }}
          pr={1}
        >
          {accountOptions.length == 1 && <MDBox mb={{ xs: 1, sm: 0 }} lineHeight={0}>
              <MDTypography variant="h6" fontWeight="medium">
                {fbAccountName == "" ? "-NA-" : fbAccountName}
              </MDTypography>
          </MDBox>}

          {accountOptions.length != 1 &&  <MDBox mb={{ xs: 1, sm: 0 }} lineHeight={0}>
            <Grid item>
              {accountSelector}
            </Grid>
          </MDBox>}
          <MDBox
            display="flex"
            alignItems={{ xs: "flex-start", sm: "center" }}
            flexDirection={{ xs: "column", sm: "row" }}
          >
            <MDBox
              display="flex"
              alignItems="center"
              justifyContent="flex-end"
              width={{ xs: "100%", sm: "auto" }}
              mt={{ xs: 1, sm: 0 }}
            >
              <MDButton color="info" variant="text" component={Link} to={`/acquisition/ads/facebook?request_id=${integration.request_id ?? ""}`} sx={{fontSize:"12px"}} size="small" fontWeight="regular">
              {seeFBDashboard ? t("view-dash") : (fbConnected ? t("finish-fb-connection") : t("connect-your-account"))}&nbsp;
                <Icon fontSize="medium">arrow_outward</Icon>
            </MDButton>
            </MDBox>
          </MDBox>
        </MDBox>
        <Divider style={{margin:0}} />
        {!!fbConfiguredAccountId && <MDBox
          display="flex"
          justifyContent="space-between"
          alignItems={{ xs: "flex-start", sm: "center" }}
          flexDirection={{ xs: "column", sm: "row" }}
          py={1}
          pl={{ xs: 1, sm: 2 }}
          pr={1}
        >
        <MDBox display="flex" flexDirection="column">
          <MDTypography variant="button" fontWeight="medium" color="text">
            {t("ad-account-id")}
          </MDTypography>
          <MDTypography variant="caption" color="text" fontWeight="light">
            <MuiLink
                color="info"
                href="https://www.facebook.com/business/help/****************"
                style={{textDecoration: "underline"}}
                target="_blank"><u>{t("find-fb-account")}</u></MuiLink>
          </MDTypography>
          </MDBox>
          <MDBox width={{ xs: "100%", sm: "25%", md: "25%" }} mt={{ xs: 1, sm: 0 }} display="flex" justifyContent="flex-end">
            <Tooltip title="Copy" placement="top">
              <MDInput size="small" value={fbConfiguredAccountId} disabled={true} />
            </Tooltip>
          </MDBox>
        </MDBox>}
        </MDBox>
          <MDBox lineHeight={0} display="flex" flexDirection="row" justifyContent="flex-end" py={1}>
            <MDButton
              variant="text"
              color="secondary"
              size="small"
              onClick={() => {disconnectSource(SOURCE_FB, integration.request_id ?? "")}} >
                {loaderDisconnectSource == SOURCE_FB
                    ? <CircularProgress size={20} color="error" />
                    : t("disconnect-btn")
                }
          </MDButton>
          </MDBox>
      </MDBox>
    </MDBox>
  )
}

function Accounts() {

  const {t} = useTranslation();
  const [controller] = useMaterialUIController();
  const { loginConfig, integrations } = controller;
  const [loaderDisconnectSource, setLoaderDisconnectSource] = useState("");
  const [selectedAccount, setSelectedAccount] = useState("");
  const loader = integrations.loading ?? false;

  let selectedIntg = {}
  const isUserFlow = loginConfig.user && loginConfig.user.email;

  let klaviyoIngs = SOURCE_KLAVIYO in integrations ? integrations[SOURCE_KLAVIYO] : [];
  let klaviyoIng = klaviyoIngs.length > 0 ? klaviyoIngs[0] : {};
  let intgs = SOURCE_FB in integrations ? integrations[SOURCE_FB] : [];

  let accountOptions = intgs.map((intg, ind) => {
    let fbConfiguredAccountId = intg.source_config_id ?? "";
    let fbAccountName = !!intg.source_config_name ? intg.source_config_name : t("new");
    return {
      label : !!fbConfiguredAccountId ? `${fbAccountName} (${fbConfiguredAccountId})` : fbAccountName,
      value : intg.request_id
    }
  })

  useEffect(() => {
    if (intgs.length > 0) {
      setSelectedAccount(intgs[0].request_id);
    }
  }, [intgs])

  intgs.forEach((intg) => {
    if (intg.request_id == selectedAccount) {
      selectedIntg = intg
    }
  })

  const disconnectSource = (source_type, request_id) => {

    if (loaderDisconnectSource === source_type) {
        return
    }

    if (!request_id) {
        toast.error(t("something-went-wrong"));
        return
    }

    setLoaderDisconnectSource(source_type);
    NiceModal.show(DisconnectDialog, {
        sourceType : source_type,
        request_id: request_id,
        onFinish : () => {
            tracker.event("Source Disconnected", {
              source_type : source_type
            })
            setLoaderDisconnectSource("");
        }
    })
  }

  const addNewStore = () => {
    NiceModal.show(IdentificationModal, {})
  }

  const accountSelector = (
    <FormControl variant="standard" size="medium" fullWidth sx={{ m: 1, minWidth: 120, ml: 0 }}>
      <Select
        value={selectedAccount}
        onChange={(e) => setSelectedAccount(e.target.value)}
        pl={0}
      >
        {accountOptions.map((option, ind) => { 
          return <MenuItem key={ind} value={option.value}><MDBox p={1} pl={0}>{option.label}</MDBox></MenuItem>
        })}
      </Select>
    </FormControl>
  );

  return (
    <Card id="accounts">
      <MDBox p={3} lineHeight={1}>
        <MDBox mb={1}>
          <MDTypography variant="h5">
            {t("connected-accounts")}
          </MDTypography>
        </MDBox>
        <MDTypography variant="button" color="text">
          {t("manage-integ")}
        </MDTypography>
      </MDBox>
      {loader && <MDBox display="flex" justifyContent="center" alignItems="center" height="200px">
        <CircularProgress size={30} color="primary" /> 
      </MDBox>}
      {!loader && <MDBox pt={0} pb={3} px={2}>
        {!isUserFlow && <MDBox
          bgColor="light"
          p={2}
          borderRadius="lg"
          display="flex"
          justifyContent="space-between"
          alignItems={{ xs: "flex-start", sm: "center" }}
          flexDirection={{ xs: "column", sm: "row" }}
        >
          <MDBox display="flex" alignItems="center">
            <MDAvatar src={logoShopify} alt="shopify logo" variant="rounded" size="sm" />
            <MDBox ml={2} lineHeight={0}>
              <MDTypography variant="h6" fontWeight="medium" color="dark">
                {t("managing-multi-stores")}
              </MDTypography>
              <MDTypography variant="button" color="dark">
                {t("multi-store-view")}
              </MDTypography>
            </MDBox>
          </MDBox>
          <MDBox
            display="flex"
            justifyContent="flex-end"
            alignItems="center"
            width={{ xs: "100%", sm: "auto" }}
            mt={{ xs: 1, sm: 0 }}
          >
            <MDBox lineHeight={0} mx={2}>
              <MDButton
                variant="outlined"
                color="dark"
                size="small"
                onClick={addNewStore} >
                  {t("add-new-store")}
              </MDButton>
            </MDBox>
          </MDBox>
        </MDBox>}
        <Divider/>
        <MDBox
          display="flex"
          justifyContent="space-between"
          alignItems={{ xs: "flex-start", sm: "center" }}
          flexDirection={{ xs: "column", sm: "row" }}
        >
          <MDBox display="flex" alignItems="center">
            <MDAvatar  size="md" src={logoFacebook} alt="Facebook logo" variant="rounded" />
            <MDBox ml={2}>
              <MDTypography variant="h5" fontWeight="medium">
                {t("facebook-ads")}
              </MDTypography>
            </MDBox>
          </MDBox>
          {!!accountOptions && accountOptions.length == 0 && <MDBox
            display="flex"
            alignItems="center"
            justifyContent="flex-end"
            width={{ xs: "100%", sm: "auto" }}
            mt={{ xs: 1, sm: 0 }}
          >
            <MDBox lineHeight={0} ml={2}>
                              <MDButton color="secondary" variant="text" component={Link} to={`/acquisition/ads/facebook`} sx={{fontSize:"13px"}} size="small" fontWeight="regular">
                {t("connect-your-account")}&nbsp;
                  <Icon fontSize="medium">arrow_outward</Icon>
              </MDButton>
            </MDBox>
          </MDBox>}
        </MDBox>
        {!!selectedIntg && selectedIntg.request_id && <SingleFacebookAccount
          integration={selectedIntg}
          disconnectSource={disconnectSource}
          accountSelector={accountSelector}
          accountOptions={accountOptions}
          loaderDisconnectSource={loaderDisconnectSource}  
        />}
        <Divider />
        <MDBox
          display="flex"
          justifyContent="space-between"
          alignItems={{ xs: "flex-start", sm: "center" }}
          flexDirection={{ xs: "column", sm: "row" }}
        >
          <MDBox display="flex" alignItems="center">
            <MDAvatar size="sm" src={klaviyoLogo} alt="klaviyo logo" variant="rounded" />
            <MDBox ml={2} lineHeight={0}>
              <MDTypography variant="h5" fontWeight="medium" textTransform="capitalize">
                {t("klaviyo")}
              </MDTypography>
              <MDTypography variant="button" color="secondary" fontWeight="regular">
                {t("tag-klaviyo-description")}
              </MDTypography>
            </MDBox>
          </MDBox>
          <MDBox
            display="flex"
            justifyContent="flex-end"
            alignItems="center"
            width={{ xs: "100%", sm: "auto" }}
            mt={{ xs: 1, sm: 0 }}
          >
            <MDBox lineHeight={0} ml={2}>
                              {!klaviyoIng.request_id && <MDButton color="secondary" variant="text" component={Link} to={`/customers/rfm`} sx={{fontSize:"13px"}} size="small" fontWeight="regular">
                {t("connect")}&nbsp;
                  <Icon fontSize="medium">arrow_outward</Icon>
              </MDButton>}
              {!!klaviyoIng.request_id && <MDTypography color="secondary" variant="button"  sx={{fontSize:"13px"}} size="small" fontWeight="regular">
                {t("connected")}
              </MDTypography>}
            </MDBox>
            {!!klaviyoIng.request_id && <MDBox lineHeight={0} display="flex" flexDirection="row" justifyContent="flex-end" >
              <MDButton
                variant="text"
                color="secondary"
                size="small"
                onClick={() => {disconnectSource('klaviyo', klaviyoIng.request_id ?? "")}} >
                  {loaderDisconnectSource == "klaviyo"
                      ? <CircularProgress size={20} color="error" />
                      : t("disconnect-btn")
                  }
            </MDButton>
            </MDBox>}
          </MDBox>
        </MDBox>
      </MDBox>}
    </Card>
  );
}

export default Accounts;
