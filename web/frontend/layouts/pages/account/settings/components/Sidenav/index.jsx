// @mui material components
import Card from "@mui/material/Card";
import Icon from "@mui/material/Icon";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";

// Material Dashboard 2 PRO React context
import { useMaterialUIController } from "@/context";

import {useTranslation} from "react-i18next";

function Sidenav({options, selectedOption, setSelectedOption}) {
  const [controller] = useMaterialUIController();
  const { miniSidenav, loginConfig, darkMode, shopConfig } = controller;

  const {t} = useTranslation();

  let showSubscriptionBanner = false && shopConfig.subscription_enabled
  && shopConfig.planDetails?.planType == "free";

    // { icon: "security", label: "2FA", href: "2fa" },    
    // { icon: "settings_applications", label: "sessions", href: "sessions" },
    // { icon: "delete", label: "delete account", href: "delete-account" },

  const renderSidenavItems = options.map(({ icon, label, id }, key) => {
    const itemKey = `item-${key}`;

    return (
      <MDBox key={itemKey} component="li" pt={key === 0 ? 0 : 1}>
        <MDTypography
          component="a"
          // href={`#${id}`}
          onClick={() => setSelectedOption(id)}
          variant="button"
          fontWeight="regular"
          textTransform="capitalize"
          sx={({
            borders: { borderRadius },
            functions: { pxToRem },
            palette: { light },
            transitions,
          }) => ({
            display: "flex",
            cursor: "pointer",
            alignItems: "center",
            borderRadius: borderRadius.md,
            backgroundColor: selectedOption === id ? light.main : "transparent",
            padding: `${pxToRem(10)} ${pxToRem(16)}`,
            transition: transitions.create("background-color", {
              easing: transitions.easing.easeInOut,
              duration: transitions.duration.shorter,
            }),

            "&:hover": {
              backgroundColor: light.main,
            },
          })}
        >
          <MDBox mr={1.5} lineHeight={1} color={darkMode ? "white" : "dark"}>
            <Icon fontSize="small">{icon}</Icon>
          </MDBox>
          {t(label)}
        </MDTypography>
      </MDBox>
    );
  });

  return (
    <Card
      sx={{
        borderRadius: ({ borders: { borderRadius } }) => borderRadius.lg,
        position: "sticky",
        top: showSubscriptionBanner ? "4rem" : "1%",
      }}
    >
      <MDBox
        component="ul"
        display="flex"
        flexDirection="column"
        p={2}
        m={0}
        sx={{ listStyle: "none" }}
      >
        {renderSidenavItems}
      </MDBox>
    </Card>
  );
}

export default Sidenav;
