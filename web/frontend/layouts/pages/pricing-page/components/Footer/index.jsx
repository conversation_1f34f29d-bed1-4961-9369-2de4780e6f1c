// @mui material components
import Grid from "@mui/material/Grid";
import {tracker} from "@/context";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import { useTranslation } from "react-i18next";

function Footer() {
  const {t} = useTranslation();
  return (
    <MDBox component="footer" py={6}>
      <Grid container justifyContent="center">
        <Grid item xs={10} lg={8}>
          <MDBox display="flex" justifyContent="center" flexWrap="wrap" mb={3}>
            <MDBox>
              <MDTypography
                // component={Link}
                // to="/book-call"
                onClick={tracker.intercom.show}
                sx={{ cursor: "pointer" }}
                variant="body2"
                fontWeight="regular"
                color="secondary"
              >
                {t("need-help")}
              </MDTypography>
            </MDBox>
          </MDBox>
        </Grid>
        <Grid item xs={12} lg={8} sx={{ textAlign: "center" }}>
          <MDTypography variant="body2" color="secondary">
            Copyright &copy; 2024, Datadrew.io
          </MDTypography>
        </Grid>
      </Grid>
    </MDBox>
  );
}

export default Footer;
