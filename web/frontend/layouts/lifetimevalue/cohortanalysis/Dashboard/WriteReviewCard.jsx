import React from "react";

// antd imports
import Icon from "@mui/material/Icon";
import MDButton from "@/components/MDButton";

// @mui material components
import Grid from "@mui/material/Grid";
import Card from "@mui/material/Card";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";

import {axiosInstance, tracker} from "@/context";

export default class WriteReviewCard extends React.Component {

    constructor(props) {
        super(props);
        this.state = {
            closed : false
        }
    
        this.writeReviewClose = this.writeReviewClose.bind(this);
    }

    writeReviewClose = () => {
        let reqData = {type : "review"};
        if (!!this.props.selectedShop) {
            reqData.selectedShop = this.props.selectedShop;
        }

        tracker.event(`Review Closed`, {report : "cohort analysis"});

        var setClose = () => {
            this.setState({closed:true});
        }

        //Pass the cancel token to the current request
        axiosInstance
            .post("/api/blacklist", reqData)
            .then(function (response) {
                console.log("success");
                setClose();
            })
            .catch((err) => {
                console.log("error", err);
            });
    };

    render() {
        const {closed} = this.state;

        if (closed) {
            return null
        }

        return (
            <MDBox mt={3} mb={6} >
            <Card>
            <Grid container spacing={3} justifyContent="center" alignItems={"center"}>
                <Grid item xs={3}>
                    <MDBox 
                        display="flex"
                        alignItems="center"
                        justifyContent="center">
                        <MDBox
                            component="img"
                            pl={3}
                            sx={{
                                width:"80%",
                            }}
                            src={"https://illustrations.popsy.co/yellow/graphic-design.svg"}
                            alt={"happy-customer"}
                        />
                    </MDBox>
                    
                </Grid>
                <Grid item xs={9}>
                    <MDBox py={3}>
                    <MDBox display="flex" alignItems="center">
                        <MDBox lineHeight={1}>
                            <MDTypography variant="h6" fontWeight="medium" mb={1}>
                                Finding our app useful? Please support us with a review.
                            </MDTypography>
                            <MDBox mb={2}>
                                <MDTypography variant="button" color="dark">
                                <p>We are working hard to create and evolve this app.</p>
                                <p>
                                    If we could add any value, please share your support by
                                    writing a review. It helps other merchants find our app.
                                </p>
                                <p>Team Datadrew LTV thank you for your kindness.</p>
                                </MDTypography>
                            </MDBox>
                            <MDButton
                                variant="gradient"
                                color="info"
                                size="small"
                                onClick={() => {tracker.event("Review Attempt", {report: "cohort analysis"})}}
                                target="_blank"
                                href="https://apps.shopify.com/customer-lifetime-value#modal-show=ReviewListingModal"
                                >
                                <Icon>rate_review</Icon>&nbsp;
                                Rate our app
                            </MDButton>
                            <MDButton variant="text" color="secondary" size="small" onClick={this.writeReviewClose}>
                                Later
                            </MDButton>
                        </MDBox>
                    </MDBox>
                    </MDBox>
                </Grid>
            </Grid>
            </Card>
            </MDBox>
        );
    }
}