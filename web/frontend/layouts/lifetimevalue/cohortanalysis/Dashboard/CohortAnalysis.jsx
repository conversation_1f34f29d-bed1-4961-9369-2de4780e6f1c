import React, { useEffect, useState } from "react";
import axios from "axios";
import dayjs from 'dayjs';
import {Link} from "react-router-dom";

import DataHeatmap from "@/layouts/lifetimevalue/cohortanalysis/Dashboard/DataHeatmap.jsx";
import LTVTrend from "@/layouts/lifetimevalue/cohortanalysis/Dashboard/LTVTrend.jsx";
import FormatFilter from "@/components/Filters/FormatFilter";
import { metrics } from "@/components/Filters/MetricSelector";

// Material Dashboard 2 PRO React components
import Divider from "@mui/material/Divider";
import Card from "@mui/material/Card";
import Icon from "@mui/material/Icon";
import MDBox from "@/components/MDBox";
import MDButton from "@/components/MDButton";
import MDTypography from "@/components/MDTypography";
import Grid from "@mui/material/Grid";
// Material Dashboard 2 React Components
import MDPagination from "@/components/MDPagination";
import MDTooltip from "@/components/MDTooltip";
import { Element } from 'react-scroll';

let cancelToken;
import {useCancellableAxios, tracker, useMaterialUIController} from "@/context";
import { useTranslation } from "react-i18next";
import { InfoOutlinedIcon } from "@/examples/Icons";

export function CohortAnalysisSummary() {
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, selectedFilters, loginConfig} = controller;
    const {start_date, end_date} = selectedFilters;
    const {t} = useTranslation();
    const [format, setFormat] = React.useState("value");
    const [loading, setLoading] = React.useState(true);
    const [response, setReponse] = React.useState({});

    const axiosInstance = useCancellableAxios();

    let metric = "acc_total_sales_per_customer";
    let time_frame = "month";
    // let start_date = dayjs().subtract(3, "month").startOf("month").format("YYYY-MM-DD");
    // let end_date = dayjs().format("YYYY-MM-DD");
    let reportLink = "/retention/cohorts/ltv";

    let timePeriodDisplay = dayjs(start_date).format("YY") == dayjs(end_date).format("YY")
        ? `${dayjs(start_date).format("D MMM")} - ${dayjs(end_date).format("D MMM")}`
        : `${dayjs(start_date).format("D MMM YY")} - ${dayjs(end_date).format("D MMM YY")}`;

    const getDataByReference = (ref, custom_format) => {
        if (!(ref in data)) {
            return "";
        }

        let format_to_use = custom_format || format;

        if (metric in data[ref] && format_to_use in data[ref][metric]) {
            return data[ref][metric][format_to_use];
        }

        return "";
    };

    const getDataObj = (ref) => {
        if (!(ref in data)) {
            return {};
        }

        return data[ref];
    };

    useEffect(() => {
        setLoading(true);

        if (typeof cancelToken != typeof undefined) {
            cancelToken.cancel("Operation canceled due to new request.");
        }

        //Save the cancel token for the current request
        cancelToken = axios.CancelToken.source();

        let reqData = {
            start_date: dayjs(start_date).format("YYYY-MM-DD"),
            end_date: dayjs(end_date).format("YYYY-MM-DD"),
            time_frame,
            applied_filters: {},
        };

        if (!!selectedShop) {
            reqData.selectedShop = selectedShop;
        }

        //Pass the cancel token to the current request
        axiosInstance
            .post("/api/cohorts", reqData, { cancelToken: cancelToken.token })
            .then(function (response) {
                setLoading(false);
                setReponse(response.data);
            })
            .catch((err) => {
                console.log("error", err);
                if (!axios.isCancel(err)) {
                    setLoading(false);
                }
            });
    }, [selectedShop, metric, start_date, end_date]);

    const {
        heatmap,
        data,
        currency,
        order_count,
        trends,
    } = response;

    let dataHeatmapProps = {
        heatmap,
        order_count,
        metric, 
        getDataByReference,
        getDataObj,
        loading,
        format,
        time_frame,
    };

    let formatFilterProps = {
        format,
        use_currency : metrics[metric].format == "currency",
        currency,
        handleFormatChange: setFormat
    };

    return (
        <Grid container spacing={3} layout="row" alignItems="stretch">
            <Grid item xs={12} md={12} lg={12}>
                <Element name="section-ltv-growth">
                <LTVTrend
                    trends={trends}
                    loading={loading}
                    reportLink={reportLink}
                    timePeriodDisplay={timePeriodDisplay} 
                />
                </Element>
            </Grid>
            <Grid item xs={12} md={12} lg={12}>
                <Element name="section-ltv-cohort-analysis">
                <CohortAnalysis
                    metric={metric}
                    dataHeatmapProps={dataHeatmapProps} 
                    formatFilterProps={formatFilterProps}
                    cohortExportButton={null} // No export button on summary
                    reportLink={reportLink}
                    maxPages={1}
                    timePeriodDisplay={timePeriodDisplay}
                />
                </Element>
            </Grid>
        </Grid>
    );
}

export function CohortAnalysis(props) {
    const {t} = useTranslation();

    const  {
        metric,
        reportLink,
        timePeriodDisplay,
        cohortExportButton,
        dataHeatmapProps,
        formatFilterProps,
        maxPages,
        pageNumber,
        setPageNumber,
    } = props;

    // Add this to your component's state
    const [visiblePageStart, setVisiblePageStart] = useState(1);

    useEffect(() => {
        setVisiblePageStart(1);
    }, [maxPages]);

    // Modify this function
    const handleVisiblePageChange = (newVisiblePageNumber) => {
        if (newVisiblePageNumber < 1 || newVisiblePageNumber >= maxPages-1) {
            return;
        }

        if (newVisiblePageNumber < visiblePageStart || newVisiblePageNumber >= visiblePageStart + 1) {
            setVisiblePageStart(newVisiblePageNumber);
        }
    };

    const generatePageNumbers = () => {
        return [...Array(Math.min(3, maxPages)).keys()].map(i => visiblePageStart + i);
    };

    const tooltipContent = (
        <MDBox display="flex" flexDirection="column" alignItems="center">
            <MDTypography variant="caption" fontWeight="medium" color="text" textTransform="capitalize">
                {t(metrics[metric].desc)}
            </MDTypography>
            {metrics[metric].desc_subtitle && 
                <MDTypography variant="caption" fontWeight="regular" color="text" mt={1}>
                    {t(metrics[metric].desc_subtitle)}
                </MDTypography>
            }
            <MDBox mt={2}>
                <MDButton
                    component="a"
                    href={"http://help.datadrew.io/en/articles/5239600-cohort-analysis"}
                    target="_blank"
                    rel="noreferrer"
                    variant="outlined"
                    color={"secondary"}
                    size="small"
                    p={1}
                    onClick={() => {
                        tracker.mixpanel.track("View Article", {report: "cohort analysis"}); 
                    }}
                >
                    {t("more")}
                </MDButton>
            </MDBox>
        </MDBox>
    )

    return (
        <Card sx={{ width: "100%" }}>
            <Grid container spacing={3} p={1.6} direction="row" justifyContent="space-between" alignItems="flex-end">
                <Grid item display="flex" justifyContent="flex-start" alignItems="center">
                    <MDTypography variant="h5" color="dark" fontWeight="regular" mr={1} className="card-title-default">
                        {!!reportLink ? t("cohort-analysis") : t(metrics[metric].label)}
                    </MDTypography>
                    <MDTooltip title={tooltipContent} placement="right">
                        <MDBox><InfoOutlinedIcon/></MDBox>
                    </MDTooltip>
                </Grid>
                <Grid item>
                    <MDBox display="flex">
                        {cohortExportButton && <MDBox ml={1}>
                            {cohortExportButton}
                        </MDBox>}
                        <MDBox ml={1}>
                            <FormatFilter {...formatFilterProps} />
                        </MDBox>
                    </MDBox>
                </Grid>
            </Grid>
            <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />
            <MDBox px={1.6} pt={1} pb={3}>
                <DataHeatmap {...dataHeatmapProps} shortView={!!reportLink} />
                {!!maxPages && maxPages > 1 && <MDBox mt={2}> 
                <MDPagination variant="contained">
                    <MDPagination item onClick = {() => handleVisiblePageChange(visiblePageStart - 1)}>
                    <Icon>keyboard_arrow_left</Icon>
                    </MDPagination>
                    {generatePageNumbers().map((page) => {
                    return (
                        <MDPagination item key={page} active={pageNumber == page} onClick = {() => setPageNumber(page)}>
                        {page}
                        </MDPagination>
                    );
                    })}
                    <MDPagination item onClick = {() => handleVisiblePageChange(visiblePageStart + 1)}>
                    <Icon>keyboard_arrow_right</Icon>
                    </MDPagination>
                </MDPagination>
                </MDBox>}
            </MDBox>
            {reportLink && <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />}
            {reportLink && <MDBox p={1.6} pt={1} display="flex" direction="row" justifyContent="space-between" alignItems="center" sx={{width :"100%"}}>
                <MDTooltip title={t("ltv-trend-tip")} placement="right">
                    <MDTypography variant={"button"} color={"secondary"} fontWeight="regular" verticalAlign="middle" alignItems="center" style={{borderBottom: "1px dotted #ccc", cursor: "pointer"}}>
                    {t("time-period") + " : " + timePeriodDisplay}
                    </MDTypography>
                </MDTooltip>
                <MDButton variant="outlined" color={"dark"} component={Link} to={reportLink} size="small" onClick={() => tracker.mixpanel.track("FullReport Viewed", {reportLink: reportLink})} >
                    <MDTypography variant={"button"} color={"dark"} fontWeight="regular" verticalAlign="middle" alignItems="center">
                        {t("deepdive-btn")} &nbsp;
                        <Icon fontSize="default" className={"mdi mdi-arrow-right"} />
                    </MDTypography>
                </MDButton>
            </MDBox>}
        </Card>
    );
}


export default CohortAnalysis;