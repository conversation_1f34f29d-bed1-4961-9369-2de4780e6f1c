import React, {useState, useEffect, useMemo} from "react";
// Material Dashboard 2 PRO React examples
import {Link} from "react-router-dom";
import MDTooltip from "@/components/MDTooltip";
// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
// Material Dashboard 2 PRO React examples
import {tracker, useMaterialUIController, useCancellableAxios} from "@/context";
import { useTranslation } from "react-i18next";
import logoFacebook from "@/assets/images/facebook-logo.png";
import logoGoogleAds from "@/assets/images/google-ads-logo.png";
import logoGoogleAnalytics from "@/assets/images/google-analytics-logo.png";
import MDBadgeDot from "@/components/MDBadgeDot";
import {BLENDED, SOURCE_FB, SOURCE_GOOGLE_ADS, SOURCE_GOOGLE_ANALYTICS} from "@/layouts/dashboards/metrics/metadata";

import { setSelectedShop } from "@/context";
import { useNavigate } from "react-router-dom";

const ConnectPill = ({logo, shopDomain}) => {
    const {t} = useTranslation();
    const [_, dispatch] = useMaterialUIController();
    const navigate = useNavigate();

    const handleClick = () => {
        if (shopDomain) {
            setSelectedShop(dispatch, shopDomain);
        }
        navigate("/integrations");
    };

    return (
        <MDBox display="flex" justifyContent="center" alignItems="center" p={1} mr={1}
                    sx={{
                        cursor: "pointer",
                        borderRadius: ({ borders: { borderRadius } }) => borderRadius.md,
                        border: "1px solid #49a3f1"
                    }}
            onClick={handleClick}
            >
            <MDBox component="img" src={logo} alt="ads" width="1rem" mr={0.5} />
            <MDTypography variant="caption" fontWeight="regular" color="text" textAlign="center" sx={{color:"#49a3f1"}}>
                {t("connect")}
            </MDTypography>
        </MDBox>
    )
}

const ConnectedPill = ({logo, accounts}) => {
    const {t} = useTranslation();

    const tooltipContent = (
        <MDBox display="flex" flexDirection="column" alignItems="left" justifyContent="center">
            {accounts.map((account) => {
                return (
                    <MDBadgeDot
                        key={account}
                        badgeContent={account}
                        color={"success"}
                        font={{color: "text", weight: "regular"}}
                        size="sm"/>
                );
            })}
        </MDBox>
    );

    return (
        <MDTooltip title={tooltipContent} placement="bottom">
        <MDBox display="flex" justifyContent="center" alignItems="center" sx={{cursor: "default"}} mr={1}>
            <MDBox component="img" src={logo} alt="ads" width="1rem" mr={0.5} />
            <MDTypography variant="caption" fontWeight="regular" color="text" textAlign="center">
                {t("connected")}
            </MDTypography>
        </MDBox>
        </MDTooltip>
    );
};

function getSourceNamesForIntegrations(intgs, t) {
    const names = [];
    for (let intg of intgs) {
        if (intg.synced) {
            const source_config_id = intg.source_config_id ?? "";
            const source_config_name = intg.source_config_name ?? "";
            let source_name = "";
            if (!!source_config_id && !!source_config_name) {
                source_name = `${source_config_name}`;
            } else if (!!source_config_id) {
                source_name = `#${source_config_id}`;
            } else {
                source_name = t("integration.finish-account-setup");
            }
            names.push(source_name);
        }
    }
    return names;
}

const ConnectedAdAccounts = ({preset, shopIntegrations = null, shopDomain = null}) => {
    
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, integrations} = controller;
    const {t} = useTranslation();

    let integrationsToUse = shopIntegrations ?? integrations;

    const [ggAccounts, fbAccounts, gaAccounts] = useMemo(() => {
        let fbIntgs = SOURCE_FB in integrationsToUse ? integrationsToUse[SOURCE_FB] : [];
        let ggIntgs = SOURCE_GOOGLE_ADS in integrationsToUse ? integrationsToUse[SOURCE_GOOGLE_ADS] : [];
        let gaIntgs = SOURCE_GOOGLE_ANALYTICS in integrationsToUse ? integrationsToUse[SOURCE_GOOGLE_ANALYTICS] : [];
        const fbAccounts = getSourceNamesForIntegrations(fbIntgs, t);
        const ggAccounts = getSourceNamesForIntegrations(ggIntgs, t);
        const gaAccounts = getSourceNamesForIntegrations(gaIntgs, t);
        return [ggAccounts, fbAccounts, gaAccounts];
    }, [integrationsToUse]);

    if (preset != BLENDED) {
        return null;
    }

    return (
        <MDBox display="flex" flexDirection="row" alignItems="center" justifyContent="center">
            {gaAccounts.length == 0 && <ConnectPill logo={logoGoogleAnalytics} shopDomain={shopDomain} />}
            {ggAccounts.length == 0 && <ConnectPill logo={logoGoogleAds} shopDomain={shopDomain} />}
            {fbAccounts.length == 0 && <ConnectPill logo={logoFacebook} shopDomain={shopDomain} />}
            {gaAccounts.length > 0 && <ConnectedPill logo={logoGoogleAnalytics} accounts={gaAccounts} />}
            {ggAccounts.length > 0 && <ConnectedPill logo={logoGoogleAds} accounts={ggAccounts} />}
            {fbAccounts.length > 0 && <ConnectedPill logo={logoFacebook} accounts={fbAccounts} />}
        </MDBox>
    );
}

export default ConnectedAdAccounts;