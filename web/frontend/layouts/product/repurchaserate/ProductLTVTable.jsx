import React, { useState, useRef, useMemo } from 'react';
// antd imports
import {Table, Space, Input, Row, Col, Spin, Button} from "antd";
import Highlighter from "react-highlight-words";
import MDBox from "@/components/MDBox";
import MDTypography from '@/components/MDTypography';
import CircularProgress from "@mui/material/CircularProgress";
import SearchOutlinedIcon from '@mui/icons-material/SearchOutlined';
import Empty from "@/components/EmptyChart";


const CustomCell = ({children}) => {
    return (
        <MDBox 
            color="none"
            sx={({ typography: { size, fontWeightBold, fontWeight } }) => ({
                fontSize: size.s,
                fontWeight: fontWeight
            })}
        >
            <MDTypography variant="inherit">
                {children}
            </MDTypography>
        </MDBox>
    )
}

const CustomHead = ({children}) => {
    return (
        <MDBox 
            color="secondary"
            sx={({ typography: { size, fontWeightBold } }) => ({
                fontSize: size.xs,
                fontWeight: fontWeightBold
            })}
        >
            <MDTypography variant="inherit" textTransform="uppercase" color="secondary">
                {children}
            </MDTypography>
        </MDBox>
    )
}


function ProductLTVTable(props) {
    const [searchText, setSearchText] = useState("");
    const [searchedColumn, setSearchedColumn] = useState("");
    const { response, loading, group_value, t, selectedProductTypes } = props;
    var searchInput = useRef(null);

    let orig_product_ltv = !!response && !!response.product_ltv ? response.product_ltv : []
    let orig_product_type_ltv = !!response && !!response.product_type_ltv ? response.product_type_ltv : [];

    let product_ltv = useMemo(() => {
        if (selectedProductTypes.length == 0) {
            return orig_product_ltv
        }
        return orig_product_ltv.filter(p => selectedProductTypes.indexOf(p.product_type ?? "") != -1)
    }, [selectedProductTypes, orig_product_ltv]);


    let product_type_ltv = useMemo(() => {
        if (selectedProductTypes.length == 0) {
            return orig_product_type_ltv
        }
        return orig_product_type_ltv.filter(p => selectedProductTypes.indexOf(p.product_type ?? "") != -1)
    }, [selectedProductTypes, orig_product_type_ltv]);

    const handleFilter = (selectedKeys, confirm, dataIndex) => {
        confirm();
    };

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0]);
        setSearchedColumn(dataIndex);
    };

    const handleSearchReset = (clearFilters) => {
        clearFilters();
        setSearchText("");
        setSearchedColumn("");
    };

    const getColumnSearchProps = (dataIndex, displayName) => {
        const { t } = props;
        
        return {
            filterDropdown: ({
                setSelectedKeys,
                selectedKeys,
                confirm,
                clearFilters,
            }) => (
                <div style={{ padding: 8}}>
                    <Input
                        ref={searchInput}
                        placeholder={`Search ${displayName}`}
                        value={selectedKeys[0]}
                        onChange={(e) =>
                            setSelectedKeys(e.target.value ? [e.target.value] : [])
                        }
                        onPressEnter={() =>
                            handleSearch(selectedKeys, confirm, dataIndex)
                        }
                        style={{ width: 188, marginBottom: 8, display: "block", zIndex : 9999 }}
                    />
                    <Space>
                        <Button
                            type="primary"
                            onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                            icon={<SearchOutlinedIcon />}
                            size="small"
                            style={{ width: 90 }}
                        >
                            {t("search")}
                        </Button>
                        <Button
                            onClick={() => handleSearchReset(clearFilters)}
                            size="small"
                            style={{ width: 90 }}
                        >
                            {t("reset")}
                        </Button>
                        <Button
                            type="link"
                            size="small"
                            onClick={() => {
                                confirm({ closeDropdown: false });
                                setSearchText(selectedKeys[0]);
                                setSearchedColumn(dataIndex);
                            }}
                        >
                            Filter
                        </Button>
                    </Space>
                </div>
            ),
            filterIcon: (filtered) => (
                <SearchOutlinedIcon style={{ color: filtered ? "#1890ff" : undefined }} />
            ),
            onFilter: (value, record) =>
                record[dataIndex]
                    ? record[dataIndex]
                        .toString()
                        .toLowerCase()
                        .includes(value.toLowerCase())
                    : "",
            onFilterDropdownOpenChange: (visible) => {
                if (visible) {
                    setTimeout(() => !!searchInput && !!searchInput.current && searchInput.current.select(), 100);
                }
            },
            render: (text) =>
                searchedColumn === dataIndex ? (
                    <Highlighter
                        highlightStyle={{ backgroundColor: "#ffc069", padding: 0 }}
                        searchWords={[searchText]}
                        autoEscape
                        textToHighlight={text ? text.toString() : ""}
                    />
                ) : (
                    <CustomCell>{text}</CustomCell>
                ),
        }
    };

    if (
        !response ||
        Object.keys(response).length == 0 ||
        (group_value == "product" && (!product_ltv || product_ltv.length == 0)) || 
        (group_value == "product_type" && (!product_type_ltv || product_type_ltv.length == 0))
    ) {
        if (loading) {
            return (
                <MDBox display="flex" alignItems="center" justifyContent="center" minHeight="500px" height="100%">
                    <CircularProgress color="secondary" />
                </MDBox>
            );
        }

        return <Empty />;
    }

    var getTitle = (primary, secondary = null) => {
        return (
            <CustomHead>
                {primary}
                {secondary && (
                    <span>
                        <br />
                        {secondary}
                    </span>
                )}
            </CustomHead>
        );
    };

    var columns_product = [
        {
            title: getTitle(t("first-order-product")),
            dataIndex: "product_name",
            ...getColumnSearchProps("product_name", t("product")),
        },
        {
            title: getTitle(t("new-cust")),
            dataIndex: "customer_count",
            render: (text, record) => (
                <CustomCell>{record.customer_count}</CustomCell>
            ),
            filterDropdown: ({
                setSelectedKeys,
                selectedKeys,
                confirm,
                clearFilters,
            }) => (
                <div style={{ padding: 8 }}>
                    <Input
                        placeholder={`with minimum customers`}
                        value={selectedKeys[0]}
                        onChange={(e) =>
                            setSelectedKeys(e.target.value ? [e.target.value] : [])
                        }
                        onPressEnter={() =>
                            handleFilter(selectedKeys, confirm, "customer_count")
                        }
                        style={{ width: 188, marginBottom: 8, display: "block" }}
                    />
                    <Space>
                        <Button
                            type="ghost"
                            onClick={() => clearFilters()}
                            size="small"
                            style={{ width: 90 }}
                        >
                            {t("reset")}
                        </Button>
                        <Button
                            type="primary"
                            onClick={() =>
                                handleFilter(selectedKeys, confirm, "customer_count")
                            }
                            size="small"
                            style={{ width: 90 }}
                        >
                            {t("ok")}
                        </Button>
                    </Space>
                </div>
            ),
            sorter: (a, b) => a.customer_count - b.customer_count,
            onFilter: (value, record) => {
                return record.customer_count > value;
            },
            defaultSortOrder: "descend",
        },
        {
            title: getTitle(t("repurchasers")),
            dataIndex: "repeat_customer_count",
            sorter: (a, b) => a.repeat_customer_count - b.repeat_customer_count,
            render: (text, record) => (
                <CustomCell>{record.repeat_customer_count}</CustomCell>
            ),
        },
        {
            title: getTitle(t("repurchased"), t("anything")),
            dataIndex: "repeat_percentage",
            sorter: (a, b) => a.repeat_percentage - b.repeat_percentage,
            render: (text, record) => (
                <CustomCell>{record.repeat_percentage + "%"}</CustomCell>
            ),
        },
        {
            title: getTitle(t("repurchased"), t("same-product")),
            dataIndex: "same_product_repeat_percentage",
            sorter: (a, b) =>
                a.same_product_repeat_percentage - b.same_product_repeat_percentage,
            render: (text, record) => (
                <CustomCell>{record.same_product_repeat_percentage + "%"}</CustomCell>
            ),
        },
        {
            title: getTitle(t("fo-aov")),
            dataIndex: "aov",
            sorter: (a, b) => a.aov - b.aov,
            render: (text, record) => (
                <CustomCell>{record.aov_display}</CustomCell>
            ),
        },
        {
            title: getTitle(t("ltv-90d")),
            dataIndex: "ltv_90d",
            sorter: (a, b) => a.ltv_90d - b.ltv_90d,
            render: (text, record) => (
                <CustomCell>{record.ltv_90d_display}</CustomCell>
            ),
        },
        {
            title: getTitle(t("ltv-180d")),
            dataIndex: "ltv_180d",
            sorter: (a, b) => a.ltv_180d - b.ltv_180d,
            render: (text, record) => (
                <CustomCell>{record.ltv_180d_display}</CustomCell>
            ),
        },
        {
            title: getTitle(t("ltv")),
            dataIndex: "ltv",
            sorter: (a, b) => a.ltv - b.ltv,
            render: (text, record) => (
                <CustomCell>{record.ltv_display}</CustomCell>
            ),
        }
    ];

    var columns_product_type = [
        {
            title: getTitle(t("product-type")),
            dataIndex: "product_type",
            ...getColumnSearchProps("product_type", t("product-type")),
        },
        {
            title: getTitle(t("new-cust")),
            dataIndex: "customer_count",
            render: (text, record) => (
                <CustomCell>{record.customer_count}</CustomCell>
            ),
            filterDropdown: ({
                setSelectedKeys,
                selectedKeys,
                confirm,
                clearFilters,
            }) => (
                <div style={{ padding: 8 }}>
                    <Input
                        placeholder={`with minimum customers`}
                        value={selectedKeys[0]}
                        onChange={(e) =>
                            setSelectedKeys(e.target.value ? [e.target.value] : [])
                        }
                        onPressEnter={() =>
                            handleFilter(selectedKeys, confirm, "customer_count")
                        }
                        style={{ width: 188, marginBottom: 8, display: "block" }}
                    />
                    <Space>
                        <Button
                            type="ghost"
                            onClick={() => clearFilters()}
                            size="small"
                            style={{ width: 90 }}
                        >
                            {t("reset")}
                        </Button>
                        <Button
                            type="primary"
                            onClick={() =>
                                handleFilter(selectedKeys, confirm, "customer_count")
                            }
                            size="small"
                            style={{ width: 90 }}
                        >
                            {t("ok")}
                        </Button>
                    </Space>
                </div>
            ),
            sorter: (a, b) => a.customer_count - b.customer_count,
            onFilter: (value, record) => {
                return record.customer_count > value;
            },
            defaultSortOrder: "descend",
        },
        {
            title: getTitle(t("repurchasers")),
            dataIndex: "repeat_customer_count",
            sorter: (a, b) => a.repeat_customer_count - b.repeat_customer_count,
            render: (text, record) => (
                <CustomCell>{record.repeat_customer_count}</CustomCell>
            ),
        },
        {
            title: getTitle(t("repurchased"), t("anything")),
            dataIndex: "repeat_percentage",
            sorter: (a, b) => a.repeat_percentage - b.repeat_percentage,
            render: (text, record) => (
                <CustomCell>{record.repeat_percentage + "%"}</CustomCell>
            ),
        },
        {
            title: getTitle(t("repurchased"), t("same-product")),
            dataIndex: "same_product_repeat_percentage",
            sorter: (a, b) =>
                a.same_product_repeat_percentage - b.same_product_repeat_percentage,
            render: (text, record) => (
                <CustomCell>{record.same_product_repeat_percentage + "%"}</CustomCell>
            ),
        },
        {
            title: getTitle(t("fo-aov")),
            dataIndex: "aov",
            sorter: (a, b) => a.aov - b.aov,
            render: (text, record) => (
                <CustomCell>{record.aov_display}</CustomCell>
            ),
        },
        {
            title: getTitle(t("ltv-90d")),
            dataIndex: "ltv_90d",
            sorter: (a, b) => a.ltv_90d - b.ltv_90d,
            render: (text, record) => (
                <CustomCell>{record.ltv_90d_display}</CustomCell>
            ),
        },
        {
            title: getTitle(t("ltv-180d")),
            dataIndex: "ltv_180d",
            sorter: (a, b) => a.ltv_180d - b.ltv_180d,
            render: (text, record) => (
                <CustomCell>{record.ltv_180d_display}</CustomCell>
            ),
        },
        {
            title: getTitle(t("ltv")),
            dataIndex: "ltv",
            sorter: (a, b) => a.ltv - b.ltv,
            render: (text, record) => (
                <CustomCell>{record.ltv_display}</CustomCell>
            ),
        }
    ];

    // Return the JSX as usual
    return (
        <Spin
            indicator={
                <CircularProgress color="secondary" />
            }
            spinning={loading}
        >
            {group_value == 'product' && <Table
                rowKey={"product_id"}
                // bordered
                dataSource={product_ltv}
                columns={columns_product}
                pagination={true}
                size="small"
                style={{ fontSize: "14px" }}
            />}
            {group_value == 'product_type' && <Table
                rowKey={"product_type"}
                // bordered
                dataSource={product_type_ltv}
                columns={columns_product_type}
                pagination={true}
                size="small"
                style={{ fontSize: "14px" }}
            />}
        </Spin>
    );
}

export default ProductLTVTable;