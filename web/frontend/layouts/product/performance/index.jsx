import React, { useState, useMemo, useEffect, useCallback } from 'react';
import {
  Box,
  Tooltip,
  Icon,
  Skeleton, Divider, Card, Grid,
  Stack, TextField
} from '@mui/material'
import MDAlert from "@/components/MDAlert";
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import ErrorIcon from '@mui/icons-material/Error';
import FilterDrawer from '@/components/Filters/FilterDrawer';
import ColumnVisibilityDrawer from '@/components/Filters/ColumnVisibilityDrawer';
import ColumnFilter from '@/components/Filters/ColumnFilter';
import DatePickerAnt from '@/components/Filters/DatePickerFilter';
import PillBar from '@/components/PillBar';
import MDTypography from '@/components/MDTypography';
import MDTooltip from '@/components/MDTooltip';
import MDExport from '@/components/MDExport';
import MDBox from '@/components/MDBox';
import DashboardLayout from "@/examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "@/examples/Navbars/DashboardNavbar";
import DataTable from '@/examples/Tables/DataTable';
import ProductCell from './components/ProductCell';
import ProductPerformanceScatterPlot from './components/ProductPerformanceScatterPlot';
import EmptyChart from '@/components/EmptyChart';
import MetricTitle from '@/layouts/dashboards/metrics/MetricTitle';
import InsightCard from './components/InsightCard';
import CatalogSummary from './components/CatalogSummary';

// Context and hooks
import { useMaterialUIController, useCancellableAxios } from "@/context";
import { useTranslation } from "react-i18next";
import { moneyFormatter } from "@/util";
import dayjs from 'dayjs';

// Data functions
import {
  fetchProductData,
  loadColumnPreferences,
  saveColumnPreferences,
  formatMetricValue,
  transformProductData,
  generateInitialColumnVisibility,
  generateAllColumns,
  updateColumnVisibility,
  calculateSummaryFromRawData,
  applyInlineFilters,
  applySearchFilter,
  processInsightsData,
  generateSearchPlaceholder,
  processApiResponse,
  createDefaultDataStructure,
  shouldFetchData
} from '@/layouts/product/performance/data';

// Common style objects
const commonStyles = {
  cardStyle: {
    height: '100%',
    borderRadius: 2,
    boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
    border: '1px solid rgba(0,0,0,0.08)',
    transition: 'box-shadow 0.2s ease-in-out',
    '&:hover': { boxShadow: '0 4px 16px rgba(0, 0, 0, 0.12)' }
  },
  iconContainer: {
    borderRadius: '12px',
    width: 36,
    height: 36,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    mr: 1
  },
  productImage: {
    width: 40,
    height: 40,
    borderRadius: 1.5,
    objectFit: 'cover',
    mr: 2,
    flexShrink: 0,
    border: '1px solid rgba(0, 0, 0, 0.08)',
    backgroundColor: '#f8f9fa'
  }
};

// Helper functions
const handleImageError = (e) => {
  e.target.onerror = null;
  e.target.src = "https://cdn.shopify.com/s/files/1/0533/2089/files/placeholder-images-image_large.png?format=webp&v=1530129081";
};


const ProductPerformance = () => {
  const [controller, dispatch] = useMaterialUIController();
  const { selectedShop, selectedFilters = {}, shopConfig } = controller;
    const { start_date, end_date, time_frame, applied_filters, filter_version } = selectedFilters;
    const { t } = useTranslation();
    // Helper to safely translate with fallback default text
    const getTranslation = useCallback((key, defaultText) => {
      if (!key) return defaultText || '';
      const translated = t(key);
      return translated === key ? (defaultText || key) : translated;
    }, [t]);
    const axiosInstance = useCancellableAxios();


  const [loading, setLoading] = useState(true);
  const [allProductData, setAllProductData] = useState([]);
  const [insightsLoading, setInsightsLoading] = useState(true);
  const [insightsData, setInsightsData] = useState(null);
  const [breakdown, setBreakdown] = useState('product_name');
  const [showBlendedSpend, setShowBlendedSpend] = useState(false);
  const [hasMetaConnection, setHasMetaConnection] = useState(false);
  const [hasGoogleAdsConnection, setHasGoogleAdsConnection] = useState(false);
  const [columnVisibility, setColumnVisibility] = useState({});
  // Now supports multiple filters per column: { [columnId]: [filterObj, ...] }
  const [inlineFilters, setInlineFilters] = useState({});
  const [columnPreferencesLoaded, setColumnPreferencesLoaded] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  // Store avg_roas from backend
  const [avgRoas, setAvgRoas] = useState(0);
  const [avgAdSpend, setAvgAdSpend] = useState(0);

  const isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;
  const isSubscriptionInActive = isSubscriptionEnabled && !(shopConfig.planDetails?.features?.product_performance ?? false);
  const shouldFetch = shouldFetchData({
    shopConfigLoading: shopConfig.loading,
    isSubscriptionInActive,
    selectedShop,
    shopDomain: shopConfig.shop?.myshopify_domain
  });

  // Load and save column preferences using extracted functions
  const loadColumnPreferencesHandler = async () => {
    const preferences = await loadColumnPreferences(selectedShop, axiosInstance);
    if (preferences) {
      setColumnVisibility(preferences);
      setColumnPreferencesLoaded(true);
      return preferences;
    }
    return null;
  };

  const saveColumnPreferencesHandler = async (preferences) => {
    await saveColumnPreferences(preferences, selectedShop, axiosInstance);
  };

  // Enhanced column visibility handler that saves preferences immediately
  const handleColumnVisibilityChange = (newVisibility) => {
    setColumnVisibility(newVisibility);
    // Save preferences immediately when user changes column visibility
    // This ensures user preferences persist across page reloads and shop changes
    saveColumnPreferencesHandler(newVisibility);
  };

  // Handle inline filter operations
  const handleInlineFilterApply = (columnId, filterValue) => {
    setInlineFilters(prev => {
      const newFilters = { ...prev };
      
      if (filterValue === null) {
        delete newFilters[columnId];
        return newFilters;
      }
      
      if (filterValue._action === 'add') {
        const arr = Array.isArray(newFilters[columnId]) ? [...newFilters[columnId]] : [];
        const { _action, ...filter } = filterValue;
        arr.push(filter);
        newFilters[columnId] = arr;
        return newFilters;
      }
      
      if (filterValue._action === 'remove') {
        const arr = Array.isArray(newFilters[columnId]) ? [...newFilters[columnId]] : [];
        if (typeof filterValue._index === 'number') {
          arr.splice(filterValue._index, 1);
        } else {
          const idx = arr.findIndex(f => JSON.stringify(f) === JSON.stringify(filterValue.target));
          if (idx !== -1) arr.splice(idx, 1);
        }
        if (arr.length === 0) {
          delete newFilters[columnId];
        } else {
          newFilters[columnId] = arr;
        }
        return newFilters;
      }
      
      // Legacy: set/replace single filter
      newFilters[columnId] = [filterValue];
      return newFilters;
    });
  };

    const fetchProductDataHandler = async () => {
      if (!start_date || !end_date) return;

      setLoading(true);
      setInsightsLoading(true);
      try {
        const params = {
          start_date,
          end_date,
          selectedShop,
          breakdown,
          applied_filters,
          inlineFilters
        };

        const data = await fetchProductData(params, axiosInstance);
        const processedData = processApiResponse(data);
        
        setAvgRoas(processedData.avgRoas);
        setAvgAdSpend(processedData.avgAdSpend);
        setAllProductData(processedData.allData);
        setShowBlendedSpend(processedData.blendedSpendValue);
        setHasMetaConnection(processedData.hasMetaConnection);
        setHasGoogleAdsConnection(processedData.hasGoogleAdsConnection);
        setInsightsData(processedData.insights);

      } catch (error) {
        const defaultData = createDefaultDataStructure();
        setAllProductData(defaultData.allData);
        setAvgRoas(defaultData.avgRoas);
        setAvgAdSpend(defaultData.avgAdSpend);
        setInsightsData(defaultData.insights);
      } finally {
        setLoading(false);
        setInsightsLoading(false);
      }
    };



    useEffect(() => {
      fetchProductDataHandler();
    }, [start_date, end_date, breakdown, shouldFetch, selectedShop, filter_version, applied_filters, inlineFilters]);



    // Load column preferences on component mount and when shop changes
    useEffect(() => {
      const initializeColumnPreferences = async () => {
        // Wait for shop config to load and ensure we have a valid shop
        if (shopConfig.loading || !selectedShop || !shouldFetch) return;
        
        const savedPreferences = await loadColumnPreferencesHandler();
        
        if (!savedPreferences) {
          // No saved preferences found, initialize with all columns visible
          const initialVisibility = generateInitialColumnVisibility(showBlendedSpend);
          setColumnVisibility(initialVisibility);
          setColumnPreferencesLoaded(true);
        }
      };

      initializeColumnPreferences();
    }, [selectedShop, shouldFetch, shopConfig.loading, showBlendedSpend]);

    // Handle dynamic column changes (e.g., when showBlendedSpend changes due to integration status)
    // This preserves user's saved preferences while making new columns visible by default
    useEffect(() => {
      if (!columnPreferencesLoaded) return;
      
      // Generate all available columns using extracted function
      const allColumns = generateAllColumns(showBlendedSpend, hasMetaConnection, hasGoogleAdsConnection);
      
      // Only update visibility for new columns that don't have saved preferences
      setColumnVisibility(prev => {
        const { updated, hasChanges } = updateColumnVisibility(prev, allColumns, showBlendedSpend);
        
        // Only save if we actually added new columns
        if (hasChanges) {
          saveColumnPreferencesHandler(updated);
        }
        
        return updated;
      });
    }, [showBlendedSpend, hasMetaConnection, hasGoogleAdsConnection, columnPreferencesLoaded, breakdown, t]);

    // Transform data and add product cell for table
    const formattedData = useMemo(() => {
      const transformedData = transformProductData(allProductData, breakdown);
      return transformedData.map((product) => {
        return {
          ...product,
          product_cell: (
            <ProductCell
              image={product.featured_image_url || null}
              name={product.name || ''}
              sku={product.product_id || 'N/A'}
              breakdown={breakdown}
            />
          ),
        };
      });
    }, [allProductData, breakdown]);

  const createNumericColumn = (metric, accessor, width = '120px') => {
    // For blended catalog ROAS and spend, pass integration connection states to grey out disconnected icons
    const header = (accessor === 'blended_catalog_roas' || accessor === 'blended_catalog_spend') ? 
            <MetricTitle 
                sx={{ fontSize: '10px' }} 
                metric={metric} 
                isTableColumn={true}
                integrationStates={{
                    hasMetaConnection,
                    hasGoogleAdsConnection
                }}
            /> :
            <MetricTitle sx={{ fontSize: '10px' }} metric={metric} isTableColumn={true} />;
        
    // Special handling for blended_catalog_roas to add tooltip for non-ad-driven revenue
  const cellRenderer = accessor === 'blended_catalog_roas' ? (cellProps) => {
      const value = cellProps.value;
      const currency = cellProps.row.original.currency;
      if (value === 'non-ad-driven') {
        return (
          <Tooltip 
            title="This revenue was generated without any ad spend, indicating organic/direct sales"
            placement="top"
            arrow
          >
            <span style={{ 
              color: '#4caf50', 
              fontWeight: 'bold',
              cursor: 'help',
              textDecoration: 'underline dotted'
            }}>
              {formatMetricValue(value, 'roas', currency, t)}
            </span>
          </Tooltip>
        );
      }
      return formatMetricValue(value, 'roas', currency, t);
    } : undefined;
        
    const columnDef = {
      Header: ({ column }) => (
        <Box sx={{ 
          position: 'relative', 
          width: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          {/* Header area - no click functionality */}
          <Box 
            sx={{ 
              display: 'flex',
              alignItems: 'center',
              flex: 1,
              minHeight: '32px',
              pr: 1, // Leave space for filter icon
            }}
          >
            {header}
          </Box>
          {/* Filter icon - small clickable area on the right */}
          <Box 
            onClick={(e) => e.stopPropagation()} // Prevent sort when clicking filter
            sx={{ 
              width: '24px',
              height: '32px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexShrink: 0
            }}
          >
            <ColumnFilter
              header={null} // Don't render header content in filter
              columnId={accessor}
              onFilterApply={handleInlineFilterApply}
              appliedValue={inlineFilters[accessor]}
              iconOnly={true} // Only show the filter icon
            />
          </Box>
        </Box>
      ),
      accessor,
      width,
      sortType: (rowA, rowB, columnId) => {
        // Use *_raw field for sorting if available, else fallback to parsed value
        const rawKey = columnId + '_raw';
        const aRaw = rowA.original[rawKey];
        const bRaw = rowB.original[rawKey];
        if (typeof aRaw === 'number' && typeof bRaw === 'number') {
          return aRaw - bRaw;
        }
        // Fallback: try to parse the display value
        const a = rowA.values[columnId];
        const b = rowB.values[columnId];
        const numA = typeof a === 'number' ? a : parseFloat((a||'').toString().replace(/[^\d.-]+/g, '')) || 0;
        const numB = typeof b === 'number' ? b : parseFloat((b||'').toString().replace(/[^\d.-]+/g, '')) || 0;
        return numA - numB;
      },
    };
    if (cellRenderer) {
      columnDef.Cell = cellRenderer;
    }
    return columnDef;
    };

    const getAllColumns = () => {
      let allColumns = [
        // Product cell and name (always first)
        {
          Header: breakdown == 'vendor' ? t('product_vendor') : breakdown == 'product_type' ? t('product_type') : t('product_title'),
          accessor: 'product_cell',
          width: '20%',
          className: 'sticky-column',
          disableSortBy: true, // Hide sort icon and disable sorting for this column
        },
        { Header: t("performance.product-name-text"), accessor: 'product_name_text' },
  // Blended catalog-specific metrics (use catalog scoped translation keys)
  createNumericColumn("blended:catalog-roas", "blended_catalog_roas"),
  ...(showBlendedSpend ? [createNumericColumn("blended:catalog-spend", "blended_catalog_spend")] : []),
        // Ad spends (Meta, Google)
  ...(hasMetaConnection === true ? [createNumericColumn("facebook-marketing:spend", "meta_ads_spend")] : []),
  ...(hasGoogleAdsConnection === true ? [createNumericColumn("google-ads:cost", "gads_spend")] : []),
        // Shopify metrics
        createNumericColumn("shopify:order_count", "order_count"),
        createNumericColumn("shopify:cust_count", "customer_count"),
        createNumericColumn("shopify:units_sold", "units_sold"),
        createNumericColumn("shopify:total_price", "net_revenue"),
        createNumericColumn("shopify:avg_price", "avg_selling_price"),
        ...(hasMetaConnection ? [
          createNumericColumn("facebook-marketing:cpc", "meta_ads_cpc"),
          createNumericColumn("facebook-marketing:cpm", "meta_ads_cpm"),
          createNumericColumn("facebook-marketing:ctr", "meta_ads_ctr"),
          createNumericColumn("facebook-marketing:clicks", "meta_ads_clicks"),
          createNumericColumn("facebook-marketing:impressions", "meta_ads_impressions"),
        ] : []),
        ...(hasGoogleAdsConnection ? [
          createNumericColumn("google-ads:cpc", "gads_cpc"),
          createNumericColumn("google-ads:cpm", "gads_cpm"),
          createNumericColumn("google-ads:ctr", "gads_ctr"),
          createNumericColumn("google-ads:clicks", "gads_clicks"),
          createNumericColumn("google-ads:impressions", "gads_impressions"),
          createNumericColumn("google-ads:conversions", "gads_conversions"),
          createNumericColumn("google-ads:conversions_value", "gads_conversions_value"),
        ] : []),
      ];
      return allColumns;
    };

    const getFilteredColumns = () => {
      const allColumns = getAllColumns();
      const filtered = allColumns.filter(col => columnVisibility[col.accessor] !== false);
      
      return filtered;
    };

    const skeletonTableData = useMemo(() => {
      // Create base skeleton fields
      const createSkeletonRow = () => {
        const baseRow = {
          product_cell: (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Skeleton variant="rectangular" width={40} height={40} sx={{ borderRadius: '8px', mr: 2 }} />
              <Box>
                <Skeleton variant="text" width={240} height={20} sx={{ mb: 0.5 }} />
                <Skeleton variant="text" width={60} height={16} />
              </Box>
            </Box>
          ),
          product_name_text: <Skeleton variant="text" width={240} height={20} />,
          blended_catalog_roas: <Skeleton variant="text" width={80} height={20} />,
          // Always include Shopify columns
          order_count: <Skeleton variant="text" width={60} height={20} />,
          customer_count: <Skeleton variant="text" width={60} height={20} />,
          units_sold: <Skeleton variant="text" width={60} height={20} />,
          net_revenue: <Skeleton variant="text" width={80} height={20} />,
          avg_selling_price: <Skeleton variant="text" width={70} height={20} />,
        };

        // Add blended spend if both integrations connected
        if (showBlendedSpend) {
          baseRow.blended_catalog_spend = <Skeleton variant="text" width={80} height={20} />;
        }

        // Add Meta columns if Meta is connected
        if (hasMetaConnection) {
          baseRow.meta_ads_spend = <Skeleton variant="text" width={80} height={20} />;
          baseRow.meta_ads_cpc = <Skeleton variant="text" width={60} height={20} />;
          baseRow.meta_ads_cpm = <Skeleton variant="text" width={60} height={20} />;
          baseRow.meta_ads_ctr = <Skeleton variant="text" width={50} height={20} />;
          baseRow.meta_ads_clicks = <Skeleton variant="text" width={70} height={20} />;
          baseRow.meta_ads_impressions = <Skeleton variant="text" width={80} height={20} />;
        }

        // Add Google Ads columns if Google Ads is connected
        if (hasGoogleAdsConnection) {
          baseRow.gads_spend = <Skeleton variant="text" width={80} height={20} />;
          baseRow.gads_cpc = <Skeleton variant="text" width={60} height={20} />;
          baseRow.gads_cpm = <Skeleton variant="text" width={60} height={20} />;
          baseRow.gads_ctr = <Skeleton variant="text" width={50} height={20} />;
          baseRow.gads_clicks = <Skeleton variant="text" width={70} height={20} />;
          baseRow.gads_impressions = <Skeleton variant="text" width={80} height={20} />;
          baseRow.gads_conversions = <Skeleton variant="text" width={70} height={20} />;
          baseRow.gads_conversions_value = <Skeleton variant="text" width={80} height={20} />;
        }

        return baseRow;
      };

      const skeletonRows = Array.from({ length: 10 }, () => createSkeletonRow());

      // Add skeleton summary rows
      const skeletonSummaryRows = [
        {
          product_cell: <Skeleton variant="text" width={150} height={20} />,
          product_name_text: <Skeleton variant="text" width={150} height={20} />,
          ...createSkeletonRow()
        },
        {
          product_cell: <Skeleton variant="text" width={120} height={20} />,
          product_name_text: <Skeleton variant="text" width={120} height={20} />,
          ...createSkeletonRow()
        }
      ];

      return {
        columns: getFilteredColumns(),
        rows: [...skeletonRows, ...skeletonSummaryRows]
      };
    }, [columnVisibility, hasMetaConnection, hasGoogleAdsConnection, showBlendedSpend]);


    const displayTableData = useMemo(() => {
      const filteredColumns = getFilteredColumns();

      // Apply inline filters and search using extracted functions
      let filteredRows = applyInlineFilters(formattedData, inlineFilters);
      filteredRows = applySearchFilter(filteredRows, searchTerm);

      return {
        columns: filteredColumns,
        rows: filteredRows
      };
    }, [formattedData, columnVisibility, searchTerm, inlineFilters]);

    // Create summary data for display below the table (calculated from raw backend data)
    const summaryData = useMemo(() => {
      return calculateSummaryFromRawData(allProductData, showBlendedSpend, hasMetaConnection, hasGoogleAdsConnection);
    }, [allProductData, showBlendedSpend, hasMetaConnection, hasGoogleAdsConnection]);

    // Use categorized insights data directly from the backend API
    const insightCards = useMemo(() => {
      return processInsightsData(insightsData);
    }, [insightsData, breakdown]);

    // Insight cards configuration
    const insightCardsConfig = useMemo(() => [
      {
        id: 'wasters',
        title: 'Ad Spend Wasters',
        titleKey: 'performance.top-budget-wasting-products',
        tooltipKey: 'performance.top-budget-wasting-tooltip',
        icon: <ErrorIcon sx={{ color: '#ef4444', fontSize: 24 }} />,
        iconBackground: '#fee2e2',
        dataKey: 'wasters'
      },
      {
        id: 'hero',
        title: 'Hero Products',
        titleKey: 'performance.top-hero-products',
        tooltipKey: 'performance.top-hero-tooltip',
        icon: <Icon sx={{ color: '#22c55e', fontSize: 24 }}>star</Icon>,
        iconBackground: '#dcfce7',
        dataKey: 'hero'
      },
      {
        id: 'potential',
        title: 'Potential Products',
        titleKey: 'performance.top-potential-products',
        tooltipKey: 'performance.top-potential-tooltip',
        icon: <Icon sx={{ color: '#eab308', fontSize: 24 }}>trending_up</Icon>,
        iconBackground: '#fef9c3',
        dataKey: 'potential'
      }
    ], []);

    // Create export button with both XLSX and CSV options
    const exportBtn = useMemo(() => {
        if (loading || allProductData.length === 0) {
            return null;
        }

    return (
      <MDExport
        feature="product_performance"
        formats={["xlsx", "csv"]}
        options={{
          type: "product_performance_export",
          fileName: `Product Performance - ${breakdown} - ${dayjs(start_date).format("YYYY-MM-DD")} to ${dayjs(end_date).format("YYYY-MM-DD")}`,
          parameters: {
            start_date: dayjs(start_date).format("YYYY-MM-DD"),
            end_date: dayjs(end_date).format("YYYY-MM-DD"),
            breakdown: breakdown,
            applied_filters: applied_filters,
            inline_filters: inlineFilters,
            column_visibility: columnVisibility
          },
        }}
      />
    );
    }, [allProductData, breakdown, start_date, end_date, loading, applied_filters, inlineFilters, columnVisibility]);

    // Dynamic search placeholder based on breakdown type
    const searchPlaceholder = useMemo(() => {
        return generateSearchPlaceholder(breakdown, t);
    }, [breakdown, t]);

    return (
      <DashboardLayout>
        <DashboardNavbar />
        <MDBox mb={3} mt={2}>
          <Grid container spacing={2} alignItems="flex-start" wrap="nowrap">
            <Grid item sx={{ flex: '1 1 auto', minWidth: 0 }}>
              {/* Controls card */}
              <Card elevation={0} mb={4} my={4}>
                <Grid container spacing={3} className="report-config-panel" direction="row" justifyContent="flex-start" alignItems="center" px={2} pb={2}>
                  <Grid item sx={{ minWidth: '200px' }}>
                    <MDTypography variant="button" sx={{fontSize:"13px"}}>
                      {t("time-period")}
                    </MDTypography>
                    <DatePickerAnt report="product-performance" />
                  </Grid>
                  <Grid item sx={{ minWidth: '200px' }}>
                    <MDTypography variant="button" sx={{fontSize:"13px", mb: 0.5}}>{t("breakdown-by")}</MDTypography>
                    <PillBar isDropdown options={[{ label: t('product_title'), value: 'product_name' }, { label: t('product-type'), value: 'product_type' }, { label: t('product_vendor'), value: 'vendor' }]} value={breakdown} onChange={(value) => setBreakdown(value)} />
                  </Grid>
                  <Grid item sx={{ minWidth: '200px' }}>
                    <MDTypography variant="button" sx={{fontSize:"13px", mb: 0.5}}>{t("filters")} &nbsp;{<MDTooltip title={t("filters-tooltip")}><InfoOutlinedIcon color="secondary" sx={{fontSize:"14px !important", verticalAlign: 'middle'}} /></MDTooltip>}</MDTypography>
                    <FilterDrawer feature="product_performance" />
                  </Grid>
                </Grid>
              </Card>

              {/* Insight cards (always shown; previously hidden when filters applied) */}
              <MDBox mt={3} mb={3}>
                  <Grid container spacing={{ xs: 2, sm: 3 }}>
                    {insightCardsConfig.map((cardConfig) => (
                      <Grid item xs={12} sm={6} md={4} key={`${cardConfig.id}-${breakdown}`}>
                        <Card sx={commonStyles.cardStyle}>
                          <MDBox pt={{ xs: 2, sm: 3 }} px={{ xs: 2, sm: 3 }} pb={{ xs: 1.5, sm: 2 }}>
                            <MDBox display="flex" alignItems="center" mb={1.2} gap={1}>
                              <Box sx={{ ...commonStyles.iconContainer, background: cardConfig.iconBackground }}>
                                {cardConfig.icon}
                              </Box>
                              <MDTooltip title={getTranslation(cardConfig.tooltipKey)} placement="top">
                                <MDTypography variant="h6" color="secondary" fontWeight="regular" className="card-title-default" display="flex" alignItems="center" sx={{ textDecoration: 'underline', textDecorationStyle: 'dashed', textDecorationColor: '#ccc', textUnderlineOffset: '4px', cursor: 'default' }}>
                                  {t(cardConfig.titleKey)}
                                </MDTypography>
                              </MDTooltip>
                            </MDBox>
                            <Divider sx={{ mb: 1.5, mt: 0.5 }} />
                            <InsightCard
                              key={`${cardConfig.dataKey}-${breakdown}`}
                              titleKey={cardConfig.titleKey}
                              products={insightCards[cardConfig.dataKey]}
                              loading={insightsLoading}
                              getTranslation={getTranslation}
                              avgROAS={avgRoas}
                              avgAdSpend={avgAdSpend}
                              currency={allProductData?.[0]?.currency}
                            />
                          </MDBox>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                </MDBox>

              {/* Scatterplot */}
              <MDBox mt={3} mb={3}>
                <Card elevation={0}>
                  <MDBox pt={3} px={3} pb={2}>
                    <MDBox display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                      <MDTooltip title={getTranslation("scatterplot-tooltip", "Visualizes ROAS vs Ad Spend for all products with reference lines for averages")} placement="top">
                        <MDTypography variant="h6" color="secondary" fontWeight="regular" className="card-title-default" display="flex" alignItems="center" sx={{ textDecoration: 'underline', textDecorationStyle: 'dashed', textDecorationColor: '#ccc', textUnderlineOffset: '4px', cursor: 'default' }}>
                          {getTranslation("performance.product-performance-scatterplot")}
                        </MDTypography>
                      </MDTooltip>
                    </MDBox>
                    <ProductPerformanceScatterPlot
                      data={allProductData}
                      loading={loading}
                      getTranslation={getTranslation}
                      showBlendedSpend={showBlendedSpend}
                      avgRoas={avgRoas}
                      avgAdSpend={avgAdSpend}
                      currency={allProductData?.[0]?.currency}
                      moneyFormatter={moneyFormatter}
                    />
                  </MDBox>
                </Card>
              </MDBox>

              {/* Table + controls */}
              <MDBox mt={1.5} mb={3}>
                <Card sx={{ overflow: 'visible' }}>
                  <MDBox px={1.6} pt={1} pb={3}>
                    <Stack direction="row" alignItems="center" spacing={2} mb={2} sx={{width: '100%'}}>
                      <Box sx={{ minWidth: 300, maxWidth: 400, flexShrink: 0 }}>
                        <MDTypography variant="button" sx={{fontSize:"13px", mb: 0.5}}>{t("search")}</MDTypography>
                        <TextField 
                          fullWidth 
                          placeholder={searchPlaceholder} 
                          value={searchTerm} 
                          onChange={e => setSearchTerm(e.target.value)}
                          size="small"
                          sx={{ 
                            '& .MuiOutlinedInput-root': {
                              height: '36px',
                              fontSize: '14px'
                            }
                          }}
                        />
                      </Box>
                      <Box sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', minHeight: 36 }}>
                        <MDTypography variant="button" sx={{fontSize:"13px", mb: 0.5}}>{t("columns")}</MDTypography>
                        <ColumnVisibilityDrawer columns={getAllColumns()} visibleColumns={columnVisibility} onVisibilityChange={handleColumnVisibilityChange} alwaysVisibleColumns={['product_cell']} />
                      </Box>
                      <Box sx={{ minHeight: 36, display: 'flex', alignItems: 'center', ml: 'auto' }}>{exportBtn}</Box>
                    </Stack>
                    {Object.keys(inlineFilters).length > 0 && (
                      <MDBox mb={2} p={1.5} sx={{ backgroundColor: '#f5f5f5', borderRadius: '8px', border: '1px solid #e0e0e0' }}>
                        <MDBox display="flex" alignItems="center" justifyContent="space-between">
                          <MDBox display="flex" alignItems="center">
                            <MDTypography variant="body2" sx={{ fontWeight: 'bold', mr: 1 }}>{t("performance.active-column-filters")} ({Object.keys(inlineFilters).length}):</MDTypography>
                            <MDBox display="flex" flexWrap="wrap" gap={1}>
                              {Object.entries(inlineFilters).map(([columnId, filters]) => {
                                const operatorSymbols = { gt: '>', lt: '<', equals: '=', notEquals: '≠', gte: '≥', lte: '≤', range: '⬌' };
                                const columnLabel = columnId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                                return filters.map((filter, idx) => {
                                  const operatorSymbol = operatorSymbols[filter.operator] || filter.operator;
                                  return (
                                    <MDBox key={columnId + '-' + idx} sx={{ backgroundColor: 'transparent', color: '#333333', border: '1px solid #666666', borderRadius: '16px', px: 2, py: 0.75, fontSize: '13px', fontWeight: '500', display: 'flex', alignItems: 'center', '&:hover': { backgroundColor: '#f0f0f0', borderColor: '#333333' } }}>
                                      <span style={{ fontWeight: '600' }}>{columnLabel}</span>
                                      <span style={{ margin: '0 8px', fontWeight: '600', color: '#666666', fontSize: '14px' }}>{operatorSymbol}</span>
                                      <span style={{ fontWeight: '600' }}>{filter.operator === 'range' ? `${filter.minValue} - ${filter.maxValue}` : filter.value}</span>
                                      <span style={{ marginLeft: '10px', cursor: 'pointer', fontWeight: 'bold', fontSize: '16px', color: '#999999', lineHeight: '1', padding: '2px', borderRadius: '50%', width: '18px', height: '18px', display: 'flex', alignItems: 'center', justifyContent: 'center' }} onClick={() => handleInlineFilterApply(columnId, { ...filter, _action: 'remove', _index: idx })} onMouseEnter={(e) => { e.target.style.backgroundColor = '#e0e0e0'; e.target.style.color = '#333333'; }} onMouseLeave={(e) => { e.target.style.backgroundColor = 'transparent'; e.target.style.color = '#999999'; }}>×</span>
                                    </MDBox>
                                  );
                                });
                              })}
                            </MDBox>
                          </MDBox>
                          <MDBox>
                            <span style={{ color: '#666666', textDecoration: 'underline', cursor: 'pointer', fontSize: '13px', fontWeight: '500' }} onClick={() => setInlineFilters({})} onMouseEnter={(e) => { e.target.style.color = '#333333'; }} onMouseLeave={(e) => { e.target.style.color = '#666666'; }}>{t("performance.clear-all-filters")}</span>
                          </MDBox>
                        </MDBox>
                      </MDBox>
                    )}
                    {/* Table */}
                    {!loading && allProductData.length === 0 && Object.keys(inlineFilters).length === 0 ? (
                      <EmptyChart />
                    ) : (!loading && displayTableData.rows.length === 0 && (Object.keys(inlineFilters).length > 0 || (searchTerm && searchTerm.trim()))) ? (
                      <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '180px', py: 6 }}>
                        <Box sx={{ textAlign: 'center', width: '100%' }}>
                          <MDTypography variant="body1" color="text.secondary">{t("performance.no-products-match-current")} {Object.keys(inlineFilters).length > 0 && searchTerm && searchTerm.trim() ? t("performance.filters-and-search") : Object.keys(inlineFilters).length > 0 ? t("performance.filters") : t("performance.search")}.</MDTypography>
                          <MDTypography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                            {Object.keys(inlineFilters).length > 0 && (<><>{t("performance.try-adjusting-column-filters")} </><span style={{ color: '#1976d2', textDecoration: 'underline', cursor: 'pointer' }} onClick={() => { setInlineFilters({}); if (searchTerm && searchTerm.trim()) setSearchTerm(''); }}>{t("performance.clear-all-filters")}</span></>)}
                            {!(Object.keys(inlineFilters).length > 0) && searchTerm && searchTerm.trim() && (<><>{t("performance.try-different-search-term")} </><span style={{ color: '#1976d2', textDecoration: 'underline', cursor: 'pointer' }} onClick={() => setSearchTerm('')}>{t("performance.clear-search")}</span></>)}
                          </MDTypography>
                        </Box>
                      </Box>
                    ) : (
                      <DataTable
                        table={loading ? skeletonTableData : displayTableData}
                        entriesPerPage={{ defaultValue: 10, entries: [5, 10, 15, 20, 25] }}
                        canSearch={false}
                        showTotalEntries={true}
                        isSorted={!loading}
                        pagination={true}
                        initialState={{ hiddenColumns: Object.keys(columnVisibility).filter(key => columnVisibility[key] === false || key === 'product_name_text') }}
                        paginationProps={{ showEntriesSelector: true, entriesSelectorPosition: 'inline' }}
                      />
                    )}
                    {/* Summary Grid */}
                    {!loading && allProductData.length > 0 && (
                      <MDBox mt={3} >
                        <MDBox display="flex" alignItems="center" sx={{ mb: 3 }}>
                          <MDBox sx={{ width: '4px', height: '24px', backgroundColor: '#1976d2', borderRadius: '2px', mr: 2 }} />
                          <MDTypography variant="h6" color="secondary" fontWeight="regular" className="card-title-default" display="flex" alignItems="center">
                            {t("performance.catalog-summary")}
                          </MDTypography>
                        </MDBox>
                        <CatalogSummary 
                          summaryData={summaryData}
                          hasMetaConnection={hasMetaConnection}
                          hasGoogleAdsConnection={hasGoogleAdsConnection}
                          avgRoas={avgRoas}
                          loading={loading}
                          currency={allProductData?.[0]?.currency}
                        />
                      </MDBox>
                    )}
                  </MDBox>
                </Card>
              </MDBox>
              <Card elevation={0} sx={{ background: '#f5faff', borderRadius: 3, mt: 2 }}>
                <MDAlert color='light' dismissible={true} closeIconColor="#3f3f46" sx={{ p: 3, display: 'flex', alignItems: 'center', background: 'transparent', border: 'none', boxShadow: 'none', m: 0 }}>
                  <Icon fontSize="small" sx={{ color: '#0088FF', pr: 5 }}>information</Icon>
                  <MDTypography variant='caption' sx={{ fontSize: '12px' }}>{t('google-ads-products-info-disclaimer')}</MDTypography>
                </MDAlert>
              </Card>
            </Grid>
          </Grid>
        </MDBox>
      </DashboardLayout>
    );
  };
  
  export default ProductPerformance;