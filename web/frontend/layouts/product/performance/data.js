import dayjs from 'dayjs';
import { moneyFormatter } from "@/util";

/**
 * Fetches product performance data from the API
 * @param {Object} params - Request parameters
 * @param {string} params.start_date - Start date in YYYY-MM-DD format
 * @param {string} params.end_date - End date in YYYY-MM-DD format
 * @param {string} params.selectedShop - Selected shop identifier
 * @param {string} params.breakdown - Breakdown type (product_name, product_type, vendor)
 * @param {Object} params.applied_filters - Applied filters object
 * @param {Object} params.inlineFilters - Inline filters object
 * @param {Object} axiosInstance - Axios instance for making requests
 * @returns {Promise<Object>} API response data
 */
export const fetchProductData = async (params, axiosInstance) => {
  const { start_date, end_date, selectedShop, breakdown, applied_filters, inlineFilters } = params;
  
  if (!start_date || !end_date) {
    throw new Error('Start date and end date are required');
  }

  const reqData = {
    start_date: dayjs(start_date).format("YYYY-MM-DD"),
    end_date: dayjs(end_date).format("YYYY-MM-DD"),
    selectedShop: selectedShop,
    breakdown: breakdown,
    applied_filters: applied_filters,
    inline_filters: inlineFilters
  };

  const response = await axiosInstance.post('/api/product-performance', reqData);
  
  if (response.data && !response.data.error) {
    return response.data.data;
  } else {
    throw new Error(response.data?.error || 'Failed to fetch product data');
  }
};

/**
 * Loads column preferences from the backend
 * @param {string} selectedShop - Selected shop identifier
 * @param {Object} axiosInstance - Axios instance for making requests
 * @returns {Promise<Object|null>} Column preferences or null if not found
 */
export const loadColumnPreferences = async (selectedShop, axiosInstance) => {
  try {
    const reqData = selectedShop ? { selectedShop } : {};
    const response = await axiosInstance.post('/api/settings/column-preferences/get', reqData);
    return response.data?.columnPreferences || null;
  } catch (error) {
    console.error('Error loading column preferences:', error);
    return null;
  }
};

/**
 * Saves column preferences to the backend
 * @param {Object} preferences - Column preferences object
 * @param {string} selectedShop - Selected shop identifier
 * @param {Object} axiosInstance - Axios instance for making requests
 */
export const saveColumnPreferences = async (preferences, selectedShop, axiosInstance) => {
  try {
    const reqData = {
      columnPreferences: preferences,
      ...(selectedShop && { selectedShop })
    };
    await axiosInstance.post('/api/settings/column-preferences', reqData);
  } catch (error) {
    console.error('Error saving column preferences:', error);
  }
};

/**
 * Formats metric values based on type
 * @param {*} value - The value to format
 * @param {string} type - The type of metric (roas, currency, percentage)
 * @param {string} currency - Currency code
 * @param {Function} t - Translation function
 * @returns {string} Formatted value
 */
export const formatMetricValue = (value, type, currency, t) => {
  if (value === null || value === undefined) return '-';
  if (typeof value === 'string' && value === 'non-ad-driven') return t("performance.no-ad-spend");
  
  const num = parseFloat(value);
  if (isNaN(num)) return value;
  
  switch (type) {
    case 'roas':
      return `${Math.round(num)}x`;
    case 'currency':
      return moneyFormatter(num, currency);
    case 'percentage':
      return `${num.toFixed(2)}%`;
    default:
      return num.toLocaleString();
  }
};

/**
 * Gets the pill color based on title key
 * @param {string} titleKey - The title key to determine color for
 * @returns {string} Color code
 */
export const getPillColor = (titleKey) => {
  if (titleKey.endsWith('top-budget-wasting-products')) return '#ef4444';
  if (titleKey.endsWith('top-hero-products')) return '#22c55e';
  if (titleKey.endsWith('top-potential-products')) return '#eab308';
  return '#64748b';
};

/**
 * Gets product image URL with fallback
 * @param {Object} product - Product object
 * @returns {string} Image URL
 */
export const getProductImageUrl = (product) => {
  return product.product_image || "https://cdn.shopify.com/s/files/1/0533/2089/files/placeholder-images-image_large.png?format=webp&v=1530129081";
};

/**
 * Transforms raw product data for display
 * @param {Array} allProductData - Raw product data from API
 * @param {string} breakdown - Breakdown type
 * @returns {Array} Transformed data array
 */
export const transformProductData = (allProductData, breakdown) => {
  return allProductData.map((product) => {
    // Create searchable text based on breakdown type
    let searchableText = '';
    if (breakdown === 'product_name') {
      searchableText = `${product.name || ''} ${product.product_id || ''}`.trim();
    } else {
      searchableText = product.name || '';
    }
    
    return {
      ...product,
      product_name_text: searchableText,
    };
  });
};

/**
 * Generates initial column visibility settings
 * @param {boolean} showBlendedSpend - Whether to show blended spend column
 * @returns {Object} Initial column visibility object
 */
export const generateInitialColumnVisibility = (showBlendedSpend) => {
  const allColumnAccessors = [
    'product_cell',
    'product_name_text',
    'blended_catalog_roas', // Always show blended catalog ROAS
    ...(showBlendedSpend ? ['blended_catalog_spend'] : []), // Only show blended catalog spend when both integrations connected
    'meta_ads_spend',
    'gads_spend', 
    'order_count',
    'customer_count',
    'units_sold',
    'net_revenue',
    'avg_selling_price',
    'meta_ads_cpc',
    'meta_ads_cpm',
    'meta_ads_ctr',
    'meta_ads_clicks',
    'meta_ads_impressions',
    'gads_cpc',
    'gads_cpm',
    'gads_ctr',
    'gads_clicks',
    'gads_impressions',
    'gads_conversions',
    'gads_conversions_value'
  ];
  
  const initialVisibility = {};
  allColumnAccessors.forEach(accessor => {
    // Always hide product_name_text column (used for search only)
    initialVisibility[accessor] = accessor !== 'product_name_text';
  });
  
  return initialVisibility;
};

/**
 * Generates all available columns configuration
 * @param {boolean} showBlendedSpend - Whether to show blended spend column
 * @param {boolean} hasMetaConnection - Whether Meta connection exists
 * @param {boolean} hasGoogleAdsConnection - Whether Google Ads connection exists
 * @returns {Array} Array of column configurations
 */
export const generateAllColumns = (showBlendedSpend, hasMetaConnection, hasGoogleAdsConnection) => {
  let allColumns = [
    { accessor: 'product_cell' },
    { accessor: 'product_name_text' },
  ];

  // Always add blended_catalog_roas column
  allColumns.push({ accessor: 'blended_catalog_roas' });
        
  // Only add catalog blended spend when both integrations are connected
  if (showBlendedSpend) {
    allColumns.push({ accessor: 'blended_catalog_spend' });
  }

  allColumns.push(...[
    // Add Meta/Facebook columns only if Meta is connected
    ...(hasMetaConnection === true ? [
      { accessor: 'meta_ads_spend' },
      { accessor: 'meta_ads_cpc' },
      { accessor: 'meta_ads_cpm' },
      { accessor: 'meta_ads_ctr' },
      { accessor: 'meta_ads_clicks' },
      { accessor: 'meta_ads_impressions' },
    ] : []),
    
    // Add Google Ads columns only if Google Ads is connected
    ...(hasGoogleAdsConnection === true ? [
      { accessor: 'gads_spend' },
      { accessor: 'gads_cpc' },
      { accessor: 'gads_cpm' },
      { accessor: 'gads_ctr' },
      { accessor: 'gads_clicks' },
      { accessor: 'gads_impressions' },
      { accessor: 'gads_conversions' },
      { accessor: 'gads_conversions_value' },
    ] : []),
    
    // Always add Shopify columns (these should always be available)
    { accessor: 'order_count' },
    { accessor: 'customer_count' },
    { accessor: 'units_sold' },
    { accessor: 'net_revenue' },
    { accessor: 'avg_selling_price' },
  ]);

  return allColumns;
};

/**
 * Updates column visibility with new columns while preserving existing preferences
 * @param {Object} currentVisibility - Current visibility settings
 * @param {Array} allColumns - All available columns
 * @param {boolean} showBlendedSpend - Whether to show blended spend column
 * @returns {Object} Updated visibility settings and whether changes were made
 */
export const updateColumnVisibility = (currentVisibility, allColumns, showBlendedSpend) => {
  const updated = { ...currentVisibility };
  let hasChanges = false;
  
  allColumns.forEach(col => {
    // If this column doesn't have a saved preference, make it visible by default
    // Exception: product_name_text should always be hidden (used for search only)
    if (!(col.accessor in updated)) {
      updated[col.accessor] = col.accessor !== 'product_name_text';
      hasChanges = true;
    }
  });
  
  // Always force blended_catalog_roas to be visible
  if (updated.blended_catalog_roas !== true) {
    updated.blended_catalog_roas = true;
    hasChanges = true;
  }
  
  // Force blended_catalog_spend to be visible only when both integrations are connected
  if (showBlendedSpend) {
    if (updated.blended_catalog_spend !== true) {
      updated.blended_catalog_spend = true;
      hasChanges = true;
    }
  } else {
    // Reset blended_catalog_spend to false when showBlendedSpend is false
    if (updated.blended_catalog_spend !== false) {
      updated.blended_catalog_spend = false;
      hasChanges = true;
    }
  }
  
  return { updated, hasChanges };
};

/**
 * Calculates summary data from raw backend data
 * @param {Array} rawData - Raw product data
 * @param {boolean} showBlendedSpend - Whether to calculate blended spend
 * @param {boolean} hasMetaConnection - Whether Meta connection exists
 * @param {boolean} hasGoogleAdsConnection - Whether Google Ads connection exists
 * @returns {Array} Summary data array
 */
export const calculateSummaryFromRawData = (rawData, showBlendedSpend, hasMetaConnection, hasGoogleAdsConnection) => {
  if (!rawData || rawData.length === 0) return [];

  // Helper function to extract numeric value from a field
  const getVal = (row, column) => {
    const rawField = column + '_raw';
    if (rawField in row && typeof row[rawField] === 'number' && !isNaN(row[rawField])) return row[rawField];
    const val = row[column];
    if (typeof val === 'number' && !isNaN(val)) return val;
    if (typeof val === 'string') {
      const num = parseFloat(val.replace(/[^\d.-]+/g, ''));
      return isNaN(num) ? 0 : num;
    }
    return 0;
  };

  // Calculate only the fields that are actually used in CatalogSummary
  const summary = {
    net_revenue: 0,
    blended_catalog_spend: 0,
    meta_ads_spend: 0,
    gads_spend: 0
  };

  // Calculate net revenue total
  summary.net_revenue = rawData.reduce((sum, row) => sum + getVal(row, 'net_revenue'), 0);

  // Calculate spend totals based on connected integrations
  if (showBlendedSpend) {
    // Calculate blended spend (Meta + Google Ads)
    summary.blended_catalog_spend = rawData.reduce((sum, row) => {
      const metaSpend = getVal(row, 'meta_ads_spend');
      const gadsSpend = getVal(row, 'gads_spend');
      return sum + metaSpend + gadsSpend;
    }, 0);
  } else {
    // Calculate individual spend totals
    if (hasMetaConnection) {
      summary.meta_ads_spend = rawData.reduce((sum, row) => sum + getVal(row, 'meta_ads_spend'), 0);
    }
    if (hasGoogleAdsConnection) {
      summary.gads_spend = rawData.reduce((sum, row) => sum + getVal(row, 'gads_spend'), 0);
    }
  }

  // Format values for display
  const formatValue = (value, column) => {
    if (typeof value === 'string') return value;
    if (value === 0) return '0';
    
    if (column.includes('revenue') || column.includes('spend')) {
      const currency = rawData[0]?.currency;
      return moneyFormatter(value, currency);
    }
    return Math.round(value).toLocaleString();
  };

  // Return formatted summary data
  return [{
    net_revenue: formatValue(summary.net_revenue, 'net_revenue'),
    blended_catalog_spend: formatValue(summary.blended_catalog_spend, 'blended_catalog_spend'),
    meta_ads_spend: formatValue(summary.meta_ads_spend, 'meta_ads_spend'),
    gads_spend: formatValue(summary.gads_spend, 'gads_spend')
  }];
};

/**
 * Applies inline filters to data
 * @param {Array} data - Data to filter
 * @param {Object} inlineFilters - Filter configuration object
 * @returns {Array} Filtered data
 */
export const applyInlineFilters = (data, inlineFilters) => {
  if (!inlineFilters || Object.keys(inlineFilters).length === 0) {
    return data;
  }

  return data.filter(row => {
    return Object.entries(inlineFilters).every(([col, filters]) => {
      if (!Array.isArray(filters) || filters.length === 0) return true;
      return filters.every(filterObj => {
        if (!filterObj) return true;
        // For product_cell, filter on product_name_text
        const cellValue = col === 'product_cell' ? row['product_name_text'] : row[col];
        const op = filterObj.operator || filterObj.type;
        if (op === 'gt') {
          return parseFloat(cellValue) > parseFloat(filterObj.value);
        } else if (op === 'lt') {
          return parseFloat(cellValue) < parseFloat(filterObj.value);
        } else if (op === 'gte') {
          return parseFloat(cellValue) >= parseFloat(filterObj.value);
        } else if (op === 'lte') {
          return parseFloat(cellValue) <= parseFloat(filterObj.value);
        } else if (op === 'equals') {
          return (cellValue || '').toString() === (filterObj.value || '').toString();
        } else if (op === 'notEquals') {
          return (cellValue || '').toString() !== (filterObj.value || '').toString();
        } else if (op === 'range') {
          const num = parseFloat(cellValue);
          const min = filterObj.minValue !== undefined ? parseFloat(filterObj.minValue) : undefined;
          const max = filterObj.maxValue !== undefined ? parseFloat(filterObj.maxValue) : undefined;
          if (isNaN(num)) return false;
          if (min !== undefined && num < min) return false;
          if (max !== undefined && num > max) return false;
          return true;
        } else if (op === 'contains') {
          return (cellValue || '').toString().toLowerCase().includes((filterObj.value || '').toString().toLowerCase());
        } else if (op === 'date_range') {
          const cellDate = cellValue ? new Date(cellValue) : null;
          const min = filterObj.min ? new Date(filterObj.min) : null;
          const max = filterObj.max ? new Date(filterObj.max) : null;
          if (!cellDate || isNaN(cellDate)) return false;
          if (min && cellDate < min) return false;
          if (max && cellDate > max) return false;
          return true;
        } else if (filterObj.value !== undefined) {
          return (cellValue || '').toString() === (filterObj.value || '').toString();
        }
        return true;
      });
    });
  });
};

/**
 * Applies search filter to data
 * @param {Array} data - Data to search
 * @param {string} searchTerm - Search term
 * @returns {Array} Filtered data
 */
export const applySearchFilter = (data, searchTerm) => {
  if (!searchTerm || !searchTerm.trim()) {
    return data;
  }

  const searchLower = searchTerm.toLowerCase().trim();
  return data.filter(row => {
    const searchableText = row.product_name_text || '';
    return searchableText.toLowerCase().includes(searchLower);
  });
};

/**
 * Processes insights data from backend response
 * @param {Object} insightsData - Raw insights data from backend
 * @returns {Object} Processed insights data with categorized products
 */
export const processInsightsData = (insightsData) => {
  if (!insightsData || typeof insightsData !== 'object') {
    return { hero: [], potential: [], wasters: [] };
  }
  return {
    hero: insightsData.top_hero_products || [],
    potential: insightsData.top_potential_products || [],
    wasters: insightsData.top_budget_wasting_products || []
  };
};

/**
 * Generates search placeholder text based on breakdown type
 * @param {string} breakdown - Breakdown type
 * @param {Function} t - Translation function
 * @returns {string} Placeholder text
 */
export const generateSearchPlaceholder = (breakdown, t) => {
  const baseText = t("performance.search-by");
  switch (breakdown) {
    case 'product_name':
      return `${baseText} ${t("performance.product-name-id")}`;
    case 'product_type':
      return `${baseText} ${t("performance.product-type")}`;
    case 'vendor':
      return `${baseText} ${t("performance.vendor")}`;
    default:
      return `${baseText} ${t("performance.product-name-id")}`;
  }
};

/**
 * Processes API response data and extracts relevant information
 * @param {Object} responseData - Raw API response data
 * @returns {Object} Processed data object
 */
export const processApiResponse = (responseData) => {
  const allData = responseData?.items || [];
  const blendedSpendValue = responseData?.show_blended_spend ?? false;
  const hasMetaConnection = responseData?.has_meta_connection ?? false;
  const hasGoogleAdsConnection = responseData?.has_google_ads_connection ?? false;
  const avgRoas = responseData?.avg_roas || 0;
  const avgAdSpend = responseData?.avg_ad_spend || 0;
  const insights = responseData?.insights || null;

  return {
    allData,
    blendedSpendValue,
    hasMetaConnection,
    hasGoogleAdsConnection,
    avgRoas,
    avgAdSpend,
    insights
  };
};

/**
 * Creates default data structure for empty/error states
 * @returns {Object} Default empty data structure
 */
export const createDefaultDataStructure = () => {
  return {
    allData: [],
    blendedSpendValue: false,
    hasMetaConnection: false,
    hasGoogleAdsConnection: false,
    avgRoas: 0,
    avgAdSpend: 0,
    insights: null
  };
};

/**
 * Validates if data fetching should proceed based on configuration
 * @param {Object} config - Configuration object
 * @param {boolean} config.shopConfigLoading - Whether shop config is loading
 * @param {boolean} config.isSubscriptionInActive - Whether subscription is inactive
 * @param {string} config.selectedShop - Selected shop
 * @param {string} config.shopDomain - Shop domain from config
 * @returns {boolean} Whether data fetching should proceed
 */
export const shouldFetchData = (config) => {
  const { shopConfigLoading, isSubscriptionInActive, selectedShop, shopDomain } = config;
  return !shopConfigLoading && !isSubscriptionInActive && !(selectedShop && shopDomain !== selectedShop);
};
