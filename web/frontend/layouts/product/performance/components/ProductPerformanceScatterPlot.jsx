import React, { useMemo, useState, useCallback, memo, useDeferredValue, useEffect } from "react";
import {
  Chart as ChartJS,
  LinearScale,
  PointElement,
  LineElement,
  Tooltip,
  Legend
} from 'chart.js';
import { Scatter } from 'react-chartjs-2';
import { Skeleton, Divider } from "@mui/material";
import { useTranslation } from "react-i18next";
import MDBox from "@/components/MDBox";
import MDInput from "@/components/MDInput";
import MDButton from "@/components/MDButton";
import EmptyChart from "@/components/EmptyChart";

// Register Chart.js components
ChartJS.register(
  LinearScale,
  PointElement,
  LineElement,
  Tooltip,
  Legend
);

// Custom plugin for reference lines
const referenceLinePlugin = {
  id: 'referenceLines',
  afterDatasetsDraw(chart, args, options) {
    const { ctx } = chart;
    const { chartArea: { top, bottom, left, right } } = chart;
    
    if (options.lines) {
      options.lines.forEach((line) => {
        ctx.save();
        ctx.strokeStyle = line.color;
        ctx.setLineDash(line.dash || []);
        ctx.lineWidth = line.width || 1;
        ctx.globalAlpha = line.opacity || 1;
        
        ctx.beginPath();
        if (line.type === 'horizontal') {
          const yPos = chart.scales.y.getPixelForValue(line.value);
          ctx.moveTo(left, yPos);
          ctx.lineTo(right, yPos);
          
          // Add label
          if (line.label) {
            ctx.fillStyle = line.color;
            ctx.font = 'bold 12px Arial';
            ctx.fillText(line.label, right - ctx.measureText(line.label).width - 10, yPos - 5);
          }
        } else if (line.type === 'vertical') {
          const xPos = chart.scales.x.getPixelForValue(line.value);
          ctx.moveTo(xPos, top);
          ctx.lineTo(xPos, bottom);
          
          // Add label
          if (line.label) {
            ctx.fillStyle = line.color;
            ctx.font = 'bold 12px Arial';
            ctx.save();
            ctx.translate(xPos + 5, top + 20);
            ctx.fillText(line.label, 0, 0);
            ctx.restore();
          }
        }
        ctx.stroke();
        ctx.restore();
      });
    }
  }
};

ChartJS.register(referenceLinePlugin);

// Helper function to get tooltip data for a product
const getTooltipData = (product, t, showBlendedSpend) => {
  const valid = v => v !== undefined && v !== null && v !== '-' && v !== '';
  
  const imageUrl = 
    product.featured_image_url ||
    product.product_image ||
    product.image ||
    product.product_image_url ||
    product.image_url ||
    product.featured_image ||
    product.src ||
    (product.product_cell?.props?.image);

  // Spend and ROAS logic
  const blended = product.blended_catalog_spend;
  const meta = product.meta_ads_spend;
  const gads = product.gads_spend;
  let spendDisplay = '-';
  if (valid(blended)) {
    spendDisplay = blended;
  } else if (valid(meta) && valid(gads)) {
    spendDisplay = `${meta} + ${gads}`;
  } else if (valid(meta)) {
    spendDisplay = meta;
  } else if (valid(gads)) {
    spendDisplay = gads;
  }

  const blendedRoas = product.blended_catalog_roas;
  const metaRaw = Number(product.meta_ads_spend_raw) || 0;
  const gadsRaw = Number(product.gads_spend_raw) || 0;
  const revenueRaw = Number(product.net_revenue_raw) || 0;
  let roasDisplay = '-';
  if (valid(blendedRoas) && blendedRoas !== 'non-ad-driven') {
    roasDisplay = blendedRoas;
  } else if (metaRaw + gadsRaw > 0) {
    roasDisplay = (revenueRaw / (metaRaw + gadsRaw)).toFixed(2);
  } else if (revenueRaw > 0 && metaRaw + gadsRaw === 0) {
    roasDisplay = '∞';
  }

  return {
    imageUrl,
    spendDisplay,
    roasDisplay,
    title: product.product_title || product.name || product.product_name || t("performance.unknown-product"),
    revenue: product.net_revenue ?? "-",
    orders: product.order_count ?? "-"
  };
};

function ProductPerformanceScatterPlot({
  data,
  loading,
  showBlendedSpend,
  avgRoas,
  avgAdSpend = 0,
  currency,
  moneyFormatter
}) {
  const { t } = useTranslation();

  // Range filter state (user inputs)
  const [minSpend, setMinSpend] = useState("");
  const [maxSpend, setMaxSpend] = useState("");
  const [minRoasInput, setMinRoasInput] = useState("");
  const [maxRoasInput, setMaxRoasInput] = useState("");

  // Set initial max values to 5x average when data loads
  const [hasSetInitialValues, setHasSetInitialValues] = useState(false);
  
  // Reset hasSetInitialValues when data, avgAdSpend, or avgRoas changes
  React.useEffect(() => {
    setHasSetInitialValues(false);
  }, [data, avgAdSpend, avgRoas]);
  
  React.useEffect(() => {
    if (!hasSetInitialValues && data && data.length > 0 && Number.isFinite(avgAdSpend) && Number.isFinite(avgRoas)) {
      if (avgAdSpend > 0) {
        const maxSpendValue = avgAdSpend * 5;
        // Round to nearest 50 for spend values
        const roundedMaxSpend = Math.ceil(maxSpendValue / 50) * 50;
        setMaxSpend(roundedMaxSpend.toString());
      }
      if (avgRoas > 0) {
        const maxRoasValue = avgRoas * 5;
        // Round to nearest 50 for ROAS values
        const roundedMaxRoas = Math.ceil(maxRoasValue / 50) * 50;
        setMaxRoasInput(roundedMaxRoas.toString());
      }
      setHasSetInitialValues(true);
    }
  }, [hasSetInitialValues, data, avgAdSpend, avgRoas]);

  const numericOr = useCallback((val, fallback) => {
    const n = parseFloat(val);
    return Number.isFinite(n) ? n : fallback;
  }, []);

  // Deferred filter values for better performance
  const deferredMinSpend = useDeferredValue(minSpend);
  const deferredMaxSpend = useDeferredValue(maxSpend);
  const deferredMinRoas = useDeferredValue(minRoasInput);
  const deferredMaxRoas = useDeferredValue(maxRoasInput);

  // Derived numeric range values (using deferred values)
  const minSpendValRaw = numericOr(deferredMinSpend, 0);
  const maxSpendValRaw = numericOr(deferredMaxSpend, Infinity);
  const minRoasValRaw = numericOr(deferredMinRoas, 0);
  const maxRoasValRaw = numericOr(deferredMaxRoas, Infinity); // no implicit cap

  // Normalize ranges so inverted inputs still work
  const minSpendVal = Math.min(minSpendValRaw, maxSpendValRaw);
  const maxSpendVal = Math.max(minSpendValRaw, maxSpendValRaw);
  const minRoasVal = Math.min(minRoasValRaw, maxRoasValRaw);
  const maxRoasVal = Math.max(minRoasValRaw, maxRoasValRaw);

  const rawMappedData = useMemo(() => {
    if (!data || data.length === 0) return [];
    return data.map((item) => {
      if (showBlendedSpend) {
  const roas = typeof item.blended_catalog_roas_raw === "number" ? item.blended_catalog_roas_raw : 0.01;
  const adSpend = typeof item.blended_catalog_spend_raw === "number" ? item.blended_catalog_spend_raw : 0.01;
        return {
          ...item,
          x: Math.max(adSpend, 0.01),
          y: Math.max(roas, 0.01),
          z: typeof item.net_revenue_raw === "number" ? item.net_revenue_raw : 0
        };
      } else {
        const metaSpend = typeof item.meta_ads_spend_raw === "number" ? item.meta_ads_spend_raw : 0;
        const gadsSpend = typeof item.gads_spend_raw === "number" ? item.gads_spend_raw : 0;
        const totalSpend = metaSpend + gadsSpend;
        const revenue = typeof item.net_revenue_raw === "number" ? item.net_revenue_raw : 0;
        const roas = totalSpend > 0 ? revenue / totalSpend : 0.01;
        return {
          ...item,
          x: Math.max(totalSpend, 0.01),
          y: Math.max(roas, 0.01),
          z: revenue
        };
      }
    });
  }, [data, showBlendedSpend]);

  const scatterData = useMemo(() => {
    return rawMappedData.filter((item) =>
      item.x >= minSpendVal &&
      item.x <= maxSpendVal &&
      item.y >= minRoasVal &&
      item.y <= maxRoasVal
    );
  }, [rawMappedData, minSpendVal, maxSpendVal, minRoasVal, maxRoasVal]);

  // Chart.js data configuration
  const chartData = useMemo(() => ({
    datasets: [
      {
        label: 'Products',
        data: scatterData.map((item) => ({
          x: item.x,
          y: item.y,
          product: item // Store the full product data for tooltips
        })),
        backgroundColor: 'rgba(25, 118, 210, 0.7)',
        borderColor: '#1976d2',
        pointRadius: 4,
        pointHoverRadius: 6,
      }
    ]
  }), [scatterData]);

  // Memoize axis labels - must be before chartOptions
  const { xAxisLabel, yAxisLabel } = useMemo(() => ({
    xAxisLabel: showBlendedSpend ? t("performance.blended-catalog-ad-spend") : t("performance.total-ad-spend"),
    yAxisLabel: showBlendedSpend ? t("performance.blended-catalog-roas") : t("performance.calculated-roas")
  }), [showBlendedSpend, t]);

  // Memoize domain calculations - must be before chartOptions
  const { xDomain, yDomain } = useMemo(() => {
    if (scatterData.length === 0) {
      // Default domains when no data is available
      return {
        xDomain: [0, 1],
        yDomain: [0, 1]
      };
    }
    
    const xs = scatterData.map((d) => d.x);
    const ys = scatterData.map((d) => d.y);
    const minXData = Math.min(...xs);
    const maxXData = Math.max(...xs);
    const minYData = Math.min(...ys);
    const maxYData = Math.max(...ys);
    
    // Determine effective max values - use 5x average if no user filters applied
    let effectiveMaxSpend = maxXData;
    let effectiveMaxRoas = maxYData;
    
    // If no user filter is applied (maxSpendVal is Infinity), use 5x average as default max
    if (maxSpendVal === Infinity && Number.isFinite(avgAdSpend) && avgAdSpend > 0) {
      const calculated5xSpend = avgAdSpend * 5;
      effectiveMaxSpend = Math.max(maxXData, calculated5xSpend);
    } else if (maxSpendVal !== Infinity) {
      effectiveMaxSpend = Math.max(maxSpendVal, maxXData);
    }
    
    // If no user filter is applied (maxRoasVal is Infinity), use 5x average as default max
    if (maxRoasVal === Infinity && Number.isFinite(avgRoas) && avgRoas > 0) {
      const calculated5xRoas = avgRoas * 5;
      effectiveMaxRoas = Math.max(maxYData, calculated5xRoas);
    } else if (maxRoasVal !== Infinity) {
      effectiveMaxRoas = Math.max(maxRoasVal, maxYData);
    }
    
    return {
      xDomain: [Math.min(minSpendVal || 0, minXData), effectiveMaxSpend],
      yDomain: [Math.min(minRoasVal || 0, minYData), effectiveMaxRoas]
    };
  }, [scatterData, minSpendVal, maxSpendVal, minRoasVal, maxRoasVal, avgAdSpend, avgRoas]);

  // Chart.js options configuration
  const chartOptions = useMemo(() => {
    const referenceLines = [];
    
    // Add average ROAS line
    if (Number.isFinite(avgRoas) && avgRoas > 0) {
      referenceLines.push({
        type: 'horizontal',
        value: avgRoas,
        color: '#e65100', // Darker orange
        dash: [8, 4],
        width: 2,
        label: `${t("performance.average-roas")}: ${avgRoas.toFixed(1)}`
      });
    }
    
    // Add average spend line
    if (Number.isFinite(avgAdSpend) && avgAdSpend > 0) {
      referenceLines.push({
        type: 'vertical',
        value: avgAdSpend,
        color: '#1565c0', // Darker blue
        dash: [8, 4],
        width: 2,
        label: `${t("performance.average-spend")}: ${moneyFormatter ? moneyFormatter(avgAdSpend, currency) : avgAdSpend.toLocaleString()}`
      });
    }
    
    return {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        intersect: false,
      },
      layout: {
        padding: {
          top: 30,
          right: 40,
          bottom: 80,
          left: 80
        }
      },
      scales: {
        x: {
          type: 'linear',
          position: 'bottom',
          title: {
            display: true,
            text: xAxisLabel,
            font: {
              size: 12,
              weight: '600'
            },
            color: '#344767'
          },
          ticks: {
            font: {
              size: 11
            },
            color: '#666',
            callback: function(value) {
              return moneyFormatter ? moneyFormatter(value, currency) : value.toLocaleString();
            }
          },
          grid: {
            color: '#e0e0e0',
            drawBorder: false,
          },
          min: xDomain[0],
          max: xDomain[1]
        },
        y: {
          type: 'linear',
          title: {
            display: true,
            text: yAxisLabel,
            font: {
              size: 12,
              weight: '600'
            },
            color: '#344767'
          },
          ticks: {
            font: {
              size: 11
            },
            color: '#666',
            callback: function(value) {
              return value.toFixed(1);
            }
          },
          grid: {
            color: '#e0e0e0',
            drawBorder: false,
          },
          min: yDomain[0],
          max: yDomain[1]
        }
      },
      plugins: {
        legend: {
          display: false
        },
        referenceLines: {
          lines: referenceLines
        },
        tooltip: {
          enabled: false, // We'll use external tooltip
          external: function(context) {
            // Get tooltip element
            let tooltipEl = document.getElementById('chartjs-tooltip');

            // Create element on first render
            if (!tooltipEl) {
              tooltipEl = document.createElement('div');
              tooltipEl.id = 'chartjs-tooltip';
              tooltipEl.innerHTML = '<div></div>';
              document.body.appendChild(tooltipEl);
            }

            // Hide if no tooltip
            const tooltipModel = context.tooltip;
            if (tooltipModel.opacity === 0) {
              tooltipEl.style.opacity = 0;
              return;
            }

            // Set caret Position
            tooltipEl.classList.remove('above', 'below', 'no-transform');
            if (tooltipModel.yAlign) {
              tooltipEl.classList.add(tooltipModel.yAlign);
            } else {
              tooltipEl.classList.add('no-transform');
            }

            if (tooltipModel.body) {
              const dataPoint = tooltipModel.dataPoints[0];
              const product = dataPoint.raw.product;
              const tooltipData = getTooltipData(product, t, showBlendedSpend);

              const imageHtml = tooltipData.imageUrl 
                ? `<div style="display: flex; justify-content: center; margin-bottom: 8px;">
                     <img width="120" height="120" 
                          src="${tooltipData.imageUrl}" 
                          alt="${tooltipData.title}"
                          style="object-fit: cover; border-radius: 6px; border: 1px solid #eee;"
                          onerror="this.src='https://cdn.shopify.com/s/files/1/0533/2089/files/placeholder-images-image_large.png?format=webp&v=1530129081'">
                   </div>`
                : `<div style="display: flex; justify-content: center; margin-bottom: 8px; padding: 10px; background-color: #f5f5f5; border-radius: 6px;">
                     <span style="color: #999; font-size: 12px;">${t("performance.no-image-available")}</span>
                   </div>`;

              const innerHtml = `
                <div style="background: white; border: 1px solid #ddd; border-radius: 8px; padding: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); max-width: 300px; user-select: none;">
                  ${imageHtml}
                  <div style="margin-bottom: 8px;">
                    <span style="color: #1976d2; font-size: 14px; font-weight: 600;">
                      ${tooltipData.title}
                    </span>
                  </div>
                  <div style="font-size: 12px; line-height: 1.4;">
                    <div style="margin-bottom: 4px;">
                      <span style="font-weight: 500;">${t("performance.ad-spend")}:</span> <b>${tooltipData.spendDisplay}</b>
                    </div>
                    <div style="margin-bottom: 4px;">
                      <span style="font-weight: 500;">${t("performance.roas")}:</span> <b>${tooltipData.roasDisplay}</b>
                    </div>
                    <div style="margin-bottom: 4px;">
                      <span style="font-weight: 500;">${t("performance.revenue")}:</span> <b>${tooltipData.revenue}</b>
                    </div>
                    <div style="margin-bottom: 4px;">
                      <span style="font-weight: 500;">${t("performance.orders")}:</span> <b>${tooltipData.orders}</b>
                    </div>
                  </div>
                </div>
              `;

              tooltipEl.innerHTML = innerHtml;
            }

            const position = context.chart.canvas.getBoundingClientRect();

            // Display, position, and set styles for font
            tooltipEl.style.opacity = 1;
            tooltipEl.style.position = 'absolute';
            tooltipEl.style.font = '12px Arial';
            tooltipEl.style.padding = '8px';
            tooltipEl.style.pointerEvents = 'none';
            tooltipEl.style.zIndex = 9999;

            // Position tooltip to top-right of cursor
            const tooltipWidth = 300; // approximate width of tooltip
            const tooltipHeight = 200; // approximate height of tooltip
            const offsetX = 15; // small offset from cursor
            const offsetY = -15; // small offset from cursor
            
            let leftPos = position.left + window.pageXOffset + tooltipModel.caretX + offsetX;
            let topPos = position.top + window.pageYOffset + tooltipModel.caretY + offsetY - tooltipHeight;
            
            // Adjust if tooltip would overflow viewport
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;
            
            // If overflowing right edge, position to left of cursor
            if (leftPos + tooltipWidth > viewportWidth - 20) {
              leftPos = position.left + window.pageXOffset + tooltipModel.caretX - tooltipWidth - offsetX;
            }
            
            // If overflowing top edge, position below cursor
            if (topPos < 20) {
              topPos = position.top + window.pageYOffset + tooltipModel.caretY + offsetX;
            }
            
            tooltipEl.style.left = leftPos + 'px';
            tooltipEl.style.top = topPos + 'px';
          }
        }
      }
    };
  }, [xAxisLabel, yAxisLabel, xDomain, yDomain, moneyFormatter, currency, t, showBlendedSpend, avgRoas, avgAdSpend]);

  // Cleanup tooltip on unmount
  useEffect(() => {
    return () => {
      const tooltipEl = document.getElementById('chartjs-tooltip');
      if (tooltipEl) {
        tooltipEl.remove();
      }
    };
  }, []);

  const resetFilters = useCallback(() => {
    setMinSpend("");
    setMinRoasInput("");
    // Reset max values to 5x average (rounded)
    if (Number.isFinite(avgAdSpend) && avgAdSpend > 0) {
      const maxSpendValue = avgAdSpend * 5;
      const roundedMaxSpend = Math.ceil(maxSpendValue / 50) * 50;
      setMaxSpend(roundedMaxSpend.toString());
    } else {
      setMaxSpend("");
    }
    if (Number.isFinite(avgRoas) && avgRoas > 0) {
      const maxRoasValue = avgRoas * 5;
      const roundedMaxRoas = Math.ceil(maxRoasValue / 50) * 50;
      setMaxRoasInput(roundedMaxRoas.toString());
    } else {
      setMaxRoasInput("");
    }
  }, [avgAdSpend, avgRoas]);

  const handleMinSpendChange = useCallback((e) => {
    const value = e.target.value;
    // Allow empty string, numbers, and decimal points
    if (value === '' || /^\d*\.?\d*$/.test(value)) {
      setMinSpend(value);
    }
  }, []);
  
  const handleMaxSpendChange = useCallback((e) => {
    const value = e.target.value;
    // Allow empty string, numbers, and decimal points
    if (value === '' || /^\d*\.?\d*$/.test(value)) {
      setMaxSpend(value);
    }
  }, []);
  
  const handleMinRoasChange = useCallback((e) => {
    const value = e.target.value;
    // Allow empty string, numbers, and decimal points
    if (value === '' || /^\d*\.?\d*$/.test(value)) {
      setMinRoasInput(value);
    }
  }, []);
  
  const handleMaxRoasChange = useCallback((e) => {
    const value = e.target.value;
    // Allow empty string, numbers, and decimal points
    if (value === '' || /^\d*\.?\d*$/.test(value)) {
      setMaxRoasInput(value);
    }
  }, []);


  if (loading) {
    return (
      <div style={{ position: "relative" }}>
        {/* Divider */}
        <Divider sx={{ mb: 2, borderColor: '#e0e0e0' }} />
        
        {/* Skeleton Loading State */}
        <div style={{ width: '100%', height: 500 }}>
          <Skeleton 
            variant="rectangular" 
            width="100%" 
            height="100%" 
            animation="wave"
            sx={{ 
              borderRadius: 2,
              bgcolor: '#f5f5f5'
            }} 
          />
        </div>
      </div>
    );
  }

  // If the store genuinely has no data at all, show legacy empty state (without filters UI)
  if (!rawMappedData || rawMappedData.length === 0) {
    return (
      <div style={{ position: "relative" }}>
        {/* Divider */}
        <Divider sx={{ mb: 2, borderColor: '#e0e0e0' }} />
        <EmptyChart />
      </div>
    );
  }
  // Otherwise, keep controls visible even if filters exclude all points.

  return (
    <div style={{ position: "relative" }}>
      {/* Divider */}
      <Divider sx={{ mb: 2, borderColor: '#e0e0e0' }} />
      
      {/* Range Filters */}
      <div style={{ 
        display: 'flex', 
        flexWrap: 'wrap', 
        gap: '12px', 
        marginBottom: '12px', 
        alignItems: 'flex-end',
        justifyContent: { xs: 'center', sm: 'flex-start' }
      }}>
        <div style={{ display: 'flex', flexDirection: 'column', minWidth: '80px' }}>
          <label style={{ fontSize: 12, fontWeight: 600, color: '#555', marginBottom: 4 }}>{t("performance.min-spend")}</label>
          <MDInput
            type="number"
            value={minSpend}
            onChange={handleMinSpendChange}
            placeholder="0"
            size="small"
            sx={{ 
              width: { xs: 80, sm: 100 }, 
              '& .MuiInputBase-input': { 
                fontSize: '12px', 
                padding: { xs: '6px 8px', sm: '8px 12px' } 
              } 
            }}
            inputProps={{ min: 0 }}
          />
        </div>
        <div style={{ display: 'flex', flexDirection: 'column', minWidth: '80px' }}>
          <label style={{ fontSize: 12, fontWeight: 600, color: '#555', marginBottom: 4 }}>{t("performance.max-spend")}</label>
          <MDInput
            type="number"
            value={maxSpend}
            onChange={handleMaxSpendChange}
            placeholder="∞"
            size="small"
            sx={{ 
              width: { xs: 80, sm: 100 }, 
              '& .MuiInputBase-input': { 
                fontSize: '12px', 
                padding: { xs: '6px 8px', sm: '8px 12px' } 
              } 
            }}
            inputProps={{ min: 0 }}
          />
        </div>
        <div style={{ display: 'flex', flexDirection: 'column', minWidth: '80px' }}>
          <label style={{ fontSize: 12, fontWeight: 600, color: '#555', marginBottom: 4 }}>{t("performance.min-roas")}</label>
          <MDInput
            type="number"
            value={minRoasInput}
            onChange={handleMinRoasChange}
            placeholder="0"
            step="0.1"
            size="small"
            sx={{ 
              width: { xs: 80, sm: 100 }, 
              '& .MuiInputBase-input': { 
                fontSize: '12px', 
                padding: { xs: '6px 8px', sm: '8px 12px' } 
              } 
            }}
            inputProps={{ min: 0 }}
          />
        </div>
        <div style={{ display: 'flex', flexDirection: 'column', minWidth: '80px' }}>
          <label style={{ fontSize: 12, fontWeight: 600, color: '#555', marginBottom: 4 }}>{t("performance.max-roas")}</label>
          <MDInput
            type="number"
            value={maxRoasInput}
            onChange={handleMaxRoasChange}
            placeholder="∞"
            step="0.1"
            size="small"
            sx={{ 
              width: { xs: 80, sm: 100 }, 
              '& .MuiInputBase-input': { 
                fontSize: '12px', 
                padding: { xs: '6px 8px', sm: '8px 12px' } 
              } 
            }}
            inputProps={{ min: 0 }}
          />
        </div>
        <MDButton
          onClick={resetFilters}
          variant="outlined"
          size="small"
          color="secondary"
          sx={{ 
            height: 32, 
            fontSize: '12px', 
            fontWeight: 600,
            minWidth: { xs: 50, sm: 60 },
            textTransform: 'none'
          }}
        >
          {t("performance.reset")}
        </MDButton>
      </div>
      <div style={{ width: '100%', height: 500, position: 'relative' }}>
        {scatterData.length > 0 ? (
          <Scatter data={chartData} options={chartOptions} />
        ) : (
          <MDBox display="flex" flexDirection="column" alignItems="center" justifyContent="center" height="100%" border="1px dashed #ccc" borderRadius={2} p={3} sx={{ background: '#fafafa' }}>
            <div style={{ fontSize: 14, fontWeight: 600, color: '#555', marginBottom: 6 }}>{t("performance.no-products-match")}</div>
            <div style={{ fontSize: 12, color: '#777', marginBottom: 12 }}>{t("performance.adjust-range-inputs")}</div>
            <MDButton
              onClick={resetFilters}
              variant="contained"
              size="small"
              color="primary"
              sx={{ 
                fontSize: '12px', 
                fontWeight: 600,
                textTransform: 'none'
              }}
            >
              {t("performance.reset-filters")}
            </MDButton>
          </MDBox>
        )}
      </div>
    </div>
  );
}

// Memoize the component and set display name
const MemoizedProductPerformanceScatterPlot = memo(ProductPerformanceScatterPlot);
MemoizedProductPerformanceScatterPlot.displayName = 'ProductPerformanceScatterPlot';

export default MemoizedProductPerformanceScatterPlot;
