import React from 'react';
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDButton from "@/components/MDButton";
import { Box, Typography, Avatar, Tooltip, Divider, Dialog } from '@mui/material';
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import { useTranslation } from 'react-i18next';

const DialogBox = NiceModal.create(({ content }) => {
    const modal = useModal();
    const { t } = useTranslation();
    return (
        <Dialog
            open={modal.visible}
            onClose={modal.hide}
            sx={{ minWidth: "200px" }}
            TransitionProps={{
                onExited: () => modal.remove(),
            }}
        >
            {content}
        </Dialog>
    );
});

const ProductCell = ({ image, name, sku, breakdown }) => {
    const { t } = useTranslation();
    const handleImgError = (e) => { e.target.onerror = null; e.target.src = "https://cdn.shopify.com/s/files/1/0533/2089/files/placeholder-images-image_large.png?format=webp&v=1530129081"; }

    if (['product_type', 'product_vendor'].includes(breakdown)) {
        image = null;
    }

    const showDialog = () => {
        NiceModal.show(DialogBox, {
            content: (
                <MDBox sx={{ overflow: "scroll", borderRadius: 0, pb: 1 }} p={1}>
                    <MDTypography variant="h6" color="dark" fontWeight="regular" mb={0.5} p={1}>
                        {name}
                    </MDTypography>
                    <MDTypography variant="caption" color="text.secondary" display="block" mb={1} px={1}>
                        ID: {sku}
                    </MDTypography>
                    <Divider />
                    <MDBox display="flex" justifyContent="center" alignItems="center" width="100%" mt={1}>
                        <MDBox component="img"
                            src={image}
                            sx={{
                                width: "100%",
                                minHeight: "100px",
                                maxHeight: "400px",
                                objectFit: "contain",
                                display: "block",
                                margin: "0 auto"
                            }}
                            onError={handleImgError}
                        />
                    </MDBox>
                    <MDBox display="flex" justifyContent="flex-end" alignItems="center" width="100%" mt={1}>
                        <MDButton variant="outlined" color="dark" size="small" my={1} onClick={() => NiceModal.hide(DialogBox)}>
                            {t("close")}
                        </MDButton>
                    </MDBox>
                </MDBox>)
        });
    }

    return (
        <Box sx={{ 
            display: 'flex', 
            alignItems: 'center',
            width: '100%',
            maxWidth: '100%',
            overflow: 'hidden'
        }}>
            <MDBox 
                display="flex" 
                alignItems="center" 
                pr={{ xs: 1, md: 2 }} 
                sx={{ 
                    cursor: !!image ? "pointer" : "default",
                    width: '100%',
                    maxWidth: '100%',
                    overflow: 'hidden'
                }} 
                onClick={!!image ? showDialog : null}
            >
                {image && (
                    <Avatar 
                        src={image} 
                        alt={name} 
                        sx={{ 
                            width: { xs: 32, md: 40 }, 
                            height: { xs: 32, md: 40 }, 
                            borderRadius: '8px', 
                            mr: { xs: 1, md: 2 },
                            flexShrink: 0
                        }} 
                    />
                )}
                <Box sx={{ 
                    minWidth: 0, // Allow text to shrink
                    overflow: 'hidden'
                }}>
                    <Tooltip title={name} placement="top" arrow>
                        <MDTypography 
                            color={!!image ? "info" : "dark"} 
                            variant="button" 
                            fontWeight="regular" 
                            display="block"
                            sx={{
                                fontSize: { xs: '0.75rem', md: '0.875rem' },
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                                maxWidth: '100%'
                            }}
                        >
                            {/* Mobile: shorter text, Desktop: longer text */}
                            <Box component="span" sx={{ display: { xs: 'inline', md: 'none' } }}>
                                {name.length > 15 ? name.substring(0, 15) + "..." : name}
                            </Box>
                            <Box component="span" sx={{ display: { xs: 'none', md: 'inline' } }}>
                                {name.length > 30 ? name.substring(0, 30) + "..." : name}
                            </Box>
                        </MDTypography>
                    </Tooltip>
                    {breakdown == 'product_name' && (
                        <Typography 
                            variant="caption" 
                            color="text.secondary"
                            sx={{
                                fontSize: { xs: '0.6rem', md: '0.75rem' },
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                                maxWidth: '100%',
                                display: 'block'
                            }}
                        >
                            ID: {sku}
                        </Typography>
                    )}
                </Box>
            </MDBox>
        </Box>
    );
};

export default ProductCell;