import React from 'react';
import { Dialog, Divider, Icon } from '@mui/material';
import MDBox from '@/components/MDBox';
import MDTypography from '@/components/MDTypography';
import MDButton from '@/components/MDButton';
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import { useTranslation } from 'react-i18next';
import { moneyFormatter } from '@/util';
import { getProductImageUrl } from '@/layouts/product/performance/data';

// Helper function for image error handling
const handleImageError = (e) => {
  e.target.onerror = null;
  e.target.src = "https://cdn.shopify.com/s/files/1/0533/2089/files/placeholder-images-image_large.png?format=webp&v=1530129081";
};

// ProductDialog component for displaying product details in a modal
const ProductDialog = NiceModal.create(({ product, avgROAS, avgAdSpend, currency }) => {
  const modal = useModal();
  const { t } = useTranslation();
  const money = (v) => moneyFormatter(v, currency);
  
  // Availability flags for UI display
  const isRoasAvailable = typeof product.roas === 'number' && !isNaN(product.roas);
  const isAdSpendAvailable = typeof product.adSpend === 'number' && !isNaN(product.adSpend);
  const isRevenueAvailable = typeof product.netRevenue === 'number' && !isNaN(product.netRevenue);
  
  // Numeric values for calculations (fallback to 0 for math operations)
  const roas = isRoasAvailable ? product.roas : 0;
  const adSpend = isAdSpendAvailable ? product.adSpend : 0;
  const revenue = isRevenueAvailable ? product.netRevenue : 0;
  
  // Diff calculations using numeric values
  const roasDiff = avgROAS ? ((roas - avgROAS) / avgROAS) * 100 : 0;
  const adSpendDiff = avgAdSpend ? ((adSpend - avgAdSpend) / avgAdSpend) * 100 : 0;
  
  return (
    <Dialog
      open={modal.visible}
      onClose={modal.hide}
      maxWidth="xs"
      fullWidth
      TransitionProps={{
        onExited: () => modal.remove(),
      }}
    >
      <MDBox sx={{ borderRadius: 2, overflow: "hidden" }}>
        {/* Header */}
        <MDBox p={2.5} pb={1.5}>
          <MDTypography variant="h6" color="dark" fontWeight="medium" mb={0.5}>
            {product.product_name}
          </MDTypography>
          <MDTypography variant="caption" color="text.secondary" fontSize="0.75rem">
            ID: {product.product_id || product.sku}
          </MDTypography>
        </MDBox>
        
        <Divider />
        
        {/* Product Image */}
        <MDBox p={2.5} display="flex" justifyContent="center">
          <MDBox 
            component="img"
            src={getProductImageUrl(product)}
            sx={{
              width: "100%",
              maxWidth: "280px",
              maxHeight: "250px",
              objectFit: "contain",
              borderRadius: 1.5,
              boxShadow: '0 2px 8px rgba(0,0,0,0.08)'
            }}
            onError={handleImageError}
          />
        </MDBox>
        
        <Divider />
        
        {/* Metrics Section */}
        <MDBox p={2.5}>
          <MDBox display="flex" flexDirection="column" gap={1.5}>
            {/* ROAS Metric */}
            <MDBox display="flex" alignItems="center" justifyContent="space-between">
              <MDBox display="flex" alignItems="center" gap={1}>
                <Icon sx={{ color: '#22c55e', fontSize: 16 }}>trending_up</Icon>
                <MDTypography variant="button" color="text.primary" fontSize="0.8rem" fontWeight="regular">
                  {t("performance.roas-label")}
                </MDTypography>
              </MDBox>
              <MDBox display="flex" alignItems="center" gap={1}>
                <MDTypography variant="button" color="dark" fontSize="0.8rem" fontWeight="medium">
                  {isRoasAvailable ? `${roas.toFixed(2)}x` : '-'}
                </MDTypography>
                {isRoasAvailable && (
                  <MDTypography 
                    variant="button" 
                    fontSize="0.7rem" 
                    fontWeight="regular"
                    sx={{ 
                      color: roasDiff >= 0 ? '#22c55e' : '#ef4444',
                      backgroundColor: roasDiff >= 0 ? '#dcfce7' : '#fee2e2',
                      px: 1,
                      py: 0.25,
                      borderRadius: 1,
                      textTransform: 'none'
                    }}
                  >
                    {roasDiff >= 0 ? '+' : ''}{roasDiff.toFixed(1)}% {t("performance.vs-avg")}
                  </MDTypography>
                )}
              </MDBox>
            </MDBox>
            
            {/* Ad Spend Metric */}
            <MDBox display="flex" alignItems="center" justifyContent="space-between">
              <MDBox display="flex" alignItems="center" gap={1}>
                <Icon sx={{ color: '#eab308', fontSize: 16 }}>payments</Icon>
                <MDTypography variant="button" color="text.primary" fontSize="0.8rem" fontWeight="regular">
                  {t("performance.ad-spend-label")}
                </MDTypography>
              </MDBox>
              <MDBox display="flex" alignItems="center" gap={1}>
                <MDTypography variant="button" color="dark" fontSize="0.8rem" fontWeight="medium">
                  {isAdSpendAvailable ? money(adSpend) : '-'}
                </MDTypography>
                {isAdSpendAvailable && (
                  <MDTypography 
                    variant="button" 
                    fontSize="0.7rem" 
                    fontWeight="regular"
                    sx={{ 
                      color: adSpendDiff >= 0 ? '#eab308' : '#ef4444',
                      backgroundColor: adSpendDiff >= 0 ? '#fef9c3' : '#fee2e2',
                      px: 1,
                      py: 0.25,
                      borderRadius: 1,
                      textTransform: 'none'
                    }}
                  >
                    {adSpendDiff >= 0 ? '+' : ''}{adSpendDiff.toFixed(1)}% {t("performance.vs-avg")}
                  </MDTypography>
                )}
              </MDBox>
            </MDBox>
            
            {/* Revenue Metric */}
            <MDBox display="flex" alignItems="center" justifyContent="space-between">
              <MDBox display="flex" alignItems="center" gap={1}>
                <Icon sx={{ color: '#0ea5e9', fontSize: 16 }}>attach_money</Icon>
                <MDTypography variant="button" color="text.primary" fontSize="0.8rem" fontWeight="regular">
                  {t("performance.revenue-label")}
                </MDTypography>
              </MDBox>
              <MDTypography variant="button" color="dark" fontSize="0.8rem" fontWeight="medium">
                {isRevenueAvailable ? money(revenue) : '-'}
              </MDTypography>
            </MDBox>
          </MDBox>
        </MDBox>
        
        {/* Footer */}
        <Divider />
        <MDBox p={2.5} display="flex" justifyContent="center">
          <MDButton 
            variant="outlined" 
            color="dark" 
            size="medium" 
            onClick={() => NiceModal.hide(ProductDialog)}
            sx={{ fontSize: '0.85rem', px: 4, minWidth: 120 }}
          >
            {t("performance.close")}
          </MDButton>
        </MDBox>
      </MDBox>
    </Dialog>
  );
});

export default ProductDialog;
