import React from 'react';
import { Skeleton } from '@mui/material';
import MDBox from '@/components/MDBox';
import MDTypography from '@/components/MDTypography';
import NiceModal from '@ebay/nice-modal-react';
import { useTranslation } from 'react-i18next';
import { moneyFormatter } from '@/util';
import { getPillColor, getProductImageUrl } from '@/layouts/product/performance/data';
import ProductDialog from '@/layouts/product/performance/components/ProductDialog';

// Helper function for image error handling
const handleImageError = (e) => {
  e.target.onerror = null;
  e.target.src = "https://cdn.shopify.com/s/files/1/0533/2089/files/placeholder-images-image_large.png?format=webp&v=1530129081";
};

// Common style objects
const commonStyles = {
  productImage: {
    width: 40,
    height: 40,
    borderRadius: 1.5,
    objectFit: 'cover',
    mr: 2,
    flexShrink: 0,
    border: '1px solid rgba(0, 0, 0, 0.08)',
    backgroundColor: '#f8f9fa'
  }
};

// Reusable InsightCard component
const InsightCard = ({ titleKey, products, loading, getTranslation, avgROAS, avgAdSpend, currency }) => {
  const { t } = useTranslation();

  // avgROAS and avgAdSpend are now passed from parent props for accuracy
  const showDialog = (product) => {
    NiceModal.show(ProductDialog, {
      product,
      avgROAS,
      avgAdSpend,
      currency
    });
  };

  return (
    <MDBox>
      <MDBox>
        {loading ? (
          Array.from({ length: 3 }).map((_, index) => (
            <MDBox key={index} display="flex" alignItems="center" mb={2} py={1}>
              <Skeleton
                variant="rectangular"
                width={48}
                height={48}
                sx={{ borderRadius: 1.5, mr: 2, flexShrink: 0 }}
              />
              <MDBox sx={{ flexGrow: 1 }}>
                <Skeleton variant="text" width="85%" height={20} sx={{ mb: 0.5 }} />
                <Skeleton variant="text" width="45%" height={16} />
              </MDBox>
            </MDBox>
          ))
        ) : products.length > 0 ? (
          products.slice(0, 3).map((product, index) => {
            const roas = product.roas !== undefined && product.roas !== null && !isNaN(product.roas)
              ? `${parseFloat(product.roas).toFixed(2)}x`
              : '-';
            const spendValue = product.adSpend !== undefined && product.adSpend !== null && !isNaN(product.adSpend)
              ? product.adSpend
              : (product.blended_catalog_spend !== undefined && !isNaN(product.blended_catalog_spend))
                ? product.blended_catalog_spend
                : null;
            const displayCurrency = product.currency;
            const spend = spendValue !== null ? moneyFormatter(spendValue, displayCurrency) : '-';

            // Color logic based on card type
            const pillColor = getPillColor(titleKey);

            return (
              <MDBox key={`${titleKey}-${index}-${product.product_id || product.product_name || index}`} display="flex" alignItems="center" mb={index < 2 ? 2.5 : 0}>
                {/* Product Image */}
                <MDBox
                  component="img"
                  src={getProductImageUrl(product)}
                  alt={product.product_name}
                  sx={commonStyles.productImage}
                  onError={handleImageError}
                />
                {/* Product Details */}
                <MDBox sx={{ minWidth: 0, flexGrow: 1, overflow: 'hidden' }}>
                  <MDTypography
                    variant="body2"
                    fontWeight="600"
                    onClick={() => showDialog(product)}
                    sx={{
                      fontSize: '0.76rem',
                      lineHeight: 1.3,
                      color: '#334155',
                      fontWeight: 500,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      cursor: 'pointer',
                      display: 'block',
                      width: '100%',
                      '&:hover': {
                        textDecoration: 'underline'
                      }
                    }}
                    title={product.product_name} // Show full name on hover
                  >
                    {product.product_name}
                  </MDTypography>
                  <MDBox 
                    display="flex" 
                    alignItems="center" 
                    gap={{ xs: 1.5, sm: 2, md: 3 }} 
                    mt={0.5}
                    sx={{ 
                      flexWrap: { xs: 'wrap', sm: 'nowrap' },
                      minWidth: 0
                    }}
                  >
                    {/* Ad Spend (or Spend) */}
                    <MDBox 
                      display="flex" 
                      alignItems="center" 
                      gap={0.5}
                      sx={{ 
                        minWidth: 0,
                        flex: { xs: '1 1 auto', sm: '0 0 auto' }
                      }}
                    >
                      <MDTypography
                        variant="body2"
                        sx={{ 
                          color: '#64748b', 
                          fontWeight: 400, 
                          fontSize: '0.73rem', 
                          mr: 0.5,
                          whiteSpace: 'nowrap',
                          flexShrink: 0
                        }}
                      >
                        {titleKey.endsWith('top-budget-wasting-products') ? t("performance.ad-spend-label") : t("performance.spend-label")}
                      </MDTypography>
                      <MDTypography
                        variant="body2"
                        sx={{ 
                          color: pillColor, 
                          fontWeight: 600, 
                          fontSize: '0.76rem',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          minWidth: 0
                        }}
                        title={spend} // Show full value on hover
                      >
                        {spend}
                      </MDTypography>
                    </MDBox>
                    {/* ROAS */}
                    <MDBox 
                      display="flex" 
                      alignItems="center" 
                      gap={0.5}
                      sx={{ 
                        minWidth: 0,
                        flex: { xs: '1 1 auto', sm: '0 0 auto' }
                      }}
                    >
                      <MDTypography
                        variant="body2"
                        sx={{ 
                          color: '#64748b', 
                          fontWeight: 400, 
                          fontSize: '0.73rem', 
                          mr: 0.5,
                          whiteSpace: 'nowrap',
                          flexShrink: 0
                        }}
                      >
                        {t("performance.roas-label")}
                      </MDTypography>
                      <MDTypography
                        variant="body2"
                        sx={{ 
                          color: pillColor, 
                          fontWeight: 600, 
                          fontSize: '0.76rem',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          minWidth: 0
                        }}
                        title={roas} // Show full value on hover
                      >
                        {roas}
                      </MDTypography>
                    </MDBox>
                  </MDBox>
                </MDBox>
              </MDBox>
            );
          })
        ) : (
          <MDBox
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            py={4}
            sx={{ opacity: 0.6 }}
          >
            <MDTypography variant="body2" color="text.secondary" textAlign="center" sx={{ fontSize: '0.875rem' }}>
              {getTranslation("no-data-available", "No data available")}
            </MDTypography>
          </MDBox>
        )}
      </MDBox>
    </MDBox>
  );
};

export default InsightCard;
