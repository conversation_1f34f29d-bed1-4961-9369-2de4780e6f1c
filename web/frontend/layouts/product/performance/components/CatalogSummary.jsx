import React from 'react';
import { Grid, Skeleton } from '@mui/material';
import MDTypography from '@/components/MDTypography';
import DefaultStatisticsCard from '@/examples/Cards/StatisticsCards/DefaultStatisticsCard';
import MetricTitle from '@/layouts/dashboards/metrics/MetricTitle';
import { useTranslation } from 'react-i18next';

// CatalogSummary component using DefaultStatisticsCard
const CatalogSummary = ({ summaryData, avgRoas, loading, hasMetaConnection, hasGoogleAdsConnection, currency }) => {
  const { t } = useTranslation();
  
  if (!summaryData || summaryData.length === 0) {
    return null;
  }

  const summaryItems = [
    {
      title: (
        <MetricTitle
          metric="blended:catalog-spend"
          integrationStates={{
            hasMetaConnection,
            hasGoogleAdsConnection
          }}
        />
      ),
      value: summaryData[0]?.blended_catalog_spend || summaryData[0]?.meta_ads_spend || summaryData[0]?.gads_spend || '—'
    },
    {
      title: (
        <MetricTitle
          metric="blended:catalog-roas"
          integrationStates={{
            hasMetaConnection,
            hasGoogleAdsConnection
          }}
        />
      ),
      value: typeof avgRoas === 'number' && !isNaN(avgRoas) ? `${avgRoas.toFixed(2)}x` : '—'
    },
    {
      title: (
        <MetricTitle
          metric="shopify:total_price"
          integrationStates={{
            hasMetaConnection,
            hasGoogleAdsConnection
          }}
        />
      ),
      value: summaryData[0]?.net_revenue || '—'
    }
  ];

  return (
    <Grid container spacing={1.2} p={1.6} pb={2.4}>
      {summaryItems.map((item, index) => {
        let countElement = (
          <MDTypography variant="h4" fontWeight="regular" color="dark" my={0.4}>
            {item.value}
          </MDTypography>
        );

        if (loading) {
          countElement = <Skeleton variant="text" width={100} />;
        }

        return (
          <Grid item xs={12} md={6} lg={4} key={index}>
            <DefaultStatisticsCard
              isEmbedded={true}
              title={item.title}
              count={countElement}
              diffLabel={null}
              vsLabel={null}
            />
          </Grid>
        );
      })}
    </Grid>
  );
};

export default CatalogSummary;
