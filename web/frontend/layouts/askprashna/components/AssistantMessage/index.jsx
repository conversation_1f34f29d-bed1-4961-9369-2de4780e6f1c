import { useState, useEffect, useRef, useMemo, useCallback, useReducer } from "react";
import { useNavigate } from "react-router-dom";
import {
  useMessage,
  MessagePrimitive,
  ActionBarPrimitive
} from "@assistant-ui/react";
import { globalStoreRef } from "@/layouts/askprashna/langgraph-runtime/globalStore.jsx";
import AssistantAvatar from "@/layouts/askprashna/components/AssistantAvatar/index.jsx";
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import { useTranslation } from "react-i18next";
import { AnimatedMarkdown } from "flowtoken";
import 'flowtoken/dist/styles.css';

import { useCancellableAxios } from "@/context/index.jsx";
import { useMaterialUIController, tracker } from "@/context/index.jsx";
import Report from "@/components/Report";
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import Dialog from '@mui/material/Dialog';
import MDButton from "@/components/MDButton";
import Divider from "@mui/material/Divider";
import { highlightSQL, sqlHighlightStyles } from "@/utils/sqlSyntaxHighlighter.jsx";
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CheckIcon from '@mui/icons-material/Check';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import { CSVLink } from "react-csv";
import MDProgress from "@/components/MDProgress";
import Icon from "@mui/material/Icon";
import { ExportIcon, CodeIcon, InfoOutlinedIcon } from "@/examples/Icons";
import { progressUtils } from "@/layouts/askprashna/langgraph-runtime/progressUtils.jsx";
import PsychologyAltIcon from '@mui/icons-material/PsychologyAlt';

// Common animated markdown styling for reuse
const animatedMarkdownStyles = {
  '& .animated-markdown': {
    // Typography improvements - smaller, more subtle
    '& h1, & h2, & h3, & h4, & h5, & h6': {
      fontWeight: 500,
      lineHeight: 1.4,
      marginTop: '1em',
      marginBottom: '0.5em',
      color: 'text.primary',
    },
    '& h1': { fontSize: '1.25rem' },
    '& h2': { 
      fontSize: '1.125rem', 
      borderBottom: '1px solid rgba(0, 0, 0, 0.08)', 
      paddingBottom: '0.25rem',
      marginBottom: '0.75em'
    },
    '& h3': { fontSize: '1rem' },
    '& h4': { fontSize: '0.95rem' },
    '& h5': { fontSize: '0.9rem' },
    '& h6': { fontSize: '0.85rem' },
    
    // Paragraph styling - smaller, consistent with UI
    '& p': {
      lineHeight: 1.6,
      marginBottom: '0.75em',
      color: 'text.secondary',
      fontSize: '0.875rem',
      fontWeight: 400,
    },
    
    // List styling - more compact
    '& ul, & ol': {
      paddingLeft: '1.25rem',
      marginBottom: '0.75em',
      fontSize: '0.875rem',
      '& li': {
        marginBottom: '0.25em',
        lineHeight: 1.5,
        color: 'text.secondary',
      },
    },
    '& ul': {
      listStyle: 'none',
      '& li': {
        position: 'relative',
        '&::before': {
          content: '"•"',
          color: 'primary.main',
          fontWeight: 500,
          position: 'absolute',
          left: '-0.75rem',
          fontSize: '0.75rem',
        },
      },
    },
    '& ol': {
      listStyleType: 'decimal',
      paddingLeft: '1.5rem',
      '& li': {
        paddingLeft: '0.25rem',
      },
    },
    
    // Code styling - more subtle
    '& code': {
      backgroundColor: 'rgba(0, 0, 0, 0.04)',
      color: 'error.main',
      padding: '0.125rem 0.25rem',
      borderRadius: '3px',
      fontSize: '0.8rem',
      fontFamily: 'SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace',
      border: '1px solid rgba(0, 0, 0, 0.08)',
    },
    '& pre': {
      backgroundColor: 'rgba(0, 0, 0, 0.02)',
      border: '1px solid rgba(0, 0, 0, 0.08)',
      borderRadius: '6px',
      padding: '0.75rem',
      marginBottom: '0.75em',
      overflow: 'auto',
      fontSize: '0.8rem',
      '& code': {
        backgroundColor: 'transparent',
        color: 'text.primary',
        padding: 0,
        border: 'none',
        fontSize: 'inherit',
      },
    },
    
    // Blockquote styling - more subtle
    '& blockquote': {
      borderLeft: '3px solid',
      borderLeftColor: 'primary.main',
      backgroundColor: 'rgba(0, 0, 0, 0.02)',
      padding: '0.75rem 1rem',
      margin: '0.75em 0',
      borderRadius: '0 4px 4px 0',
      fontStyle: 'italic',
      color: 'text.secondary',
      fontSize: '0.875rem',
    },
    
    // Table styling - more subtle
    '& table': {
      width: '100%',
      borderCollapse: 'collapse',
      marginBottom: '0.75em',
      border: '1px solid rgba(0, 0, 0, 0.08)',
      borderRadius: '6px',
      overflow: 'hidden',
      fontSize: '0.875rem',
      '& th, & td': {
        padding: '0.5rem 0.75rem',
        textAlign: 'left',
        borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
      },
      '& th': {
        backgroundColor: 'rgba(0, 0, 0, 0.02)',
        fontWeight: 500,
        color: 'text.primary',
        fontSize: '0.8rem',
      },
      '& td': {
        color: 'text.secondary',
      },
      '& tr:nth-of-type(even) td': {
        backgroundColor: 'rgba(0, 0, 0, 0.01)',
      },
    },
    
    // Link styling - consistent with theme
    '& a': {
      color: 'primary.main',
      textDecoration: 'none',
      borderBottom: '1px solid transparent',
      transition: 'all 0.2s ease',
      fontSize: 'inherit',
      '&:hover': {
        color: 'primary.dark',
        borderBottomColor: 'primary.dark',
      },
    },
    
    // Image styling - more subtle
    '& img': {
      maxWidth: '100%',
      height: 'auto',
      borderRadius: '6px',
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
      marginBottom: '0.75em',
    },
    
    // Horizontal rule styling
    '& hr': {
      border: 'none',
      height: '1px',
      backgroundColor: 'rgba(0, 0, 0, 0.08)',
      margin: '1.5em 0',
      borderRadius: '0.5px',
    },
    
    // Strong and emphasis - more subtle
    '& strong': {
      fontWeight: 500,
      color: 'text.primary',
    },
    '& em': {
      fontStyle: 'italic',
      color: 'text.secondary',
    },
  }
};

export const FinalAnswerText = (props) => {
  return (
    <MDBox 
      my={1.5} 
      key="final-answer"
      sx={{
        padding: '12px 16px',
        ...animatedMarkdownStyles
      }}
    >
      <div className="animated-markdown">
        <AnimatedMarkdown
          key="final-answer"
          animation="fadeIn"
          animationDuration="0.6s"
          animationTimingFunction="ease-out"
          content={props.text}
          sep="word"
        />
      </div>
    </MDBox>
  );
};

const Steps = ({ steps, final_answer, progress_value, showProgressBar, progressBarOpacity, threadId, activeThreadId }) => {
  const collapseStateRef = useRef({});
  const [, forceRender] = useReducer(x => x + 1, 0);
  let threadIdToUse = threadId ? threadId : activeThreadId;

  const getCollapseState = () => {
    if (!threadIdToUse) return { isCollapsed: true, userInteracted: false };
    return collapseStateRef.current[threadIdToUse] || { isCollapsed: true, userInteracted: false };
  };

  const setCollapseState = (newState) => {
    if (!threadIdToUse) return;
    collapseStateRef.current[threadIdToUse] = { ...getCollapseState(), ...newState };
    forceRender();
  };

  const { isCollapsed, userInteracted } = getCollapseState();
  const isAnalysisRunning = showProgressBar && progress_value > 0 && progress_value < 100;

    useEffect(() => {
      // Only apply automatic behavior if user hasn't manually interacted
      if (!userInteracted) {
        if (progress_value === 100) {
          const timer = setTimeout(() => {
            setCollapseState({ isCollapsed: true });
          }, 4000); // Same timing as progress bar fade
          return () => clearTimeout(timer);
        } else if (progress_value > 0 && progress_value < 100) {
          setCollapseState({ isCollapsed: false });
        }
      }
    }, [progress_value, userInteracted, threadId]);

    if (final_answer && steps.length <= 1) {
      return null;
    }

    return (
      <MDBox 
        mt={1}
        sx={{
          borderRadius: '12px',
          backgroundColor: 'rgba(0, 0, 0, 0.02)',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.03)',
          overflow: 'hidden',
          width: { xs: '100%', sm: '75%', md: '50%' },
          maxWidth: '100%'
        }}
      >
        <MDBox>
          <MDBox
            display="flex"
            flexDirection="column"
            sx={{ 
              cursor: 'pointer',
              transition: 'background-color 0.2s ease',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.03)'
              }
            }}
            onClick={() => {
              setCollapseState({ isCollapsed: !isCollapsed, userInteracted: true });
            }}
          >
            <MDBox display="flex" alignItems="center" p={1.5} pb={showProgressBar ? 1 : 1.5}>
              <AssistantAvatar size="xs"/>
              <MDBox sx={{ ml: 1.5, display: 'flex', alignItems: 'center', flex: 1 }}>
                <MDTypography
                  variant="button"
                  fontWeight="medium"
                  color="text"
                  sx={{
                    fontSize: '0.875rem',
                    ...(isAnalysisRunning && {
                      background: 'linear-gradient(90deg, currentColor, rgba(189, 197, 207, 0.8), currentColor)',
                      backgroundSize: '200% 100%',
                      backgroundClip: 'text',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      animation: 'shimmer 2s ease-in-out infinite',
                      '@keyframes shimmer': {
                        '0%': {
                          backgroundPosition: '200% 0'
                        },
                        '100%': {
                          backgroundPosition: '-200% 0'
                        }
                      }
                    })
                  }}
                >
                  {isAnalysisRunning ? "Analysing" : "AI Analysis"}
                </MDTypography>
                <MDBox sx={{ ml: 'auto', display: 'flex', alignItems: 'center', gap: 1 }}>
                  {showProgressBar && (
                    <MDTypography
                      variant="button"
                      color="dark"
                      sx={{ fontSize: '0.8rem', fontWeight: 'medium' }}
                    >
                      {Math.round(progress_value)}%
                    </MDTypography>
                  )}
                  {isCollapsed ? (
                    <ExpandMoreIcon sx={{ color: 'text.secondary', fontSize: '22px' }} />
                  ) : (
                    <ExpandLessIcon sx={{ color: 'text.secondary', fontSize: '22px' }} />
                  )}
                </MDBox>
              </MDBox>
            </MDBox>
            
            {showProgressBar && (
              <MDBox px={1.5} pb={1.5}>
                <MDProgress
                  variant="gradient"
                  value={progress_value}
                  color="info"
                  sx={{
                    height: '3px',
                    borderRadius: '1.5px',
                    opacity: progressBarOpacity,
                    transition: 'opacity 0.5s ease-out',
                    backgroundColor: 'rgba(0, 0, 0, 0.05)',
                    '& .MuiLinearProgress-bar': {
                      borderRadius: '1.5px'
                    }
                  }}
                />
              </MDBox>
            )}
          </MDBox>
        </MDBox>
        
        <MDBox 
          className={`transition-all duration-300 ease-in-out ${isCollapsed ? 'max-h-0 overflow-hidden' : 'max-h-[1000px]'}`}
          sx={{
            px: 2,
            pb: isCollapsed ? 0 : 2
          }}
        >
          {(steps ?? []).map((step, index) => (
            <MDBox 
              key={index}
              sx={{
                display: 'flex',
                alignItems: 'flex-start',
                gap: 1.5,
                pt: index === 0 ? 2 : 1.5,
                pb: index === steps.length - 1 ? 1 : 0,
                position: 'relative'
              }}
            >
              {index < steps.length - 1 && (
                <MDBox
                  sx={{
                    position: 'absolute',
                    left: '8px',
                    top: '32px',
                    width: '2px',
                    height: 'calc(100% - 20px)',
                    backgroundColor: 'rgba(26, 115, 232, 0.2)',
                    borderRadius: '1px'
                  }}
                />
              )}
              
              <MDBox
                sx={{
                  width: '16px',
                  height: '16px',
                  borderRadius: '50%',
                  backgroundColor: 'rgba(26, 115, 232, 0.1)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexShrink: 0,
                  mt: '4px',
                  border: '2px solid rgba(26, 115, 232, 0.3)',
                  position: 'relative',
                  zIndex: 1,
                  ...(index === steps.length - 1 && showProgressBar && {
                    animation: 'pulse 2s ease-in-out infinite',
                    '@keyframes pulse': {
                      '0%': {
                        boxShadow: '0 0 0 0 rgba(26, 115, 232, 0.4)'
                      },
                      '70%': {
                        boxShadow: '0 0 0 8px rgba(26, 115, 232, 0)'
                      },
                      '100%': {
                        boxShadow: '0 0 0 0 rgba(26, 115, 232, 0)'
                      }
                    }
                  })
                }}
              >
                <MDBox
                  sx={{
                    width: '6px',
                    height: '6px',
                    borderRadius: '50%',
                    backgroundColor: 'rgba(26, 115, 232, 0.8)',
                    ...(index === steps.length - 1 && showProgressBar && {
                      animation: 'blink 1.5s ease-in-out infinite',
                      '@keyframes blink': {
                        '0%, 50%': {
                          opacity: 1
                        },
                        '25%, 75%': {
                          opacity: 0.3
                        }
                      }
                    })
                  }}
                />
              </MDBox>
              
              {/* Step content */}
              <MDBox sx={{ flex: 1, minWidth: 0 }}>
                <MDTypography
                  variant="caption"
                  fontWeight="regular"
                  color="dark"
                  sx={{
                    fontSize: '0.8rem',
                    lineHeight: '1.5',
                    display: 'block',
                    wordBreak: 'break-word'
                  }}
                >
                  <AnimatedMarkdown
                    key={step}
                    content={step}
                    animation="dropIn"
                    animationDuration="0.5s"
                    animationTimingFunction="ease-in-out"
                  />
                </MDTypography>
              </MDBox>
            </MDBox>
          ))}
        </MDBox>
      </MDBox>
    );
};

const PrashnaReport = (props) => {
  const { query_result, plotly_image, plotly_json_path, report_title, query_tags, query_explanation, source, displayDebugInfo } = props;
  const { t } = useTranslation();
  const [plotlyJson, setPlotlyJson] = useState(null);
  const [csvData, setCsvData] = useState(null);
  const [csvFilename, setCsvFilename] = useState("data.csv");
  const csvLinkRef = useRef(null);
  const axiosInstance = useCancellableAxios();
  const [controller] = useMaterialUIController();
  const { selectedShop, loginConfig } = controller;

  // Define all hooks before any conditional returns
  const columns = query_result?.columns ?? [];
  const data = query_result?.data ?? [];

  // Prepare tableData object once when query_result changes
  const tableData = useMemo(() => {
    if (columns && columns.length > 0 && data && data.length > 0) {
      return { columns, data };
    }
    return null;
  }, [columns, data]);

  const displayQueryExplanation = useCallback(() => {
    NiceModal.show(QueryExplanation, {
      query_explanation
    });
  }, [query_explanation]);

  // Prepare export data whenever tableData changes
  useEffect(() => {
    if (!tableData || !tableData.columns || !tableData.data || tableData.columns.length === 0) {
      setCsvData(null);
      return;
    }

    const formatColumnName = (columnName) => {
      if (typeof columnName !== 'string') return columnName;
      return columnName
        .split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    };

    // Prepare data for CSV export
    const preparedCsvData = [
      // Header row with formatted column names
      tableData.columns.map(column => formatColumnName(column)),
      // Data rows
      ...tableData.data.map(row => tableData.columns.map(column => row[column]))
    ];

    setCsvData(preparedCsvData);
    setCsvFilename(`${report_title || "data"}.csv`);
  }, [tableData, report_title]);


  const fetchPlotlyJson = useCallback(async (pathToFetch) => {
    let data = { plotly_json_path: pathToFetch };
    if (selectedShop) {
      data.selectedShop = selectedShop;
    }

    setPlotlyJson(null);
    try {
      const response = await axiosInstance.post(`/api/agent/plotly`, data);

      if (response.data && response.data.data && response.data.layout) {
        setPlotlyJson(response.data);
      } else {
        console.error("Invalid plotly json:", response.data);
      }
    } catch (error) {
      console.error("Error parsing plotly_json_path:", error);
    }
  }, [axiosInstance, selectedShop]);

  useEffect(() => {
    // Only fetch if there's a path to fetch
    if (!plotly_json_path) {
      setPlotlyJson(null);
      return;
    };

    fetchPlotlyJson(plotly_json_path);
  }, [plotly_json_path, selectedShop]);

  // Create views array for the report
  const views = [];
  // Add table view if table data exists
  if (tableData) {
    views.push({
      id: "table",
      viewType: "table",
      title: t("data"),
      dataReference: "$config.data.tableData",
      variables: ["$config.data.tableData"]
    });
  }
  
  // Add chart view if plotly data exists
  if (plotlyJson) {
    views.push({
      id: "chart",
      viewType: "chart",
      title: t("chart"),
      dataReference: "$config.data.plotlyJSON",
      variables: ["$config.data.plotlyJSON"],
    });
  }

  // Handle export button click
  const handleExport = useCallback(() => {
    // Trigger the CSV download by clicking the hidden CSVLink element
    if (csvLinkRef.current) {
      csvLinkRef.current.link.click();
    }
  }, [csvLinkRef]);


  // If no views available, don't render anything
  if (views.length === 0) {
    return null;
  }

  // Prepare action buttons for the report footer
  const actions = [];

  // Add "How did we arrive" button if explanation exists
  if (query_explanation) {
    actions.push({
      label: "How was this calculated?",
      tooltip: "View the reasoning and steps that led to this result",
      icon: <InfoOutlinedIcon />,
      onClick: displayQueryExplanation
    });
  }
  
  // Add export button if we have data
  if (tableData && csvData) {
    actions.push({
      label: "Export",
      tooltip: "Download the data as CSV",
      icon: <ExportIcon />,
      onClick: handleExport
    });
  }

  // Add debug info button if we have SQL or other debug data
  if (loginConfig.admin) {
    actions.push({
      label: "",
      tooltip: "",
      icon: <CodeIcon />,
      onClick: displayDebugInfo
    });
  }

  const report_json_config = {
    title: report_title,
    tags: query_tags,
    source: source,
    data: {
      plotlyJSON: plotlyJson,
      tableData: tableData,
    },
    views: views,
    actions: actions
  };

  return (
    <MDBox sx={{ 
      width: '100%', 
      maxWidth: '100%',
      position: 'relative'
    }}>
      {/* Hidden CSVLink component for export functionality */}
      {csvData && (
        <div style={{ display: "none" }}>
          <CSVLink
            ref={csvLinkRef}
            data={csvData}
            filename={csvFilename}
            style={{ display: "none" }}
          />
        </div>
      )}
      <Report config={report_json_config} />
    </MDBox>
  );
};

const QueryExplanation = NiceModal.create(({query_explanation}) => {
  const modal = useModal();
  
  // Parse the explanation to extract steps if it's a list
  const parseSteps = (content) => {
    if (!content) return [];
    
    // Split by lines and look for bullet points or numbered lists
    const lines = content.split('\n').filter(line => line.trim());
    const steps = [];
    
    lines.forEach(line => {
      const trimmed = line.trim();
      // Check if it's a bullet point (• or *) or numbered list
      if (trimmed.match(/^[•*]\s+/) || trimmed.match(/^\d+\.\s+/)) {
        steps.push(trimmed.replace(/^[•*]\s+/, '').replace(/^\d+\.\s+/, ''));
      } else if (steps.length === 0) {
        // If no steps found yet, treat as regular content
        steps.push(trimmed);
      }
    });
    
    return steps.length > 1 ? steps : [];
  };
  
  const steps = parseSteps(query_explanation);
  const isStepFormat = steps.length > 1;
  
  return (
    <Dialog 
      open={modal.visible} 
      onClose={modal.hide} 
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: { xs: "0", sm: "12px" },
          boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
          minWidth: { xs: "100%", sm: "500px" },
          maxWidth: { xs: "100%", sm: "800px" },
          maxHeight: { xs: "100vh", sm: "80vh" },
          height: { xs: "100vh", sm: "auto" },
          margin: { xs: 0, sm: "32px" },
          overflow: "hidden"
        }
      }}
    >
      <MDBox p={0} sx={{ overflow: "hidden", display: "flex", flexDirection: "column" }}>
                 {/* Header */}
         <MDBox 
           p={{ xs: 2, sm: 3 }} 
           sx={{ 
             borderBottom: "1px solid rgba(0,0,0,0.08)",
             background: "linear-gradient(135deg, rgba(26, 115, 232, 0.02) 0%, rgba(26, 115, 232, 0.08) 100%)",
             display: "flex",
             alignItems: "center",
             justifyContent: "space-between"
           }}
         >
          <MDBox display="flex" alignItems="center" gap={1.5}>
            <MDBox
              sx={{
                width: "32px",
                height: "32px",
                borderRadius: "50%",
                backgroundColor: "rgba(26, 115, 232, 0.1)",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                border: "2px solid rgba(26, 115, 232, 0.2)"
              }}
            >
              <Icon sx={{ color: "rgba(26, 115, 232, 0.8)", fontSize: "16px" }}>info_outlined</Icon>
            </MDBox>
                         <MDTypography variant="h6" fontWeight="bold" color="text.primary" sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}>
               How was this calculated?
             </MDTypography>
          </MDBox>
          <MDBox 
            onClick={modal.hide}
            sx={{ 
              cursor: "pointer", 
              borderRadius: "50%", 
              width: "32px",
              height: "32px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              transition: "all 0.2s ease",
              "&:hover": { 
                backgroundColor: "rgba(0,0,0,0.04)",
                transform: "scale(1.05)"
              }
            }}
          >
            <Icon sx={{ fontSize: "18px", color: "text.secondary" }}>close</Icon>
          </MDBox>
        </MDBox>
        
                 {/* Content */}
         <MDBox 
           sx={{ 
             p: { xs: 2, sm: 3 }, 
             overflowY: "auto",
             flex: 1,
             maxHeight: { xs: "calc(100vh - 120px)", sm: "60vh" }
           }}
         >
          {isStepFormat ? (
            // Render as structured steps
            <MDBox>
              {steps.map((step, index) => (
                <MDBox 
                  key={index}
                  sx={{
                    display: 'flex',
                    alignItems: 'flex-start',
                    gap: 2,
                    mb: index === steps.length - 1 ? 0 : 2.5,
                    p: 2,
                    borderRadius: '10px',
                    backgroundColor: index % 2 === 0 ? 'rgba(26, 115, 232, 0.02)' : 'rgba(0, 0, 0, 0.02)',
                    border: '1px solid rgba(0, 0, 0, 0.04)',
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      backgroundColor: index % 2 === 0 ? 'rgba(26, 115, 232, 0.04)' : 'rgba(0, 0, 0, 0.04)',
                      transform: 'translateY(-1px)',
                      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)'
                    }
                  }}
                >
                  {/* Step number */}
                  <MDBox
                    sx={{
                      width: '28px',
                      height: '28px',
                      borderRadius: '50%',
                      backgroundColor: 'rgba(26, 115, 232, 0.1)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'rgba(26, 115, 232, 0.8)',
                      fontWeight: 'bold',
                      fontSize: '0.875rem',
                      flexShrink: 0,
                      mt: 0.25,
                      border: '2px solid rgba(26, 115, 232, 0.2)',
                      transition: 'all 0.2s ease'
                    }}
                  >
                    {index + 1}
                  </MDBox>
                  
                  {/* Step content */}
                  <MDBox sx={{ flex: 1, minWidth: 0 }}>
                    <MDTypography
                      variant="body2"
                      fontWeight="regular"
                      color="text.primary"
                      sx={{
                        fontSize: '0.875rem',
                        lineHeight: 1.6,
                        display: 'block',
                        wordBreak: 'break-word'
                      }}
                    >
                      <AnimatedMarkdown
                        key={`step-${index}`}
                        content={step}
                        animation="fadeIn"
                        animationDuration="0.4s"
                        animationTimingFunction="ease-out"
                        animationDelay={`${index * 0.1}s`}
                      />
                    </MDTypography>
                  </MDBox>
                </MDBox>
              ))}
            </MDBox>
          ) : (
                         // Render as regular markdown content
             <MDBox 
               sx={{
                 ...animatedMarkdownStyles
               }}
             >
              <div className="animated-markdown">
                <AnimatedMarkdown
                  key="query-explanation"
                  animation="fadeIn"
                  animationDuration="0.6s"
                  animationTimingFunction="ease-out"
                  content={query_explanation}
                  sep="word"
                />
              </div>
            </MDBox>
          )}
        </MDBox>
      </MDBox>
    </Dialog>
  );
});

const DebugInfo = NiceModal.create(({expanded_user_query, final_sql, query_data_processed, reasoning, source}) => {
  const modal = useModal();
  const [copied, setCopied] = useState(false);
  const [expandedStep, setExpandedStep] = useState(null);
  
  const handleCopy = () => {
    if (final_sql) {
      navigator.clipboard.writeText(final_sql)
        .then(() => {
          setCopied(true);
          setTimeout(() => setCopied(false), 2000);
        })
        .catch(err => {
          console.error('Failed to copy SQL: ', err);
        });
    }
  };

  const toggleStep = (index) => {
    if (expandedStep === index) {
      setExpandedStep(null);
    } else {
      setExpandedStep(index);
    }
  };

  return (
    <Dialog
        open={modal.visible}
        onClose={modal.hide}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: { xs: "0", sm: "10px" },
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
            minWidth: { xs: "100%", sm: "600px" },
            maxWidth: { xs: "100%", sm: "90vw" },
            maxHeight: { xs: "100vh", sm: "90vh" },
            height: { xs: "100vh", sm: "auto" },
            margin: { xs: 0, sm: "32px" },
            overflow: "hidden"
          }
        }}
        TransitionProps={{
            onExited: () => modal.remove(),
        }}
    >
      <MDBox 
        p={0} 
        sx={{
          overflow: "hidden",
          display: "flex",
          flexDirection: "column",
          height: "100%"
        }}
      >
        {/* Header */}
        <MDBox 
          p={2} 
          sx={{ 
            borderBottom: "1px solid rgba(0,0,0,0.08)",
            background: "linear-gradient(to right, #f9f9f9, #ffffff)",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between"
          }}
        >
          <MDTypography variant="h6" fontWeight="bold">
            Debug Information
          </MDTypography>
          <MDBox 
            onClick={modal.hide}
            sx={{ 
              cursor: "pointer", 
              borderRadius: "50%", 
              width: "30px",
              height: "30px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              "&:hover": { backgroundColor: "rgba(0,0,0,0.04)" }
            }}
          >
            ✕
          </MDBox>
        </MDBox>
        
        {/* Content - scrollable */}
        <MDBox 
          sx={{ 
            p: 3, 
            overflowY: "auto",
            flex: 1
          }}
        >
          {/* MiB processed info with pill styling */}
          <MDBox 
            mb={3}
            sx={{ 
              display: "inline-flex",
              background: "rgba(0,0,0,0.03)",
              borderRadius: "20px",
              padding: "6px 12px"
            }}
          >
            <MDTypography variant="button" fontWeight="medium" color="text">
              <strong>MiB processed:</strong> {query_data_processed != null ? Math.round(query_data_processed) : 'N/A'}
            </MDTypography>
          </MDBox>

          {/* Reasoning Steps Section */}
          {reasoning && reasoning.length > 0 && (
            <MDBox mb={3}>
              <MDTypography variant="button" fontWeight="bold" color="dark" mb={1} display="block">
                Reasoning Steps:
              </MDTypography>
              <MDBox 
                sx={{
                  backgroundColor: "#f8f9fa",
                  borderRadius: "8px",
                  border: "1px solid rgba(0,0,0,0.08)",
                  overflow: "hidden"
                }}
              >
                {reasoning.map((step, index) => (
                  <MDBox 
                    key={index}
                    sx={{
                      borderBottom: index < reasoning.length - 1 ? "1px solid rgba(0,0,0,0.08)" : "none",
                      position: "relative"
                    }}
                  >
                    {/* Step header with role and toggle button */}
                    <MDBox
                      sx={{
                        padding: "12px 16px",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        cursor: "pointer",
                        transition: "background-color 0.2s ease",
                        "&:hover": {
                          backgroundColor: "rgba(0,0,0,0.02)"
                        },
                        backgroundColor: expandedStep === index ? "rgba(26, 115, 232, 0.08)" : "transparent"
                      }}
                      onClick={() => toggleStep(index)}
                    >
                      <MDBox display="flex" alignItems="center" gap={2}>
                        <MDBox
                          sx={{
                            width: "28px",
                            height: "28px",
                            borderRadius: "50%",
                            backgroundColor: "rgba(26, 115, 232, 0.2)",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            color: "white",
                            fontWeight: "bold",
                            fontSize: "14px",
                            transition: "all 0.2s ease"
                          }}
                        >
                          {index + 1}
                        </MDBox>
                        <MDTypography 
                          variant="button" 
                          fontWeight="medium"
                          color={expandedStep === index ? "primary" : "text"}
                        >
                          {step.role}
                        </MDTypography>
                      </MDBox>
                      <MDBox
                        sx={{
                          width: "24px",
                          height: "24px",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          transform: expandedStep === index ? "rotate(180deg)" : "rotate(0deg)",
                          transition: "transform 0.2s ease"
                        }}
                      >
                        <KeyboardArrowDownIcon fontSize="small" />
                      </MDBox>
                    </MDBox>
                    
                    {/* Expanded content */}
                    {expandedStep === index && (
                      <MDBox
                        sx={{
                          padding: "0 16px 16px 60px",
                          fontSize: "0.875rem",
                          lineHeight: 1.5,
                          animation: "fadeIn 0.3s ease",
                          "@keyframes fadeIn": {
                            "0%": {
                              opacity: 0,
                              transform: "translateY(-10px)"
                            },
                            "100%": {
                              opacity: 1,
                              transform: "translateY(0)"
                            }
                          }
                        }}
                      >
                        {step.content}
                      </MDBox>
                    )}
                  </MDBox>
                ))}
              </MDBox>
            </MDBox>
          )}
          
          {/* Expanded Query Section */}
          <MDBox mb={3}>
            <MDTypography variant="button" fontWeight="bold" color="dark" mb={1} display="block">
              Expanded User Query:
            </MDTypography>
            <MDBox 
              sx={{
                backgroundColor: "#f8f9fa",
                padding: "16px",
                borderRadius: "8px",
                border: "1px solid rgba(0,0,0,0.08)",
                fontSize: "0.875rem",
                fontFamily: "'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace",
                whiteSpace: "pre-wrap",
                wordBreak: "break-word",
                lineHeight: 1.5
              }}
            >
              {expanded_user_query}
            </MDBox>
          </MDBox>
          
          {/* SQL Section */}
          <MDBox>
            <MDBox 
              display="flex" 
              justifyContent="space-between" 
              alignItems="center" 
              mb={1}
            >
              <MDTypography variant="button" fontWeight="bold" color="dark" display="block">
                Final SQL:
              </MDTypography>
              <Tooltip title={copied ? "Copied!" : "Copy SQL"}>
                <IconButton 
                  onClick={handleCopy} 
                  size="small"
                  sx={{
                    backgroundColor: copied ? "rgba(76, 175, 80, 0.08)" : "rgba(0, 0, 0, 0.04)",
                    borderRadius: "4px",
                    padding: "4px 8px",
                    '&:hover': {
                      backgroundColor: copied ? "rgba(76, 175, 80, 0.12)" : "rgba(0, 0, 0, 0.08)"
                    }
                  }}
                >
                  {copied ? (
                    <CheckIcon fontSize="small" sx={{ color: "#4caf50" }} />
                  ) : (
                    <ContentCopyIcon fontSize="small" />
                  )}
                </IconButton>
              </Tooltip>
            </MDBox>
            <MDBox 
              sx={{
                ...sqlHighlightStyles.container,
                ...sqlHighlightStyles.sxStyles,
                position: "relative",
                '&:hover': {
                  border: "1px solid rgba(25, 118, 210, 0.3)"
                }
              }}
              dangerouslySetInnerHTML={{ __html: highlightSQL(final_sql) }}
            />
          </MDBox>
        </MDBox>
      </MDBox>
    </Dialog>
  );
});


const ErrorBlock = ({error}) => {
  return (
    <MDBox sx={{ width: '100%' }}>
      <MDBox
        sx={{
          display: 'flex',
          alignItems: 'flex-start',
          gap: 2,
          p: 2,
          borderRadius: '12px',
          backgroundColor: 'rgba(244, 67, 54, 0.04)',
          border: '1px solid rgba(244, 67, 54, 0.12)',
          boxShadow: '0 2px 8px rgba(244, 67, 54, 0.08)',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        
        <MDBox sx={{ flex: 1, minWidth: 0 }}>
          <MDBox display="flex" alignItems="center" gap={0.5} mb={1}>
            <Icon fontSize="medium" sx={{ color: 'rgba(244, 67, 54, 0.8)' }}>error_outline</Icon>
            <MDTypography variant="button" fontWeight="medium" sx={{ color: 'rgba(244, 67, 54, 0.9)' }}>
              Analysis Failed
            </MDTypography>
          </MDBox>
          
          <MDTypography 
            variant="button" 
            color="text.secondary" 
            sx={{ 
              lineHeight: 1.6,
              mb: 2,
              fontSize: '0.875rem'
            }}
          >
            I encountered an issue while processing your request. This could be due to a temporary server problem or an issue with your query.
          </MDTypography>
          
          <MDBox display="flex" gap={1} flexWrap="wrap" mt={1}>
            {/* <MDButton
              variant="outlined"
              color="error"
              size="small"
              sx={{
                px: 2,
                py: 0.5,
                borderColor: 'rgba(244, 67, 54, 0.3)',
                color: 'error.main'
              }}
              onClick={() => window.location.reload()}
            >
              Try Again
            </MDButton> */}
            
            <MDButton
              variant="outlined"
              size="small"
              color="secondary"
              onClick={tracker.intercom.show}
            >
              Report Issue
            </MDButton>
          </MDBox>

          {error && typeof error === 'string' && (
            <MDBox
              sx={{
                mt: 2,
                p: 1.5,
                backgroundColor: 'rgba(0, 0, 0, 0.03)',
                borderRadius: '8px',
                border: '1px solid rgba(0, 0, 0, 0.06)'
              }}
            >
              <MDTypography
                variant="caption"
                color="text.secondary"
                sx={{
                  fontFamily: 'monospace',
                  fontSize: '0.75rem',
                  lineHeight: 1.4,
                  wordBreak: 'break-word'
                }}
              >
                {error}
              </MDTypography>
            </MDBox>
          )}
        </MDBox>
      </MDBox>
    </MDBox>
  )
}

const AbortedMessage = () => {
  return (
    <MDBox sx={{ width: '100%' }}>
      <MDBox
        sx={{
          display: 'flex',
          alignItems: 'flex-start',
          gap: 2,
          p: 2,
          borderRadius: '12px',
          backgroundColor: 'rgba(255, 152, 0, 0.04)',
          border: '1px solid rgba(255, 152, 0, 0.12)',
          boxShadow: '0 2px 8px rgba(255, 152, 0, 0.08)',
          position: 'relative',
          overflow: 'hidden'
        }}
      >

        <MDBox sx={{ flex: 1, minWidth: 0 }}>
          <MDBox display="flex" alignItems="center" gap={0.5} mb={1}>
            <Icon fontSize="medium" sx={{ color: 'rgba(255, 152, 0, 0.8)' }}>cancel_outlined</Icon>
            <MDTypography variant="button" fontWeight="medium" sx={{ color: 'rgba(255, 152, 0, 0.9)' }}>
              Run Aborted
            </MDTypography>
          </MDBox>

          <MDTypography
            variant="button"
            color="text.secondary"
            sx={{
              lineHeight: 1.6,
              fontSize: '0.875rem'
            }}
          >
            The analysis was cancelled. You can ask a new question to continue.
          </MDTypography>
        </MDBox>
      </MDBox>
    </MDBox>
  )
}

export default function AssistantMessage() {
  const { steps, current_step, plotly_image, plotly_json_path, final_answer, query_result, error, report_title, query_tags, query_explanation, expanded_user_query, final_sql, query_data_processed, reasoning, source, progress_update, thread_id, aborted, interrupt_message, interrupt_actions } = useMessage(m => {
    return m.metadata?.custom ?? {};
  });

  useEffect(() => {
    // just re-render the component when interrupt message changes
  }, [interrupt_message]);

  const activeThreadId = globalStoreRef.activeThreadId; // the one that user is currently seeing on UI
  const progressValue = progressUtils.getProgress(thread_id)?.progressValue ?? 0;

  const progressStateRef = useRef({});
  const [uiSteps, setUiSteps] = useState([]);

  const getProgressState = () => {
    if (!thread_id) return { showProgressBar: false, progressBarOpacity: 1 };
    return progressStateRef.current[thread_id] || { showProgressBar: false, progressBarOpacity: 1 };
  };

  const setProgressState = (newState) => {
    if (!thread_id) return;
    progressStateRef.current[thread_id] = { ...getProgressState(), ...newState };
    forceRender();
  };

  const [, forceRender] = useReducer(x => x + 1, 0);

  const { showProgressBar, progressBarOpacity } = getProgressState();

  useEffect(() => {
    if (!thread_id) {
      return;
    }

    // update progress from progress_update event
    if (progress_update && progress_update.identifier) {
      const currentProgress = progressUtils.getProgress(thread_id);

      if (currentProgress.nodeProgressMap[progress_update.identifier]) return;

      const newNodeProgressMap = {
        ...currentProgress.nodeProgressMap,
        [progress_update.identifier]: progress_update
      };

      let newProgressValue = currentProgress.progressValue;
      if (progress_update.type === "overwrite") {
        newProgressValue = progress_update.percentage;
      }
      else {
        newProgressValue = Math.min(currentProgress.progressValue + progress_update.percentage, 100);
      }

      progressUtils.setProgress(thread_id, {
        progressValue: newProgressValue,
        nodeProgressMap: newNodeProgressMap
      });
    }

    // update progress percentage bar from progress state
    const threadProgress = progressUtils.getProgress(thread_id);
    if (threadProgress && (threadProgress.progressValue > 0 || (Object.keys(threadProgress.nodeProgressMap ?? {}).length > 0))) {
      if (threadProgress.progressValue > 0 && threadProgress.progressValue < 100) {
        setProgressState({ showProgressBar: true, progressBarOpacity: 1 });
      } else if (threadProgress.progressValue === 100 && current_step) {
        setProgressState({ showProgressBar: true, progressBarOpacity: 0 });
        const timer = setTimeout(() => {
          setProgressState({ showProgressBar: false });
          progressUtils.clearProgress(thread_id);
          globalStoreRef.aiStepsByThread[thread_id] = [];
        }, 4000);
        return () => clearTimeout(timer);
      }
    } else {
      setProgressState({ showProgressBar: false, progressBarOpacity: 1 });
    }
  }, [thread_id, progress_update]);

  useEffect(() => {
    if (!current_step) {
      return;
    }

    let globalStepsForThread = globalStoreRef.aiStepsByThread[thread_id] ?? [];

    if (current_step && !globalStepsForThread.includes(current_step)) {
      if (!globalStoreRef.aiStepsByThread[thread_id]) {
        globalStoreRef.aiStepsByThread[thread_id] = [];
      }
      globalStoreRef.aiStepsByThread[thread_id].push(current_step);
    }

    setUiSteps(globalStoreRef.aiStepsByThread[thread_id] ?? []);
  }, [current_step]);

  useEffect(() => {
    if (!!steps && steps.length > 0 && !current_step) { // only process steps for completed run, that is, no current_step
      const deduped = Array.from(new Set(steps)); // deduping to handle retries in nodes
      setUiSteps(deduped);
    }
  }, [steps]);

  const displayDebugInfo = () => {
    NiceModal.show(DebugInfo, {
      expanded_user_query,
      final_sql,
      query_data_processed,
      reasoning,
      source
    });
  }

  // If this is an aborted message, render the aborted message component
  if (aborted) {
    return (
      <MessagePrimitive.Root className="relative mb-6 flex w-full max-w-4xl gap-4 font-size-12">
        <MDBox sx={{ width: '100%' }}>
          <AbortedMessage />
        </MDBox>
      </MessagePrimitive.Root>
    );
  }

  // If this is an interrupt message, render the interrupt UI
  if (interrupt_message) {
    return (
      <MessagePrimitive.Root className="relative mb-6 flex flex-col w-full max-w-4xl gap-4 font-size-12">
        <MDBox sx={{ width: '100%' }}>
          <InterruptMessage interrupt_message={interrupt_message} interrupt_actions={interrupt_actions} />
        </MDBox>
      </MessagePrimitive.Root>
    );
  }

  return (
    <MessagePrimitive.Root className="relative mb-6 flex w-full max-w-4xl gap-4 font-size-12">
      <MDBox sx={{ width: '100%' }}>
        {(uiSteps ?? []).length > 0 && <Steps steps={uiSteps} final_answer={final_answer} progress_value={progressValue} showProgressBar={showProgressBar} progressBarOpacity={progressBarOpacity} threadId={thread_id} activeThreadId={activeThreadId} />}

        {!!error && <MDBox mt={2}><ErrorBlock error={error} /></MDBox>}

        {(!!query_result || !!plotly_image || !!plotly_json_path) &&
          <MDBox mt={2}>
            <PrashnaReport
              source={source}
              query_result={query_result}
              plotly_image={plotly_image}
              report_title={report_title}
              plotly_json_path={plotly_json_path}
              query_tags={query_tags}
              query_explanation={query_explanation}
              displayDebugInfo={displayDebugInfo}
            />
            </MDBox>}

        {final_answer && <MDBox mt={2}>
            <MDTypography variant="body2" fontWeight="regular">
              <MessagePrimitive.Content components={{Text: FinalAnswerText}}/>
            </MDTypography>
          </MDBox>}

          {/* Action Bar positioned within the message content */}
        <MDBox
            sx={{ 
              mt: 2,
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              opacity: 0.7,
              transition: 'opacity 0.2s ease',
              '&:hover': {
                opacity: 1
              }
            }}
          >
            <ActionBarPrimitive.Root>
              <ActionBarPrimitive.Reload />
              <ActionBarPrimitive.Copy />
              <ActionBarPrimitive.Speak />
            </ActionBarPrimitive.Root>
        </MDBox>
        </MDBox>
      </MessagePrimitive.Root>
    );
  };

const InterruptMessage = ({ interrupt_message, interrupt_actions }) => {
  const navigate = useNavigate();

  const handleActionClick = (action) => {
    if (action.identifier === "connect_integration") {
      navigate('/integrations');
    } else if (action.identifier === "connect_with_support") {
      window.open('https://help.datadrew.io/', '_blank');
    }
  };

  return (
    <MDBox
      display="flex"
      alignItems="center"
      gap={2}
      p={2.5}
      maxWidth="75%"
      bgColor="#f5f5f5"
      sx={{
        borderRadius: 2,
        border: 1,
        borderColor: '#f5f5f5',
      }}
    >
      <MDBox
        display="flex"
        alignItems="center"
        justifyContent="center"
        sx={{
          width: 24,
          height: 24,
          color: '#1A73E8',
          '& svg': {
            width: '100%',
            height: '100%',
          }
        }}
      >
        <PsychologyAltIcon />
      </MDBox>

      <MDBox flex={1}>
        <MDTypography
          variant="body3"
          color="text.secondary"
          fontWeight="regular"
        >
          {interrupt_message}
        </MDTypography>

        {/* Action buttons positioned below the text */}
        {interrupt_actions && interrupt_actions.length > 0 && (
          <MDBox
            display="flex"
            gap={1}
            mt={1.5}
          >
            {interrupt_actions.map((action, index) => (
              <MDButton
                variant="gradient"
                color="info"
                fullWidth
                fontWeight="bold"
                onClick={() => handleActionClick(action)}
                sx={{
                  "&:disabled": {
                    color: "grey",
                  },
                  width: '50%',
                  marginTop: '20px',
                }}
              >
                {action.title}
              </MDButton>
            ))}
          </MDBox>
        )}
      </MDBox>
    </MDBox>
  );
};
