import { MessagePrimitive, ActionBarPrimitive } from "@assistant-ui/react";
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import Avatar from 'boring-avatars';
import MDAvatar from "@/components/MDAvatar";
import MDTooltip from "@/components/MDTooltip";
import { useMaterialUIController } from "@/context";
import {Avatar as AntAvatar} from "antd";

function UserAvatar() {
    const [controller] = useMaterialUIController();
    const {loginConfig, shopConfig} = controller;

    const displayName = shopConfig.shop.name;

    return (
        <MDTooltip title={displayName} placement="top">
            <MDAvatar 
                size="sm" 
                shadow="sm"
                bgColor="info"
                sx={{
                    outline: "none",
                    fontWeight: "550"
                }}
            >
                {displayName.length > 0 ? displayName[0] : "x"}
            </MDAvatar>
        </MDTooltip>
    );
}

export default function UserMessage() {
  return (
    <MDBox className="relative mb-8 flex w-full justify-end gap-3" display="flex">
      <MessagePrimitive.Root display="flex">
        <MDBox 
          className="space-y-4" 
          display="flex" 
          verticalAlign="middle"
          sx={{
            p: 1.5,
            backgroundColor: '#f5f5f5',
            borderRadius: '12px 2px 12px 12px',
            position: 'relative',
            maxWidth: '500px'
          }}
        >
          <MDBox className="space-y-2">
            <MDTypography variant="button" fontWeight="regular" sx={{lineHeight: "30px !important"}}>
              <MessagePrimitive.Content />
            </MDTypography>
          </MDBox>
        </MDBox>
      </MessagePrimitive.Root>
      <UserAvatar />
    </MDBox>
  );
}
