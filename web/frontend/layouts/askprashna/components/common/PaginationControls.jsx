import React from 'react';
import Stack from "@mui/material/Stack";
import IconButton from "@mui/material/IconButton";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import MDTypography from "@/components/MDTypography";
import MDBox from "@/components/MDBox";

/**
 * Reusable pagination controls component
 * 
 * @param {Object} props
 * @param {number} props.currentPage - Current page index (0-based)
 * @param {number} props.totalPages - Total number of pages
 * @param {Function} props.onPrevPage - Function to call when previous page button is clicked
 * @param {Function} props.onNextPage - Function to call when next page button is clicked
 * @param {Object} props.containerSx - Optional additional styling for the container
 * @param {Object} props.buttonSx - Optional additional styling for the buttons
 * @param {Object} props.typographySx - Optional additional styling for the page indicator text
 * @param {boolean} props.showDisabled - Whether to show disabled state on buttons (default: true)
 * @param {string} props.size - Size of the buttons ('small', 'medium', 'large') (default: 'small')
 */
const PaginationControls = ({
  currentPage,
  totalPages,
  onPrevPage,
  onNextPage,
  containerSx = {},
  buttonSx = {},
  typographySx = {},
  showDisabled = true,
  size = 'small'
}) => {
  // Default button styles based on size
  const buttonSizes = {
    small: { width: "28px", height: "28px" },
    medium: { width: "32px", height: "32px" },
    large: { width: "36px", height: "36px" }
  };
  
  // Default icon sizes based on component size
  const iconSizes = {
    small: { fontSize: '0.8rem' },
    medium: { fontSize: 'small' },
    large: { fontSize: 'medium' }
  };

  const defaultButtonStyle = {
    border: "1px solid #e0e0e0",
    borderRadius: "50%",
    ...buttonSizes[size]
  };

  return (
    <MDBox 
      sx={{ 
        display: "flex", 
        justifyContent: "center", 
        alignItems: "center",
        ...containerSx
      }}
    >
      <Stack 
        direction="row" 
        spacing={1}
        sx={{
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <IconButton 
          onClick={onPrevPage} 
          size={size}
          sx={{
            ...defaultButtonStyle,
            ...buttonSx
          }}
          disabled={showDisabled && currentPage === 0}
        >
          <ArrowBackIcon sx={iconSizes[size]} />
        </IconButton>
        
        <MDTypography 
          variant="caption" 
          sx={{ 
            mx: 1,
            ...typographySx
          }}
        >
          {currentPage + 1} / {totalPages}
        </MDTypography>
        
        <IconButton 
          onClick={onNextPage} 
          size={size}
          sx={{
            ...defaultButtonStyle,
            ...buttonSx
          }}
          disabled={showDisabled && currentPage === totalPages - 1}
        >
          <ArrowForwardIcon sx={iconSizes[size]} />
        </IconButton>
      </Stack>
    </MDBox>
  );
};

export default PaginationControls; 
