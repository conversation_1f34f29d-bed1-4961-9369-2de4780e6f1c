import { useTranslation } from "react-i18next";
import { useState, useMemo } from "react";
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDButton from "@/components/MDButton";
import Grid from "@mui/material/Grid";
import { ThreadPrimitive } from "@assistant-ui/react";
import Card from "@mui/material/Card";
import Box from "@mui/material/Box";
import Chip from "@mui/material/Chip";
import SearchIcon from "@mui/icons-material/Search";
import InputAdornment from "@mui/material/InputAdornment";
import MDInput from "@/components/MDInput";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import { SUGGESTION_CATEGORIES, getPrompts } from "@/layouts/askprashna/components/constants/prompts.jsx";
import PaginationControls from "@/layouts/askprashna/components/common/PaginationControls.jsx";

const Discover = ({ onClose }) => {
  const [selectedCategory, setSelectedCategory] = useState("ALL");
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(0);
  const { t } = useTranslation();
  const promptsPerPage = 6;

  // Use the shared prompts from the constants file
  const allPrompts = getPrompts();
  
  // Get unique categories for the dropdown
  const categories = useMemo(() => [
    { id: "ALL", label: "All Categories" },
    ...Object.entries(SUGGESTION_CATEGORIES).map(([key, value]) => ({
      id: key,
      label: value.label
    }))
  ], []);

  const handleCategoryChange = (event) => {
    setSelectedCategory(event.target.value);
    setCurrentPage(0);
  };

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
    setCurrentPage(0);
  };

  // Filter prompts based on selected category and search term
  const filteredPrompts = useMemo(() => {
    return allPrompts.filter(prompt => 
      (selectedCategory === "ALL" || prompt.category.label === SUGGESTION_CATEGORIES[selectedCategory]?.label) &&
      prompt.text.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [allPrompts, selectedCategory, searchTerm]);

  const totalPages = Math.ceil(filteredPrompts.length / promptsPerPage);
  const currentPrompts = filteredPrompts.slice(
    currentPage * promptsPerPage,
    (currentPage + 1) * promptsPerPage
  );

  const handleNextPage = () => {
    setCurrentPage((prev) => (prev + 1) % totalPages);
  };

  const handlePrevPage = () => {
    setCurrentPage((prev) => (prev - 1 + totalPages) % totalPages);
  };

  return (
    <MDBox id="discover" sx={{ height: "100%", display: "flex", flexDirection: "column" }}>
      <MDBox p={3} mb={1} sx={{ borderBottom: "1px solid rgba(0, 0, 0, 0.06)" }}>
        <MDTypography variant="h4" color="secondary" fontWeight="regular" className="card-title-default" mb={3} sx={{ fontSize: "24px !important" }}>
          {t("Discover")}
        </MDTypography>
        
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={8}>
            <MDInput
              placeholder="Search prompts..."
              fullWidth
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon fontSize="small" color="action" />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Box sx={{ position: 'relative' }}>
              <Select
                value={selectedCategory}
                onChange={handleCategoryChange}
                displayEmpty
                variant="outlined"
                fullWidth
                IconComponent={ArrowDropDownIcon}
                sx={{ 
                  '.MuiOutlinedInput-notchedOutline': { 
                    borderColor: 'rgba(0, 0, 0, 0.12)' 
                  },
                  height: '40px',
                  fontSize: '14px',
                  '&.MuiOutlinedInput-root': {
                    borderRadius: '6px'
                  }
                }}
              >
                {categories.map((category) => (
                  <MenuItem key={category.id} value={category.id}>
                    {category.label}
                  </MenuItem>
                ))}
              </Select>
            </Box>
          </Grid>
        </Grid>
      </MDBox>

      <MDBox sx={{ flex: 1, overflowY: "auto", px: 3, pb: 3, pt: 2 }}>
        <Grid container spacing={2}>
          {currentPrompts.map((prompt, index) => (
            <Grid item xs={12} sm={6} md={6} key={index}>
              <ThreadPrimitive.Suggestion
                prompt={prompt.text}
                method="replace"
                autoSend={false}
                asChild
              >
                <Card
                  sx={{
                    height: "130px",
                    display: "flex",
                    flexDirection: "column",
                    p: 2,
                    pb: 4, /* Add more padding at bottom for the badge */
                    cursor: "pointer",
                    transition: "all 0.2s ease-in-out",
                    border: "1px solid rgba(0,0,0,0.08)",
                    boxShadow: "0 2px 5px rgba(0,0,0,0.05)",
                    '&:hover': {
                      transform: "translateY(-3px)",
                      boxShadow: "0 5px 15px rgba(0,0,0,0.1)",
                    },
                    position: "relative",
                    backgroundColor: "white",
                    borderRadius: "10px"
                  }}
                  onClick={() => {
                    if (onClose) onClose();
                  }}
                >
                  <MDTypography 
                    variant="body2" 
                    fontSize="0.875rem"
                    sx={{ 
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      display: "-webkit-box",
                      WebkitLineClamp: 3,
                      WebkitBoxOrient: "vertical",
                    }}
                  >
                    {prompt.text}
                  </MDTypography>
                  
                  <Chip
                    label={prompt.category.label}
                    size="small"
                    sx={{
                      position: "absolute",
                      bottom: "10px",
                      left: "10px",
                      backgroundColor: prompt.category.backgroundColor,
                      color: prompt.category.textColor,
                      fontSize: "0.65rem",
                      height: "22px",
                      fontWeight: "bold"
                    }}
                  />
                </Card>
              </ThreadPrimitive.Suggestion>
            </Grid>
          ))}
        </Grid>
        
        {filteredPrompts.length === 0 && (
          <MDBox sx={{ 
            textAlign: "center", 
            py: 5, 
            display: "flex", 
            flexDirection: "column", 
            alignItems: "center",
            justifyContent: "center",
            height: "200px"
          }}>
            <SearchIcon sx={{ fontSize: '3rem', opacity: 0.2, mb: 2 }} />
            <MDTypography variant="h6" color="secondary" fontWeight="regular">
              No prompts found matching your search
            </MDTypography>
            <MDButton 
              variant="text" 
              color="info" 
              onClick={() => {
                setSearchTerm("");
                setSelectedCategory("ALL");
              }}
              sx={{ mt: 1 }}
            >
              Clear filters
            </MDButton>
          </MDBox>
        )}
      </MDBox>

      {totalPages > 1 && (
        <PaginationControls
          currentPage={currentPage}
          totalPages={totalPages}
          onPrevPage={handlePrevPage}
          onNextPage={handleNextPage}
          containerSx={{ 
            p: 2,
            borderTop: "1px solid rgba(0,0,0,0.08)"
          }}
          size="medium"
        />
      )}
    </MDBox>
  );
};

export default Discover; 
