import { useTranslation } from "react-i18next";
import { useState } from "react";
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDButton from "@/components/MDButton";
import MDInput from "@/components/MDInput";
import Grid from "@mui/material/Grid";
import { ThreadList, ThreadListItemPrimitive } from "@assistant-ui/react";
import { CompactThreadListItem, LoadMoreButton } from "@/layouts/askprashna/components/MyAssistant";
import Card from "@mui/material/Card";
import Skeleton from "@mui/material/Skeleton";
import SearchIcon from "@mui/icons-material/Search";
import InputAdornment from "@mui/material/InputAdornment";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";

// Initialize dayjs plugin
dayjs.extend(relativeTime);

// Enhanced Thread Card Component  
const EnhancedThreadCard = ({ thread, onThreadSelect }) => {
  const formattedTime = thread.updatedAt ? dayjs(thread.updatedAt).fromNow() : "";
  
  const handleClick = () => {
    // This will trigger the thread selection in the parent
    onThreadSelect(thread.threadId);
  };
  
  return (
    <Card
      onClick={handleClick}
      sx={{
        minHeight: "130px",
        display: "flex",
        flexDirection: "column",
        p: 2,
        pb: 2.5,
        cursor: "pointer",
        transition: "all 0.2s ease-in-out",
        border: "1px solid rgba(0,0,0,0.08)",
        boxShadow: "0 2px 5px rgba(0,0,0,0.05)",
        '&:hover': {
          transform: "translateY(-2px)",
          boxShadow: "0 5px 15px rgba(0,0,0,0.1)",
        },
        position: "relative",
        backgroundColor: "white",
        borderRadius: "10px",
        width: "100%"
      }}
    >
      {/* Header with title and timestamp */}
      <MDBox sx={{ display: "flex", flexDirection: "column", mb: 1 }}>
        <MDTypography 
          variant="subtitle2" 
          fontWeight="regular"
          sx={{ 
            overflow: "hidden",
            textOverflow: "ellipsis",
            display: "-webkit-box",
            WebkitLineClamp: 2,
            WebkitBoxOrient: "vertical",
            fontSize: "0.9rem",
            lineHeight: 1.3,
            mb: 0.5,
          }}
        >
          {thread.title}
        </MDTypography>
        <MDTypography 
          variant="caption" 
          color="text.secondary" 
          sx={{ 
            fontSize: "0.75rem",
            opacity: 0.8
          }}
        >
          {formattedTime}
        </MDTypography>
      </MDBox>

      {/* Last message snippet */}
      <MDTypography 
        variant="body2" 
        color="text.secondary"
        sx={{ 
          fontSize: "0.75rem",
          overflow: "hidden",
          textOverflow: "ellipsis",
          display: "-webkit-box",
          WebkitLineClamp: 2,
          WebkitBoxOrient: "vertical",
          opacity: 0.9,
          lineHeight: 1.4,
        }}
      >
        {thread.finalAnswer ? 
          thread.finalAnswer.replace(/[#*_`\n]/g, '').replace(/\s+/g, ' ').substring(0, 120) : 
          ""
        }
      </MDTypography>
    </Card>
  );
};

const ChatHistorySkeleton = () => {
  return (
    <Grid container spacing={2} sx={{ mt: 2 }}>
      {Array.from({ length: 6 }).map((_, index) => (
        <Grid item xs={12} sm={6} md={4} key={index}>
          <Card 
            sx={{ 
              height: 130, 
              p: 2,
              display: 'flex',
              flexDirection: 'column',
              borderRadius: "10px",
              boxShadow: "0 2px 5px rgba(0,0,0,0.05)",
              border: "1px solid rgba(0,0,0,0.08)",
              position: "relative"
            }}
          >
            <MDBox sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Skeleton variant="circular" width={24} height={24} sx={{ mr: 1 }} />
              <Skeleton variant="text" width="70%" height={24} />
            </MDBox>
            <Skeleton variant="text" width="90%" />
            <Skeleton variant="text" width="60%" />
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};

const ChatHistory = ({ 
  threads = [], 
  threadsLoading = false, 
  onClose,
  searchTerm = "", 
  onSearchChange = () => {},
  loadMoreThreads = () => {},
  hasMoreThreads = false,
  loadingMoreThreads = false,
  onThreadSelect = () => {} // Add this prop
}) => {
  const { t } = useTranslation();

  const handleSearchChange = (event) => {
    onSearchChange(event.target.value);
  };

  const handleThreadSelect = (threadId) => {
    onThreadSelect(threadId);
    onClose(); // Close the history view after selection
  };

  return (
    <MDBox id="history" sx={{ height: "100%", display: "flex", flexDirection: "column" }}>
      <MDBox p={3} mb={1} sx={{ borderBottom: "1px solid rgba(0, 0, 0, 0.06)" }}>
        <MDTypography variant="h4" color="secondary" fontWeight="regular" className="card-title-default" mb={3} sx={{ fontSize: "24px !important" }}>
          {t("Recent chats")}
        </MDTypography>
        
        <MDInput
          placeholder="Search chats..."
          fullWidth
          value={searchTerm}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon fontSize="small" color="action" />
              </InputAdornment>
            ),
          }}
        />
      </MDBox>

      <MDBox 
        sx={{ 
          flex: 1, 
          overflowY: "auto", 
          px: 3, 
          pb: 3, 
          pt: 2,
          maxHeight: "calc(100% - 120px)"  // Ensure proper scrolling height
        }}
      >
        {threadsLoading ? (
          <ChatHistorySkeleton />
        ) : (
          <Grid container spacing={2}>
            {/* Show threads if we have them */}
            {threads.length > 0 && (
              threads.map((thread, index) => (
                <Grid item xs={12} sm={6} md={4} key={thread.threadId || index}>
                  <EnhancedThreadCard thread={thread} onThreadSelect={handleThreadSelect} />
                </Grid>
              ))
            )}
            
            {/* Load More Button - Show even during search so user can load more threads that might match */}
            {hasMoreThreads && (
              <Grid item xs={12}>
                <LoadMoreButton
                  loadMoreThreads={loadMoreThreads}
                  loadingMoreThreads={loadingMoreThreads}
                  hasMoreThreads={hasMoreThreads}
                  sx={{ mt: 2, mb: 1 }}
                />
              </Grid>
            )}
            
            {/* Show info when search is active */}
            {searchTerm.trim() !== "" && (
              <Grid item xs={12}>
                <MDBox 
                  sx={{ 
                    textAlign: "center", 
                    mt: 2, 
                    p: 2,
                    backgroundColor: "rgba(0, 0, 0, 0.02)",
                    borderRadius: "8px"
                  }}
                >
                  <MDTypography variant="caption" color="secondary" fontWeight="regular">
                    {threads.length > 0 
                      ? `Showing ${threads.length} chat${threads.length !== 1 ? 's' : ''} matching "${searchTerm}"${hasMoreThreads ? ' • Load more for additional results' : ''}`
                      : `No chats match "${searchTerm}"${hasMoreThreads ? ' • Try loading more chats' : ''}`
                    }
                  </MDTypography>
                </MDBox>
              </Grid>
            )}

            {/* No conversations found message - only show when no threads AND no search AND no more to load */}
            {threads.length === 0 && searchTerm.trim() === "" && !hasMoreThreads && !threadsLoading && (
              <Grid item xs={12}>
                <MDBox 
                  sx={{ 
                    textAlign: "center", 
                    py: 5, 
                    display: "flex", 
                    flexDirection: "column", 
                    alignItems: "center",
                    justifyContent: "center",
                    height: "200px"
                  }}
                >
                  <SearchIcon sx={{ fontSize: '3rem', opacity: 0.2, mb: 2 }} />
                  <MDTypography variant="h6" color="secondary" fontWeight="regular">
                    No chats found
                  </MDTypography>
                </MDBox>
              </Grid>
            )}
          </Grid>
        )}
      </MDBox>
    </MDBox>
  );
};

export default ChatHistory; 