import React, { useRef, useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Thread,
  useThread,
  ThreadWelcome,
  ThreadList,
  Composer,
  AssistantRuntimeProvider,
  ThreadPrimitive,
  ThreadListPrimitive,
  ThreadListItemPrimitive,
  WebSpeechSynthesisAdapter,
  ThreadListItem,
  useComposerRuntime,
} from "@assistant-ui/react";
import "@assistant-ui/react/styles/index.css";
import MDTypography from "@/components/MDTypography";
import MDBox from "@/components/MDBox";
import MDButton from "@/components/MDButton";
import { useLangGraphRuntime } from "@/layouts/askprashna/langgraph-runtime/index.jsx";
import { globalStoreRef } from "@/layouts/askprashna/langgraph-runtime/globalStore.jsx";
import UserMessage from "@/layouts/askprashna/components/UserMessage/index.jsx";
import AssistantMessage from "@/layouts/askprashna/components/AssistantMessage/index.jsx";
import AssistantAvatar from "@/layouts/askprashna/components/AssistantAvatar/index.jsx";
import {
  createThread,
  getThreadState,
  sendMessage,
  getThreads,
  cancelRun,
} from "@/layouts/askprashna/langgraph-api/index.jsx";
import { useTranslation } from "react-i18next";
import Skeleton from "@mui/material/Skeleton";
import Menu from "@mui/material/Menu";
import Grid from "@mui/material/Grid";
import AddIcon from "@mui/icons-material/Add";
import Box from "@mui/material/Box";
import Chip from "@mui/material/Chip";
import useMediaQuery from "@mui/material/useMediaQuery";
import { useCancellableAxios } from "@/context/index.jsx";
import { useMaterialUIController } from "@/context/index.jsx";
import { toast } from 'react-toastify';
import Sidenav from "@/layouts/askprashna/components/Sidenav/index.jsx";
import Discover from "@/layouts/askprashna/components/Discover/index.jsx";
import ChatHistory from "@/layouts/askprashna/components/ChatHistory/index.jsx";
import MDTooltip from "@/components/MDTooltip";
import { Global, css } from '@emotion/react';
import { getPrompts } from "@/layouts/askprashna/components/constants/prompts.jsx";
import PaginationControls from "@/layouts/askprashna/components/common/PaginationControls.jsx";
import { progressUtils } from "@/layouts/askprashna/langgraph-runtime/progressUtils.jsx";

// Global styles component
const GlobalStyles = () => (
  <Global
    styles={css`
      @keyframes pulse {
        0% {
          opacity: 0.3;
          transform: scale(0.8);
        }
        50% {
          opacity: 1;
          transform: scale(1.2);
        }
        100% {
          opacity: 0.3;
          transform: scale(0.8);
        }
      }
    `}
  />
);

const MySuggestions = () => {
  const [currentPage, setCurrentPage] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const [shouldShowSuggestions, setShouldShowSuggestions] = useState(true);

  // Check if we're on mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const suggestionsPerPage = isMobile ? 2 : 4;

  // Use the shared welcome suggestions
  const allSuggestions = getPrompts();

  const totalPages = Math.ceil(allSuggestions.length / suggestionsPerPage);
  const currentSuggestions = allSuggestions.slice(
    currentPage * suggestionsPerPage,
    (currentPage + 1) * suggestionsPerPage
  );

  const handleNextPage = () => {
    setCurrentPage((prev) => (prev + 1) % totalPages);
  };

  const handlePrevPage = () => {
    setCurrentPage((prev) => (prev - 1 + totalPages) % totalPages);
  };

  return (
    <Box
      sx={{
        width: "100%",
        px: 2,
        opacity: shouldShowSuggestions ? 1 : 0,
        transform: shouldShowSuggestions ? 'translateY(0)' : 'translateY(-20px)',
        transition: 'opacity 0.3s ease, transform 0.3s ease',
        pointerEvents: shouldShowSuggestions ? 'auto' : 'none',
        visibility: shouldShowSuggestions ? 'visible' : 'hidden',
      }}
    >
      <Grid container spacing={2} sx={{ mb: 2 }}>
        {currentSuggestions.map((suggestion, index) => (
          <Grid item xs={12} sm={6} key={index}>
            <ThreadPrimitive.Suggestion
              prompt={suggestion.text}
              method="replace"
              autoSend={false}
              asChild
              onClick={() => {
                setShouldShowSuggestions(false);
              }}
            >
              <MDTooltip title={suggestion.text} placement="top">
                <MDButton
                  variant="outlined"
                  color="secondary"
                  fullWidth
                  sx={{
                    height: "100px",
                    minHeight: "100px",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "space-between",
                    alignItems: "flex-start",
                    textAlign: "left",
                    p: 2,
                    pb: 3.5,
                    position: "relative"
                  }}
                >
                  <MDTypography 
                    variant="button" 
                    fontWeight="regular" 
                    sx={{ 
                      width: "100%", 
                      textAlign: "left",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      display: "-webkit-box",
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: "vertical",
                    }}
                  >
                    {suggestion.text}
                  </MDTypography>
                  <Chip
                    label={suggestion.category.label}
                    size="small"
                    sx={{
                      position: "absolute",
                      bottom: "8px",
                      left: "8px",
                      backgroundColor: suggestion.category.backgroundColor,
                      color: suggestion.category.textColor,
                      fontSize: "0.65rem",
                      height: "20px",
                      fontWeight: "bold"
                    }}
                  />
                </MDButton>
              </MDTooltip>
            </ThreadPrimitive.Suggestion>
          </Grid>
        ))}
      </Grid>
      
      {totalPages > 1 && (
        <PaginationControls
          currentPage={currentPage}
          totalPages={totalPages}
          onPrevPage={handlePrevPage}
          onNextPage={handleNextPage}
          containerSx={{ mt: 1, mb: 1 }}
          size="small"
        />
      )}
    </Box>
  );
};

const MyThreadWelcome = () => {
  const { t } = useTranslation();
  return (
    <ThreadWelcome.Root>
      <ThreadWelcome.Center>
        <AssistantAvatar size="sm"/>
        <ThreadWelcome.Message message={t("prashna.welcome-message")} />
      </ThreadWelcome.Center>
      <MySuggestions />
    </ThreadWelcome.Root>
  );
};

const MyComposerAction = () => {
  const composerRuntime = useComposerRuntime();

  const handleCancel = () => {
    composerRuntime.cancel(); // Core cancellation method
  };
  return (
    <>
      <ThreadPrimitive.If running={false}>
        <Composer.Send />
      </ThreadPrimitive.If>
      <ThreadPrimitive.If running>
        <Composer.Cancel onClick={handleCancel} />
      </ThreadPrimitive.If>
    </>
  );
};

// Reusable Load More Button Component
export const LoadMoreButton = ({ 
  loadMoreThreads, 
  loadingMoreThreads, 
  hasMoreThreads, 
  sx = {} 
}) => {
  if (!hasMoreThreads) return null;

  return (
    <MDBox 
      sx={{ 
        display: "flex", 
        justifyContent: "center", 
        mt: 1, 
        mb: 1,
        ...sx
      }}
    >
      <MDBox
        component="button"
        onClick={loadMoreThreads}
        disabled={loadingMoreThreads}
        sx={{
          border: "none",
          background: "none",
          cursor: loadingMoreThreads ? "default" : "pointer",
          padding: "6px 12px",
          borderRadius: "6px",
          transition: "all 0.2s ease",
          fontSize: "0.75rem",
          color: "rgba(0, 0, 0, 0.6)",
          "&:hover": {
            backgroundColor: loadingMoreThreads ? "transparent" : "rgba(0, 0, 0, 0.05)",
          },
          display: "flex",
          alignItems: "center",
          gap: 0.5,
          opacity: loadingMoreThreads ? 0.7 : 1,
        }}
      >
        {loadingMoreThreads ? (
          <>
            <MDBox
              sx={{
                width: 12,
                height: 12,
                border: '2px solid',
                borderColor: 'rgba(0, 0, 0, 0.1)',
                borderTopColor: 'currentColor',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                '@keyframes spin': {
                  '0%': { transform: 'rotate(0deg)' },
                  '100%': { transform: 'rotate(360deg)' }
                }
              }}
            />
            Loading...
          </>
        ) : (
          "Load more"
        )}
      </MDBox>
    </MDBox>
  );
};


const MyComposer = () => {
  const { t } = useTranslation();
  return (
    <MDBox sx={{ backgroundColor: "white !important", borderRadius: "12px", p: 0, width: "100%" }}>
    <Composer.Root>
      <Composer.Input
        placeholder={t("prashna.composer-placeholder")}
        autoFocus
      />
      <MyComposerAction />
    </Composer.Root>
    </MDBox>
  );
};

const ThreadFollowupSuggestions = () => {
  const suggestions = useThread((t) => t.suggestions);
  const [currentPage, setCurrentPage] = useState(0);
  const followupSuggestionsPerPage = 4;

  if (!suggestions || suggestions.length === 0) {
    return null;
  }

  const totalPages = Math.ceil(suggestions.length / followupSuggestionsPerPage);
  const currentSuggestions = suggestions.slice(
    currentPage * followupSuggestionsPerPage,
    (currentPage + 1) * followupSuggestionsPerPage
  );

  const handleNextPage = () => {
    setCurrentPage((prev) => (prev + 1) % totalPages);
  };

  const handlePrevPage = () => {
    setCurrentPage((prev) => (prev - 1 + totalPages) % totalPages);
  };

  return (
    <ThreadPrimitive.If empty={false} running={false}>
      <MDBox className="relative mb-8 flex w-full max-w-4xl gap-4 font-size-12 p-4 mx-auto">
        <MDBox width="100%">
          <MDTypography variant="subtitle2" fontWeight="regular" color="secondary" mb={1.5}>
            Related questions
          </MDTypography>
          
          <MDBox 
            sx={{ 
              display: 'flex',
              flexDirection: 'column',
              width: '100%',
              borderRadius: '8px',
              overflow: 'hidden',
              border: '1px solid rgba(0,0,0,0.1)',
              boxShadow: '0 1px 2px rgba(0,0,0,0.05)'
            }}
          >
            {currentSuggestions.map((suggestion, idx) => (
              <ThreadPrimitive.Suggestion
                key={idx}
                prompt={suggestion.prompt}
                method="replace"
                autoSend={false}
                asChild
              >
                <MDBox
                  sx={{
                    width: '100%',
                    p: 1.8,
                    cursor: 'pointer',
                    backgroundColor: 'white',
                    transition: 'background-color 0.2s',
                    borderBottom: idx < currentSuggestions.length - 1 ? '1px solid rgba(0,0,0,0.06)' : 'none',
                    '&:hover': {
                      backgroundColor: 'rgba(0,0,0,0.02)'
                    },
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  <MDTypography 
                    variant="body2" 
                    fontWeight="regular" 
                    color="dark"
                    sx={{ 
                      width: "100%",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      fontSize: "0.85rem",
                      lineHeight: "1.4",
                      display: "-webkit-box",
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: "vertical",
                    }}
                  >
                    {suggestion.prompt}
                  </MDTypography>
                  <MDBox 
                    sx={{ 
                      ml: 1.5, 
                      color: 'rgba(0, 0, 0, 0.35)',
                      fontSize: '0.9rem',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <span>→</span>
                  </MDBox>
                </MDBox>
              </ThreadPrimitive.Suggestion>
            ))}
          </MDBox>

          {totalPages > 1 && (
            <PaginationControls
              currentPage={currentPage}
              totalPages={totalPages}
              onPrevPage={handlePrevPage}
              onNextPage={handleNextPage}
              containerSx={{ mt: 2 }}
              size="small"
              typographySx={{ fontSize: '0.7rem' }}
              buttonSx={{ width: "24px", height: "24px" }}
            />
          )}
        </MDBox>
      </MDBox>
    </ThreadPrimitive.If>
  );
};

const MyThread = (config) => {
  const { currentThreadLoading } = config;

  if (currentThreadLoading) {
    return (
      <Thread.Root config={config}>
        <Thread.Viewport height="80vh" autoScroll={false}>
          <Grid container spacing={3} sx={{ px: 2, py: 3 }}>
            {/* Welcome message skeleton */}
            <Grid item xs={12} sx={{ mb: 2 }}>
              <MDBox 
                sx={{ 
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexDirection: 'column',
                  mb: 3,
                  mt: 1
                }}
              >
                <Skeleton variant="circular" width={50} height={50} sx={{ mb: 2 }} />
                <Skeleton variant="text" width="40%" height={35} sx={{ mb: 1 }} />
                <Skeleton variant="text" width="60%" height={24} />
              </MDBox>
            </Grid>
            
            {/* User message skeleton */}
            <Grid item xs={12} container justifyContent="flex-end" sx={{ mb: 1 }}>
              <Grid item xs={8} sm={7} md={6}>
                <MDBox
                  sx={{
                    p: 1.5,
                    backgroundColor: '#f0f0f0',
                    borderRadius: '12px 2px 12px 12px',
                    position: 'relative',
                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                  }}
                >
                  <Skeleton variant="text" width="100%" height={24} />
                  <Skeleton variant="text" width="80%" height={24} />
                </MDBox>
              </Grid>
            </Grid>
            
            {/* Assistant message skeleton with thinking animation */}
            <Grid item xs={12} container sx={{ mb: 2 }}>
              <Grid item xs={1} sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'center' }}>
                <Skeleton variant="circular" width={36} height={36} />
              </Grid>
              <Grid item xs={8} sm={7} md={6}>
                <MDBox
                  sx={{
                    p: 1.5,
                    backgroundColor: 'white',
                    borderRadius: '2px 12px 12px 12px',
                    border: '1px solid rgba(0, 0, 0, 0.08)',
                    position: 'relative',
                    boxShadow: '0 1px 2px rgba(0,0,0,0.05)'
                  }}
                >
                  <MDBox sx={{ display: 'flex', gap: 0.7, mb: 1.5, pl: 1 }}>
                    <Box 
                      sx={{ 
                        width: 8, 
                        height: 8, 
                        borderRadius: '50%',
                        backgroundColor: 'rgba(0, 0, 0, 0.5)',
                        animation: 'pulse 1s infinite ease-in-out',
                        animationDelay: '0s'
                      }} 
                    />
                    <Box 
                      sx={{ 
                        width: 8, 
                        height: 8, 
                        borderRadius: '50%',
                        backgroundColor: 'rgba(0, 0, 0, 0.5)',
                        animation: 'pulse 1s infinite ease-in-out',
                        animationDelay: '0.2s'
                      }} 
                    />
                    <Box 
                      sx={{ 
                        width: 8, 
                        height: 8, 
                        borderRadius: '50%',
                        backgroundColor: 'rgba(0, 0, 0, 0.5)',
                        animation: 'pulse 1s infinite ease-in-out',
                        animationDelay: '0.4s'
                      }} 
                    />
                  </MDBox>
                  <Skeleton variant="text" width="90%" height={24} />
                  <Skeleton variant="text" width="100%" height={24} />
                  <Skeleton variant="text" width="75%" height={24} />
                </MDBox>
              </Grid>
            </Grid>
            <Grid item xs={12} sx={{ mt: 2 }}>
              <Skeleton variant="rounded" width="100%" height={50} sx={{ borderRadius: '8px' }} />
            </Grid>
          </Grid>
        </Thread.Viewport>
      </Thread.Root>
    );
  }

  return (
    <Thread.Root config={config} height="80vh" sx={{ maxWidth: "100%", overflow: "hidden" }}>
      <Thread.Viewport height="80vh" autoScroll={false}>
        <MyThreadWelcome />
        <Thread.Messages components={{ 
          AssistantMessage: AssistantMessage,
          UserMessage: UserMessage
        }} />
        <ThreadFollowupSuggestions />
        <Thread.ViewportFooter>
          <Thread.ScrollToBottom />
          <MyComposer />
        </Thread.ViewportFooter>
      </Thread.Viewport>
    </Thread.Root>
  );
};

export const CompactThreadListItem = ({ onSelect }) => {
  return (
    <ThreadListItemPrimitive.Root>
      <ThreadListItemPrimitive.Trigger
        className="w-full"
        asChild
        onClick={onSelect}
      >
        <MDButton
          variant="text"
          color="secondary"
          fullWidth
          sx={{
            justifyContent: "flex-start",
            textAlign: "left",
            minHeight: "50px",
            padding: "12px 16px",
            margin: "0",
            borderRadius: "8px",
            marginBottom: "4px",
            "&:hover": {
              backgroundColor: "rgba(0, 0, 0, 0.05)",
            },
            "&:last-child": {
              marginBottom: 0
            }
          }}
        >
          <MDTypography
            variant="body2"
            fontWeight="regular"
            sx={{
              display: "-webkit-box",
              WebkitBoxOrient: "vertical",
              WebkitLineClamp: 2,
              overflow: "hidden",
              fontSize: "0.9rem",
              lineHeight: "1.4",
              color: "rgba(0, 0, 0, 0.87)"
            }}
          >
            <ThreadListItemPrimitive.Title />
          </MDTypography>
        </MDButton>
      </ThreadListItemPrimitive.Trigger>
    </ThreadListItemPrimitive.Root>
  );
};

// Mobile Thread List
const MobileThreadList = ({ onCreateNewThread }) => {
  const { t } = useTranslation();
  const [anchorEl, setAnchorEl] = useState(null);
  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleMenuClose = () => {
    setAnchorEl(null);
  };
  
  const handleNewChatClick = () => {
    onCreateNewThread();
  };
  
  return (
    <div className="md:hidden">
      <ThreadList.Root className="mb-3">
        <MDBox sx={{ display: 'flex', gap: 2}}>
          <MDButton 
            variant="contained" 
            color="info"
            onClick={handleNewChatClick}
            sx={{
              flex: 1,
              transition: 'all 0.2s ease-in-out',
              textTransform: 'none',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 1
            }}
          >
            <AddIcon fontSize="medium" />
            <MDTypography variant="button" fontWeight="medium" color="white">
              {t("prashna.new-question")}
            </MDTypography>
          </MDButton>
          <MDButton
            variant="outlined"
            color="secondary"
            onClick={handleMenuOpen}
            sx={{
              minWidth: '48px',
              width: '48px',
              height: '48px',
              borderRadius: '12px',
              border: '1.5px solid rgba(0, 0, 0, 0.12)',
              '&:hover': {
                border: '1.5px solid rgba(0, 0, 0, 0.24)',
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
              },
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <MDTypography variant="h6" color="secondary" sx={{ fontSize: '1.2rem' }}>
              ☰
            </MDTypography>
          </MDButton>
        </MDBox>
      </ThreadList.Root>
      
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        PaperProps={{
          style: {
            maxHeight: 400,
            width: '280px',
            marginTop: '8px',
            borderRadius: '12px',
            boxShadow: '0 8px 24px rgba(0, 0, 0, 0.12)',
            border: '1px solid rgba(0, 0, 0, 0.08)',
          },
        }}
      >
        <MDBox sx={{ p: 1, borderBottom: '1px solid rgba(0, 0, 0, 0.08)' }}>
          <MDTypography variant="h6" fontWeight="medium" color="dark">
            {t("prashna.threads.all-conversations")}
          </MDTypography>
        </MDBox>
        <ThreadList.Items
          components={{ ThreadListItem: CompactThreadListItem }}
        />
      </Menu>
    </div>
  );
};

export function MyAssistant({initialSelectedOption = "chat"}) {
  const {t} = useTranslation();
  const navigate = useNavigate();
  
  const [rawThreads, setRawThreads] = useState([]);
  const [uiThreads, setUiThreads] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [threadsLoading, setThreadsLoading] = useState(false);
  const [currentThreadLoading, setCurrentThreadLoading] = useState(false);
  const [selectedOption, setSelectedOption] = useState(initialSelectedOption);
  
  // Pagination state for threads
  const [threadsOffset, setThreadsOffset] = useState(0);
  const [hasMoreThreads, setHasMoreThreads] = useState(true);
  const [loadingMoreThreads, setLoadingMoreThreads] = useState(false);
  const threadsLimit = 20; // Limit per page

  // Update selectedOption when pathname changes
  useEffect(() => {
    setSelectedOption(initialSelectedOption);
  }, [initialSelectedOption]);

  const [controller, dispatch] = useMaterialUIController();
  const {selectedShop} = controller;
  const axiosInstance = useCancellableAxios();

  // Compute filtered threads based on search term
  const filteredThreads = searchTerm.trim() === "" 
    ? uiThreads 
    : uiThreads.filter(thread => {
        const title = thread.title.toLowerCase();
        return title.includes(searchTerm.toLowerCase());
      });

  const processThreads = (rawThreads) => {
    return rawThreads
      .filter((thread) => {
        return (
          thread.values?.thread_name &&
          (
            thread.values?.conversation_history?.run_summaries &&
            thread.values?.conversation_history?.run_summaries.length > 0
          ) || (
            thread.values?.user_interactions &&
            thread.values?.user_interactions.length > 0
          )
        );
      })
      .map((thread) => {
        const title = thread.values?.thread_name ?? thread.values?.user_query ?? "New Chat";
        const threadId = thread.thread_id;
        const updatedAt = thread.updated_at;
        
        // Get the latest final_answer from conversation history
        const runSummaries = thread.values?.conversation_history?.run_summaries || [];
        const latestRun = runSummaries[runSummaries.length - 1];
        const finalAnswer = latestRun?.final_answer || "";

        return {
          status: "regular",
          threadId: threadId,
          title: title,
          updatedAt: updatedAt,
          finalAnswer: finalAnswer,
        };
      });
  }

  // Handle search term change
  const handleSearchTermChange = (newSearchTerm) => {
    setSearchTerm(newSearchTerm);
  };

  // Process raw threads into UI threads
  useEffect(() => {
    const processed = processThreads(rawThreads);
    setUiThreads(processed);
  }, [rawThreads]);

  const threadIdRef = useRef();
  const creatingThreadRef = useRef(false);

  const createNewThread = async () => {
    // Check if we're already in the process of creating a thread
    if (currentThreadLoading) {
      return;
    }
    
    // If runtime is available with switchToNewThread capability, use it
    if (runtime?.switchToNewThread) {
      try {
        await runtime.switchToNewThread();
        return;
      } catch (error) {
        console.error("Error using runtime to create new thread:", error);
        // Fall through to manual method if runtime method fails
      }
    }
    
    // Fallback to manual creation if runtime method isn't available or failed
    try {
      setCurrentThreadLoading(true);
      const { thread_id } = await createThread(axiosInstance, selectedShop);
      threadIdRef.current = thread_id;
    } catch (error) {
      console.error("Failed to create new thread:", error);
    } finally {
      setCurrentThreadLoading(false);
    }
  }

  const fetchThreads = async (with_loading = true, append = false) => {
    if (with_loading) {
      setThreadsLoading(true);
    }
    
    const offset = append ? threadsOffset : 0;
    const { threads, has_more } = await getThreads(axiosInstance, selectedShop, threadsLimit, "idle", offset);
    
    if (append) {
      setRawThreads(prevThreads => [...prevThreads, ...threads]);
    } else {
      setRawThreads(threads);
      setThreadsOffset(0);
    }
    
    // Update hasMore based on whether we got fewer threads than requested
    setHasMoreThreads(has_more);
    
    if (with_loading) {
      setThreadsLoading(false);
    }
  };

  async function* tagWithThreadId(originalStream, threadId) {
    for await (const chunk of originalStream) {
      // Add the id two ways, so *every* consumer can see it
      // 1) top-level for quick checks   2) inside data for non-array events
      const taggedChunk = {
        ...chunk,
        thread_id: threadId,
        data:
          // for messages/partial & messages/complete the data is an ARRAY,
          // so leave it unchanged — guard will look at top-level field
          Array.isArray(chunk.data)
            ? chunk.data
            : { ...(chunk.data ?? {}), thread_id: threadId },
      };

      yield taggedChunk;
    }
  }

  async function* parseSSE(stream) {
    const reader = stream.getReader();
    const decoder = new TextDecoder('utf-8');
    let buffer = '';

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });

      let delimiterIndex;
      while ((delimiterIndex = buffer.indexOf("\n\n")) !== -1) {
        const rawEvent = buffer.slice(0, delimiterIndex);
        buffer = buffer.slice(delimiterIndex + 2);

        const lines = rawEvent.split("\n").filter(Boolean);
        let eventType = null;
        let dataPayload = null;

        for (const line of lines) {
          if (line.startsWith("event:")) {
            eventType = line.replace("event:", "").trim();
          } else if (line.startsWith("data:")) {
            const dataStr = line.replace("data:", "").trim();
            try {
              dataPayload = JSON.parse(dataStr);
            } catch (error) {
              console.error("Error parsing SSE data payload:", dataStr, error);
            }
          }
        }

        if (eventType && dataPayload !== null) {
          yield { event: eventType, data: dataPayload };
        }
      }
    }
  }

  const loadMoreThreads = async () => {
    if (loadingMoreThreads || !hasMoreThreads) return;
    
    setLoadingMoreThreads(true);
    const newOffset = threadsOffset + threadsLimit;
    setThreadsOffset(newOffset);
    
    const { threads, has_more } = await getThreads(axiosInstance, selectedShop, threadsLimit, "idle", newOffset);
    setRawThreads(prevThreads => [...prevThreads, ...threads]);
    
    // Update hasMore based on whether we got fewer threads than requested
    setHasMoreThreads(has_more);
    setLoadingMoreThreads(false);
  };

  const runtime = useLangGraphRuntime({
    threadId: threadIdRef.current,
    threads: selectedOption === "history" ? filteredThreads : uiThreads,
    adapters: {
      speech: new WebSpeechSynthesisAdapter(),
    },
    unstable_allowCancellation: true,
    fetchThreads: fetchThreads,
    stream: async (messages, options) => {
      const { abortSignal } = options || {};

      if (abortSignal) {
        abortSignal.addEventListener("abort", async () => {
          const currentRunId = globalStoreRef.runIdsByThread[threadIdRef.current];
          if (currentRunId) await cancelRun(threadIdRef.current, currentRunId, axiosInstance, selectedShop);
        });
      }

      if (!threadIdRef.current) {
        await createNewThread();
      }
      const threadId = threadIdRef.current;

      if (messages.length === 1 && messages[0].type === "human") {
        // Append new thread to sidebar list once first prompt is submitted
        const newThread = {
          status: "regular",
          threadId: threadId,
          title: messages[0].content,
        };
        setUiThreads(prevThreads => {
          // Remove existing thread with same ID if it exists
          const filteredThreads = prevThreads.filter(thread => thread.threadId !== threadId);
          return [newThread, ...filteredThreads];
        });
      }

      const backendStream = await sendMessage(
        {
          threadId,
          messages,
          abortSignal,
        },
        axiosInstance,
        selectedShop
      );

      return tagWithThreadId(parseSSE(backendStream), threadId);
    },

    onSwitchToNewThread: async () => {
        // Don't start if UI is already loading or if we're already creating a thread
        if (currentThreadLoading || creatingThreadRef.current) return;
        
        creatingThreadRef.current = true; // Immediate blocking
        setCurrentThreadLoading(true); // UI state
      
             try {
         const { thread_id } = await createThread(axiosInstance, selectedShop);
         
         if (!thread_id) {
           // If thread creation failed, log error and show user feedback
           console.error("Failed to create thread - no thread ID returned");
           toast.error("Failed to create new chat. Please try again.");
           return; // Exit gracefully without crashing
         }
         
         threadIdRef.current = thread_id;
         globalStoreRef.activeThreadId = thread_id;
         progressUtils.initializeProgress(thread_id);
         
       } catch (error) {
         // Log error for debugging but don't crash the app
         console.error("Error creating new thread:", error);
         toast.error("Failed to create new chat. Please try again.");
       } finally {
        creatingThreadRef.current = false; // Reset immediately 
        setCurrentThreadLoading(false); // UI state
      }
    },
    onSwitchToThread: async (threadId) => {
      globalStoreRef.activeThreadId = threadId;
      setCurrentThreadLoading(true);
      const state = await getThreadState(threadId, axiosInstance, selectedShop);
      threadIdRef.current = threadId;
      setCurrentThreadLoading(false);

      // Access the conversation history which contains all past conversations
      const conversationHistory = state.values?.conversation_history?.run_summaries || [];
      
      // Create an array to hold all messages from backend
      const backendMessages = [];

      // With an interrupt in the first message in the thread, conversation history is not being stored right now, so rendering from outside of run_summaries
      if ((!conversationHistory || conversationHistory.length === 0) && state.values?.user_interactions && state.values?.user_interactions.length > 0) {
        backendMessages.push({
          type: 'human',
          id: `${threadId}-user-query--0`,
          content: state.values?.user_query || "",
        });

        const user_interaction = state.values?.user_interactions[0];
        backendMessages.push({
          type: 'ai',
          id: `${threadId}-interrupt--0`,
          content: user_interaction?.question || "",
          metadata: {
            custom: {
              interrupt_message: user_interaction?.question ?? "",
              interrupt_actions: user_interaction?.actions ?? null,
              steps: state.values?.steps ?? [],
            }
          }
        });

        if (user_interaction?.response) {
          backendMessages.push({
            type: 'human',
            id: `${threadId}-user-response--0`,
            content: user_interaction?.response || "",
            metadata: {
              custom: {
                interrupt_message: user_interaction?.response ?? "",
              }
            }
          });
        }
      }

      // Map through the conversation history to create alternating user and assistant messages
      conversationHistory.forEach((convo, index) => {
        // Add user message
        backendMessages.push({
          type: 'human',
          id: `${threadId}-user-query-${index}`,
          content: convo.user_query || "",
        });

        if (
          convo.user_interactions
          && convo.user_interactions.length > 0
        ) {
          // only picking one rn; handle for multiple interactions ??
          const user_interaction = convo.user_interactions[0];
          backendMessages.push({
            type: 'ai',
            id: `${threadId}-interrupt-${index}`,
            content: user_interaction?.question || "",
            metadata: {
              custom: {
                interrupt_message: user_interaction?.question ?? "",
                interrupt_actions: user_interaction?.actions ?? null,
              }
            }
          });

          if (user_interaction?.response) {
            backendMessages.push({
              type: 'human',
              id: `${threadId}-user-response-${index}`,
              content: user_interaction?.response || "",
              metadata: {
                custom: {
                  interrupt_message: user_interaction?.response ?? "",
                }
              }
            });
          }
        }

        // Add assistant message
        backendMessages.push({
          type: 'ai',
          id: `${threadId}-assistant-response-${index}`,
          content: convo.final_answer || "",
          metadata: {
            custom: {
              current_step: "",
              reasoning: convo.reasoning || [],
              source: convo.source || "",
              final_answer: convo.final_answer || "",
              query_result: convo.query_result || {},
              plotly_image: convo.plotly_image || "",
              plotly_json_path: convo.plotly_json_path || "",
              expanded_user_query: convo.expanded_user_query || "",
              final_sql: convo.final_sql || "",
              query_data_processed: convo.query_data_processed || "",
              report_title: convo.report_title || "",
              query_tags: convo.query_tags || [],
              query_explanation: convo.query_explanation || "",
              progress_update: convo.progress_update || null,
              steps: convo.steps || []
            }
          }
        });
      });

      // Get any live messages that might be in the global store for this thread
      const liveMessages = globalStoreRef.messagesByThread[threadId] || [];

      // Merge backend messages with live messages, prioritizing live messages for recent activity
      const mergedMessages = [...backendMessages];

      // Add any live messages that aren't already in backend messages
      liveMessages.forEach(liveMsg => {
        const isDuplicate = mergedMessages.some(backendMsg =>
          backendMsg.id === liveMsg.id ||
          (backendMsg.type === liveMsg.type && backendMsg.content === liveMsg.content)
        ); // TODO: verify if dedup logic is correct

        if (!isDuplicate) {
          mergedMessages.push(liveMsg);
        }
      });
      
      return { messages: mergedMessages };
    },
  });

  useEffect(() => {
    // Reset pagination state when shop changes
    setThreadsOffset(0);
    setHasMoreThreads(true);
    setRawThreads([]);
    
    fetchThreads();
    if (!threadIdRef.current) {
      createNewThread();
    }
  }, [selectedShop]);

  const handleResetMainView = () => {
            navigate("/ask-ai");
  };

  const handleThreadSelect = (threadId) => {
    // Use the runtime to switch to the selected thread
    if (runtime?.switchToThread) {
      runtime.switchToThread(threadId);
    }
    // Reset to chat view
    navigate("/ask-ai");
  };

  const options = [
    { id: "chat", label: "New Chat", icon: "chat_outlined" },
    { id: "discover", label: "Discover", icon: "explore_outlined" },
    { id: "history", label: "Recent Chats", icon: "history_outlined" }
  ];

  return (
    <AssistantRuntimeProvider runtime={runtime}>
      <GlobalStyles />
      <MDBox height="83vh" sx={{ overflow: "hidden" }}>
        <article className="h-[83vh] overflow-hidden text-sm">
          <div className="flex h-full flex-col">
            <div 
              className={`flex-grow p-4 w-full overflow-hidden ${selectedOption === "chat" || selectedOption === "old_chat" ? "bg-white" : "bg-transparent"}`}
            >
              {(selectedOption === "chat" || selectedOption === "old_chat") && <MyThread currentThreadLoading={currentThreadLoading} />}
              {selectedOption === "discover" && <Discover onClose={handleResetMainView} />}
              {selectedOption === "history" && (
                <ChatHistory 
                  threads={filteredThreads} 
                  threadsLoading={threadsLoading} 
                  onClose={handleResetMainView} 
                  searchTerm={searchTerm}
                  onSearchChange={handleSearchTermChange}
                  loadMoreThreads={loadMoreThreads}
                  hasMoreThreads={hasMoreThreads}
                  loadingMoreThreads={loadingMoreThreads}
                  onThreadSelect={handleThreadSelect}
                />
              )}
            </div>
          </div>
        </article>
      </MDBox>
    </AssistantRuntimeProvider>
  );
}

export default MyAssistant;