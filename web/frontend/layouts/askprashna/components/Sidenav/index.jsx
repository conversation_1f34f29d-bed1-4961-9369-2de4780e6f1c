// @mui material components
import Card from "@mui/material/Card";
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import MenuIcon from "@mui/icons-material/Menu";
import MenuOpenIcon from "@mui/icons-material/MenuOpen";
import Divider from "@mui/material/Divider";
import Skeleton from "@mui/material/Skeleton";
import {ThreadList, ThreadListItemPrimitive, ThreadListPrimitive, useThreadListItem} from "@assistant-ui/react";
import { LoadMoreButton } from "@/layouts/askprashna/components/MyAssistant";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDTooltip from "@/components/MDTooltip";

// Material Dashboard 2 PRO React context
import { useMaterialUIController } from "@/context";

import {useTranslation} from "react-i18next";

function Sidenav({
    collapsed,
    setCollapsed,
  options,
  selectedOption,
  setSelectedOption,
  threads = [],
  threadsLoading = false,
  loadMoreThreads = () => {},
  hasMoreThreads = false,
  loadingMoreThreads = false,
  currentThreadId = null // Track the currently selected thread
}) {
  const [controller] = useMaterialUIController();
  const { miniSidenav, loginConfig, darkMode, shopConfig } = controller;

  const {t} = useTranslation();

  let showSubscriptionBanner = false && shopConfig.subscription_enabled
  && shopConfig.planDetails?.planType == "free";

  const toggleCollapse = () => {
    setCollapsed(!collapsed);
  };

  const SidenavThreadListItem = ({ onSelect }) => {
    const threadListItem = useThreadListItem();
    const isCurrentThread = threadListItem.threadId === currentThreadId;
    
    return (
      <ThreadListItemPrimitive.Root>
      <ThreadListItemPrimitive.Trigger
        className="w-full"
        asChild
        onClick={onSelect}
      >
      <MDBox
        component="div"
        onClick={() => setSelectedOption("old_chat")}
        sx={({
          palette: { light },
        }) => ({
          cursor: "pointer",
          p: 1,
          pl: 5, // Align with navigation text (padding + icon width + icon margin)
          borderRadius: "4px",
          backgroundColor: isCurrentThread ? light.main : "transparent",
          "&:hover": {
            backgroundColor: isCurrentThread ? light.main : "rgba(0, 0, 0, 0.03)",
          },
          mb: 1,
          width: "100%",
          transition: "background-color 0.1s ease-in-out",
          display: "flex",
          alignItems: "center",
          overflow: "hidden",
        })}
      >
        <MDTypography
          variant="button"
          color="dark"
          fontWeight="regular"
          sx={{
            display: "-webkit-box",
            WebkitBoxOrient: "vertical",
            WebkitLineClamp: 2,
            overflow: "hidden",
            fontSize: "0.8rem",
            lineHeight: "1.2",
            flex: 1,
            textOverflow: "ellipsis",
            whiteSpace: "normal",
            opacity: 0.85,
            wordBreak: "break-word", // Prevent horizontal overflow
            minWidth: 0, // Allow flex item to shrink
          }}
        >
          <ThreadListItemPrimitive.Title />
        </MDTypography>
      </MDBox>
      </ThreadListItemPrimitive.Trigger>
      </ThreadListItemPrimitive.Root>
    );
  };

  const renderSidenavItems = options.map(({ icon, label, id }, key) => {
    const itemKey = `item-${key}`;
    
    const navItem = (
      <MDTypography
        component="a"
        onClick={() => setSelectedOption(id)}
        variant="button"
        fontWeight="regular"
        textTransform="capitalize"
        sx={({
          borders: { borderRadius },
          functions: { pxToRem },
          palette: { light },
          transitions,
        }) => ({
          display: "flex",
          cursor: "pointer",
          alignItems: "center",
          borderRadius: borderRadius.md,
          backgroundColor: (selectedOption === id || (id === "history" && selectedOption === "old_chat")) ? light.main : "transparent",
          padding: collapsed ? `${pxToRem(10)} 0` : `${pxToRem(10)} ${pxToRem(16)}`,
          transition: transitions.create("background-color", {
            easing: transitions.easing.easeInOut,
            duration: transitions.duration.shorter,
          }),
          justifyContent: collapsed ? "center" : "flex-start",
          width: collapsed ? "40px" : "100%",
          minWidth: collapsed ? "40px" : "100%",
          maxWidth: collapsed ? "40px" : "100%",
          margin: 0,
          "&:hover": {
            backgroundColor: light.main,
          },
        })}
      >
        <MDBox mr={collapsed ? 0 : 1.5} lineHeight={1} color={darkMode ? "white" : "dark"} display="flex" justifyContent="center" alignItems="center">
          <Icon fontSize="small">{icon}</Icon>
        </MDBox>
        {!collapsed && t(label)}
      </MDTypography>
    );

    let displayItem = (
      <MDBox key={itemKey} component="li" pt={key === 0 ? 0 : 1} sx={{
        display: "flex",
        justifyContent: collapsed ? "center" : "flex-start",
        alignItems: "center",
        width: collapsed ? "100%" : "100%",
      }}>
        {collapsed ? (
          <MDTooltip title={t(label)} placement="right" arrow>
            {navItem}
          </MDTooltip>
        ) : (
          navItem
        )}
      </MDBox>
    );

    if (id === "chat") {
      displayItem = (
        <ThreadListPrimitive.New asChild>
          {displayItem}
        </ThreadListPrimitive.New>
      )
    }

    return displayItem;
  });

  return (
    <Card
      sx={{
        borderRadius: "0.50rem", // Match main section border radius
        position: "sticky",
        top: showSubscriptionBanner ? "4rem" : "1%",
        transition: "width 0.3s ease",
        width: collapsed ? "64px" : "240px",
        maxWidth: collapsed ? "64px" : "240px",
        height: "fit-content",
        maxHeight: "calc(100vh - 100px)",
        display: "flex",
        flexDirection: "column",
        marginRight: "12px",
        overflow: "hidden",
        backgroundColor: "white", // Explicit white background
        boxShadow: "0 2px 5px 0 rgba(0, 0, 0, 0.1)", // Match main section shadow
        border: "none", // Remove default border if any
      }}
    >
      <MDBox
        display="flex"
        alignItems="center"
        justifyContent={collapsed ? "center" : "space-between"}
        p={2}
        sx={{
          borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
        }}
      >
        {!collapsed && (
          <MDTypography variant="h6" fontWeight="medium">
            Prashna AI
          </MDTypography>
        )}
        {collapsed ? (
            <IconButton onClick={toggleCollapse} sx={{ mx: collapsed ? 'auto' : 0 }}>
              <MenuIcon />
            </IconButton>
        ) : (
          <IconButton onClick={toggleCollapse} sx={{ mx: collapsed ? 'auto' : 0 }}>
            <MenuOpenIcon />
          </IconButton>
        )}
      </MDBox>
      <MDBox
        component="ul"
        display="flex"
        flexDirection="column"
        p={2}
        m={0}
        sx={{
          listStyle: "none",
          alignItems: collapsed ? "center" : "flex-start",
          width: "100%",
          gap: 1.5,
        }}
      >
        {renderSidenavItems}
      </MDBox>

      {/* Chat History Section (visible only in expanded mode) */}
      {!collapsed && threads.length > 0 && (
        <MDBox px={2} sx={{ flex: 1, display: "flex", flexDirection: "column", overflow: "hidden" }}>
          <MDBox
            sx={{
              flex: 1,
              overflowY: "auto",
              maxHeight: "100%",
              pb: 2,
              px: 0.5,
              '&::-webkit-scrollbar': {
                width: '6px',
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: 'rgba(0,0,0,0.2)',
                borderRadius: '3px',
              }
            }}
          >
            {threadsLoading ? (
              Array.from({ length: 3 }).map((_, index) => (
                <MDBox
                  key={index}
                  sx={{
                    p: 1.5,
                    borderRadius: "8px",
                    backgroundColor: "rgba(0, 0, 0, 0.02)",
                    mb: 1.5,
                    width: "100%",
                    display: "flex",
                    alignItems: "center",
                    boxShadow: "0 1px 3px rgba(0,0,0,0.05)",
                    overflow: "hidden",
                  }}
                >
                  <Skeleton 
                    variant="circular" 
                    width={20} 
                    height={20} 
                    sx={{ mr: 1.5, flexShrink: 0 }} 
                  />
                  <Skeleton 
                    variant="text" 
                    width="85%" 
                    height={20} 
                  />
                </MDBox>
              ))
            ) : (
              <>
                {threads.length > 0 && (
                  <ThreadList.Items
                    components={{ ThreadListItem: SidenavThreadListItem }}
                  />
                )}
                
                {/* Load More Button - Same structure as thread items */}
                {hasMoreThreads && (
                  <MDBox
                    component="div"
                    onClick={loadMoreThreads}
                    sx={{
                      cursor: loadingMoreThreads ? "default" : "pointer",
                      p: 1,
                      pl: 5, // Same as thread items
                      borderRadius: "4px",
                      "&:hover": {
                        backgroundColor: loadingMoreThreads ? "transparent" : "rgba(0, 0, 0, 0.03)",
                      },
                      mb: 1,
                      width: "100%",
                      transition: "background-color 0.1s ease-in-out",
                      display: "flex",
                      alignItems: "center",
                      overflow: "hidden",
                      opacity: loadingMoreThreads ? 0.6 : 1,
                    }}
                  >
                    <MDTypography
                      variant="button"
                      color="secondary"
                      fontWeight="regular"
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: 0.5,
                        fontSize: "0.8rem", // Same as thread items
                        lineHeight: "1.2",
                        flex: 1,
                        fontStyle: "italic", // Distinctive italic style
                        opacity: 0.75, // Slightly less muted than before
                        minWidth: 0,
                      }}
                    >
                      {loadingMoreThreads ? (
                        <>
                          <MDBox
                            sx={{
                              width: 10,
                              height: 10,
                              border: '1.5px solid',
                              borderColor: 'rgba(0, 0, 0, 0.1)',
                              borderTopColor: 'currentColor',
                              borderRadius: '50%',
                              animation: 'spin 1s linear infinite',
                              '@keyframes spin': {
                                '0%': { transform: 'rotate(0deg)' },
                                '100%': { transform: 'rotate(360deg)' }
                              }
                            }}
                          />
                          Loading...
                        </>
                      ) : (
                        <>
                          <MDBox
                            component="span"
                            sx={{
                              fontSize: "0.6rem",
                              opacity: 0.8,
                              mr: 0.5,
                              lineHeight: 1,
                            }}
                          >
                            •••
                          </MDBox>
                          Load more
                        </>
                      )}
                    </MDTypography>
                  </MDBox>
                )}
              </>
            )}
          </MDBox>
        </MDBox>
      )}
    </Card>
  );
}

export default Sidenav;
