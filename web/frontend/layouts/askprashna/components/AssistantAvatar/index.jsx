
import AssistantIcon from '@mui/icons-material/Assistant';
import MDAvatar from "@/components/MDAvatar";

const AssistantAvatar = ({ size = "xs" }) => {
    const avatarStyles = {
      border: "none",
      position: "relative",
      backgroundColor: "rgba(26, 115, 232, 0.08)",
      "&:hover, &:focus": {
        zIndex: "10",
      },
    };

    const getIconFontSize = () => {
      return size === "sm" ? "18px" : "14px";
    };
  
    return (
      <div className="flex-shrink-0">
        <MDAvatar sx={avatarStyles} size={size}>
          <AssistantIcon 
            fontSize="16px" 
            sx={{ 
              color: "rgba(26, 115, 232, 0.7)",
              fontSize: getIconFontSize()
            }} 
          />
        </MDAvatar>
      </div>
    );
}

export default AssistantAvatar;
