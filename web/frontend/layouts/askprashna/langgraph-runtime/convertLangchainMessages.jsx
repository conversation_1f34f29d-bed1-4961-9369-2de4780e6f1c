"use client";

import { useExternalMessageConverter } from "@assistant-ui/react";

const contentToParts = (content) => {
  if (typeof content === "string")
    return [{ type: "text", text: content }];
  return content
    .map((part) => {
      const type = part.type;
      switch (type) {
        case "text":
          return { type: "text", text: part.text };
        case "image_url":
          if (typeof part.image_url === "string") {
            return { type: "image", image: part.image_url };
          } else {
            return {
              type: "image",
              image: part.image_url.url,
            };
          }
        case "tool_use":
          return null;
        default:
          throw new Error(`Unknown content part type: ${type}`);
      }
    })
    .filter((a) => a !== null);
};

export const convertLangchainMessages = (message) => {
  switch (message.type) {
    case "system":
      return {
        role: "system",
        id: message.id,
        content: [{ type: "text", text: message.content }],
      };
    case "human":
      return {
        role: "user",
        id: message.id,
        content: contentToParts(message.content),
      };
    case "ai":
      return {
        role: "assistant",
        id: message.id,
        content: [
          ...contentToParts(message.content),
          ...(message.tool_calls?.map((chunk) => ({
            type: "tool-call",
            toolCallId: chunk.id,
            toolName: chunk.name,
            args: chunk.args,
            argsText:
              message.tool_call_chunks?.find((c) => c.id === chunk.id)?.args ??
              JSON.stringify(chunk.args),
          })) ?? []),
        ],
        metadata: {
          custom: message.metadata?.custom ?? {}
        },
      };
    case "tool":
      return {
        role: "tool",
        toolName: message.name,
        toolCallId: message.tool_call_id,
        result: message.content,
      };
  }
};
