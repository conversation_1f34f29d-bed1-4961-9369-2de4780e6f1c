import { useEffect, useRef, useState, useCallback } from "react";
import {
  useExternalMessageConverter,
  useExternalStoreRuntime,
  useThread,
  useThreadListItemRuntime,
} from "@assistant-ui/react";
import { convertLangchainMessages } from "./convertLangchainMessages.jsx";
import { useLangGraphMessages } from "./useLangGraphMessages.jsx";
import { SimpleImageAttachmentAdapter } from "@assistant-ui/react";
import { globalStoreRef } from "./globalStore.jsx";
import { progressUtils } from './progressUtils.jsx';

const symbolLangGraphRuntimeExtras = Symbol("langgraph-runtime-extras");

const getPendingToolCalls = (messages) => {
  const pendingToolCalls = new Map();
  for (const message of messages) {
    if (message.type === "ai") {
      for (const toolCall of message.tool_calls ?? []) {
        pendingToolCalls.set(toolCall.id, toolCall);
      }
    }
    if (message.type === "tool") {
      pendingToolCalls.delete(message.tool_call_id);
    }
  }

  return [...pendingToolCalls.values()];
};

const getMessageContent = (msg) => {
  const allContent = [
    ...msg.content,
    ...msg.attachments.flatMap((a) => a.content),
  ];
  const content = allContent.map((part) => {
    const type = part.type;
    switch (type) {
      case "text":
        return { type: "text", text: part.text };
      case "image":
        return { type: "image_url", image_url: { url: part.image } };
      case "tool-call":
        throw new Error("Tool call appends are not supported.");
      default:
        throw new Error(`Unsupported append content part type: ${type}`);
    }
  });

  if (content.length === 1 && content[0]?.type === "text") {
    return content[0].text ?? "";
  }

  return content;
};

const asLangGraphRuntimeExtras = (extras) => {
  if (
    typeof extras !== "object" ||
    extras == null ||
    !(symbolLangGraphRuntimeExtras in extras)
  )
    throw new Error(
      "This method can only be called when you are using useLangGraphRuntime",
    );

  return extras;
};

export const useLangGraphInterruptState = () => {
  const { interrupt } = useThread((t) => asLangGraphRuntimeExtras(t.extras));
  return interrupt;
};

export const useLangGraphSend = () => {
  const { send } = useThread((t) => asLangGraphRuntimeExtras(t.extras));
  return send;
};

export const useLangGraphSendCommand = () => {
  const send = useLangGraphSend();
  return (command) => send([], { command });
};

export const useLangGraphRuntime = ({
  autoCancelPendingToolCalls,
  adapters: { attachments, feedback, speech } = {},
  unstable_allowImageAttachments,
  unstable_allowCancellation,
  fetchThreads,
  stream,
  threadId,
  threads,
  onSwitchToNewThread,
  onSwitchToThread,
}) => {
  /* keep the freshest thread id so inner hooks always receive the right one */
  const [activeThreadId, setActiveThreadId] = useState(threadId);
  useEffect(() => {
    setActiveThreadId(threadId);
  }, [threadId]);

  const runningByThreadRef = useRef({});

  const { interrupt, messages, sendMessage, cancel, setMessages, followupSuggestions } =
    useLangGraphMessages({
      stream,
      fetchThreads,
      threadId: activeThreadId
    });

  const handleSendMessage = async (messages, config) => {
    try {
      runningByThreadRef.current[activeThreadId] = true;
      await sendMessage(messages, config);
    } catch (error) {
      console.error("Error streaming messages:", error);
    } finally {
      runningByThreadRef.current[activeThreadId] = false;
    }
  };

  const threadMessages = useExternalMessageConverter({
    callback: convertLangchainMessages,
    messages,
    isRunning: runningByThreadRef.current[activeThreadId],
  });

  if (attachments && unstable_allowImageAttachments)
    throw new Error(
      "Replace unstable_allowImageAttachments with `adapters: { attachments: new SimpleImageAttachmentAdapter() }`.",
    );

  if (unstable_allowImageAttachments)
    attachments = new SimpleImageAttachmentAdapter();

  const switchToThread = !onSwitchToThread
    ? undefined
    : async (externalId) => {
      try {
        const { messages } = await onSwitchToThread(externalId);
        setMessages(externalId, messages);
        setActiveThreadId(externalId);

        // Force an immediate progress state update for the new thread
        // Trigger a re-render by updating the timestamp
        if (globalStoreRef) {
          globalStoreRef.lastUpdate = Date.now();
        }
      } catch (error) {
        console.error("Error in switchToThread:", error);
        setActiveThreadId(externalId);
      }
    };

  const threadList = {
    threadId,
    threads,
    onSwitchToNewThread: !onSwitchToNewThread
      ? undefined
      : async () => {
          const currentMessages = messages;
          
        try {
          await onSwitchToNewThread();
          if (messages === currentMessages) {
            setMessages([]);
          }
        } catch (error) {
          console.error("Error switching to new thread:", error);
        }
      },
    onSwitchToThread: switchToThread,
  };

  const loadingRef = useRef(false);
  const threadListItemRuntime = useThreadListItemRuntime({ optional: true });
  useEffect(() => {
    if (!threadListItemRuntime || !switchToThread || loadingRef.current) return;

    const externalId = threadListItemRuntime.getState().externalId;
    if (externalId) {
      loadingRef.current = true;
      switchToThread(externalId).finally(() => {
        loadingRef.current = false;
      });
    }
  }, []);

  return useExternalStoreRuntime({
    isRunning: runningByThreadRef.current[activeThreadId],
    messages: threadMessages,
    adapters: {
      attachments,
      feedback,
      speech,
      threadList,
    },
    suggestions: followupSuggestions,
    extras: {
      [symbolLangGraphRuntimeExtras]: true,
      interrupt,
      send: handleSendMessage
    },
    onNew: (msg) => {
      const cancellations =
        autoCancelPendingToolCalls !== false
          ? getPendingToolCalls(messages).map((t) => ({
              type: "tool",
              name: t.name,
              tool_call_id: t.id,
              content: JSON.stringify({ cancelled: true }),
            }))
          : [];

      return handleSendMessage(
        [
          ...cancellations,
          {
            type: "human",
            content: getMessageContent(msg),
          },
        ],
        {
          runConfig: msg.runConfig,
        },
      );
    },
    onAddToolResult: async ({ toolCallId, toolName, result }) => {
      await handleSendMessage(
        [
          {
            type: "tool",
            name: toolName,
            tool_call_id: toolCallId,
            content: JSON.stringify(result),
          },
        ],
        {},
      );
    },
    onCancel: unstable_allowCancellation
      ? async () => {
        cancel();
        runningByThreadRef.current[activeThreadId] = false;
      }
      : undefined,
  });
};
