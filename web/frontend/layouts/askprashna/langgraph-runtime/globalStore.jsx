// ─── shared between all hook instances ─────────────────────────────
export const globalStoreRef = {
  messagesByThread: {},   // { [threadId]: Message[] }
  interruptByThread: {},   // { [threadId]: Interrupt | undefined }
  abortControllers: {},  // { [threadId]: AbortController }
  runIdsByThread: {},    // { [threadId]: string } - thread-specific run IDs
  progressByThread: {},  // { [threadId]: { progressValue: number, nodeProgressMap: object } } - thread-specific progress
  aiStepsByThread: {},
  activeThreadId: null,  // { string } - track the currently active thread
  lastUpdate: null,      // { number } - track last update timestamp
};
// ─────────────────────────────────────────────────────────────────── 
