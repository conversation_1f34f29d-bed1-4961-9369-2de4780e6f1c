import { useState, useCallback, useReducer } from "react";
import { v4 as uuidv4 } from "uuid";
import { globalStoreRef } from "./globalStore.jsx";
import { progressUtils } from "./progressUtils.jsx";

export const useLangGraphMessages = ({ stream, fetchThreads, threadId }) => {
  const [, forceRender] = useReducer((x) => x + 1, 0);
  const [followupSuggestions, setFollowupSuggestions] = useState([]);

  // Get thread-specific run ID
  const getRunIdForThread = (tid) => globalStoreRef.runIdsByThread[tid] || null;
  const setRunIdForThread = (tid, runId) => {
    globalStoreRef.runIdsByThread = {
      ...globalStoreRef.runIdsByThread,
      [tid]: runId
    };
  };

  const sendMessage = useCallback(
    async (newMessages, config) => {
      setRunIdForThread(threadId, null);
      progressUtils.initializeProgress(threadId);
      progressUtils.setProgress(threadId, {
        progressValue: 0,
        nodeProgressMap: {}
      });

      // Clear any messages for which the run has been aborted
      const currentMessages = globalStoreRef.messagesByThread[threadId] ?? [];
      const messagesWithoutAborted = currentMessages.filter(msg => !msg.metadata?.custom?.aborted);

      if (messagesWithoutAborted.length !== currentMessages.length) {
        globalStoreRef.messagesByThread = {
          ...globalStoreRef.messagesByThread,
          [threadId]: messagesWithoutAborted,
        };
        forceRender();
      }

      // ensure all messages have an ID
      newMessages = newMessages.map((m) => (m.id ? m : { ...m, id: uuidv4() }));

      const addMessages = (tid, newMsgs) => {
        if (newMsgs.length === 0) return;

        /* deduplicate + preserve order exactly like before */
        const existing = globalStoreRef.messagesByThread[tid] ?? [];
        const map = new Map(existing.map((m) => [m.id, m]));
        for (const m of newMsgs) map.set(m.id ?? uuidv4(), m);

        globalStoreRef.messagesByThread = {
          ...globalStoreRef.messagesByThread,
          [tid]: [...map.values()],
        };

        forceRender();

        // trigger a global state update to ensure all components re-render
        globalStoreRef.lastUpdate = Date.now();
      };

      addMessages(threadId, [...(globalStoreRef.messagesByThread[threadId] ?? []), ...newMessages]);

      const processStreamChunk = (chunk) => {
        const incomingThread =
          chunk.thread_id ?? chunk.data?.thread_id ?? threadId;

        if (
          chunk.event === "messages/partial" ||
          chunk.event === "messages/complete"
        ) {
          addMessages(incomingThread, chunk.data);
        } else if (chunk.event === "updates") {
          globalStoreRef.interruptByThread = {
            ...globalStoreRef.interruptByThread,
            [incomingThread]: chunk.data.__interrupt__?.[0],
          };
        }  else if (chunk.event === "metadata") {
          // save the run id from data and reuse it to assign message.id to the message
          const {run_id} = chunk.data;
          if (run_id) {
            setRunIdForThread(incomingThread, run_id);
            setFollowupSuggestions([]);
          }
        } else if (chunk.event === "error") {
          const {message} = chunk.data;
          console.error("error", message);
          const assistantMessage = {
            type: 'ai',
            id: `${getRunIdForThread(incomingThread)}-full-answer`,
            content: "",
            metadata: {
              custom: {
                error: message,
              }
            }
          };
          addMessages(incomingThread, [assistantMessage]);
        } else if (chunk.event === "values" || chunk.event === "custom") {
          const {
            steps,
            current_step,
            final_answer,
            query_result,
            plotly_image,
            plotly_json_path,
            followup_questions,
            report_title,
            query_tags,
            query_explanation,
            expanded_user_query,
            final_sql,
            query_data_processed,
            reasoning,
            source,
            progress_update
          } = chunk.data;

          if (!!followup_questions && followup_questions.length > 0) {
            setFollowupSuggestions(followup_questions.map((question) => ({
              prompt: question,
              method: "replace",
              autoSend: true,
            })));
          }

          // TODO: this will fetch threads on each final_answer, do this only when the final_answer is received for the first time in a thread
          // The purpose of this is to update the name in sidebar from `question title` to actual thread name
          if (!!final_answer) {
            fetchThreads(false);
          }

          const assistantMessage = {
            type: 'ai',
            id: `${getRunIdForThread(incomingThread)}-full-answer`,
            content: final_answer ?? "",
            metadata: {
              custom: {
                thread_id: incomingThread,
                steps: (!!steps && steps.length > 0) ? steps : [],
                current_step: (!!current_step) ? current_step : "",
                reasoning: (!!reasoning) ? reasoning : [],
                source: (!!source) ? source : "",
                final_answer: (!!final_answer) ? final_answer : "",
                query_result: (!!query_result) ? query_result : {},
                plotly_image: (!!plotly_image) ? plotly_image : "",
                plotly_json_path: (!!plotly_json_path) ? plotly_json_path : "",
                report_title: (!!report_title) ? report_title : "",
                query_tags: (!!query_tags) ? query_tags : [],
                query_explanation: (!!query_explanation) ? query_explanation : "",
                expanded_user_query: (!!expanded_user_query) ? expanded_user_query : "",
                final_sql: (!!final_sql) ? final_sql : "",
                query_data_processed: (!!query_data_processed) ? query_data_processed : 0,
                progress_update: (!!progress_update) ? progress_update : null,
              }
            }
          };
          addMessages(incomingThread, [assistantMessage]);
        } else if (chunk.event === "__interrupt__") {
          globalStoreRef.interruptByThread[incomingThread] = chunk.data.interrupt_message;
          const {
            current_step,
            interrupt_message,
            interrupt_actions
          } = chunk.data;

          const assistantMessage = {
            type: 'ai',
            id: `${getRunIdForThread(incomingThread)}-full-answer`,
            content: interrupt_message ?? "",
            metadata: {
              custom: {
                thread_id: incomingThread,
                current_step: (!!current_step) ? current_step : "",
                is_interrupt: true,
                interrupt_message: (!!interrupt_message) ? interrupt_message : "",
                interrupt_actions: (!!interrupt_actions) ? interrupt_actions : null,
              }
            }
          };
          addMessages(incomingThread, [assistantMessage]);
        }
      }

      addMessages(
        threadId,
        [...(globalStoreRef.messagesByThread[threadId] ?? []), ...newMessages]
      );

      const abortController = new AbortController();
      globalStoreRef.abortControllers = {
        ...globalStoreRef.abortControllers,
        [threadId]: abortController,
      };

      const chunkStream = await stream(newMessages, {
        ...config,
        runId: () => getRunIdForThread(threadId),
        abortSignal: abortController.signal,
      });

      try {
        for await (const chunk of chunkStream) {
          processStreamChunk(chunk);
        }

        delete globalStoreRef.abortControllers[threadId];

      } catch (error) {
        console.error("Stream aborted or failed:", error);

        if (error.name === 'AbortError') {
          // Clean up thread-specific state
          delete globalStoreRef.runIdsByThread[threadId];
          delete globalStoreRef.aiStepsByThread[threadId];
          delete globalStoreRef.interruptByThread[threadId];
          progressUtils.clearProgress(threadId);
          setFollowupSuggestions([]);
          forceRender();
        }
      }

      // Optionally process any remaining data in the buffer if it makes sense to do so.
      // if (buffer.trim().length) {
      //   const lines = buffer.split("\n").filter(line => line.trim());
      //   let eventType = null;
      //   let dataPayload = null;
      //   for (const line of lines) {
      //     if (line.startsWith("event:")) {
      //       eventType = line.replace("event:", "").trim();
      //     } else if (line.startsWith("data:")) {
      //       const dataStr = line.replace("data:", "").trim();
      //       try {
      //         dataPayload = JSON.parse(dataStr);
      //       } catch (error) {
      //         console.error("Error parsing trailing data payload:", dataStr, error);
      //       }
      //     }
      //   }
      //   if (eventType && dataPayload !== null) {
      //     processStreamChunk({ event: eventType, data: dataPayload });
      //   }
      // }
      // Clean up completed stream
      delete globalStoreRef.abortControllers[threadId];
    },
    [globalStoreRef.messagesByThread[threadId] ?? [], stream, threadId],
  );

  const cancel = useCallback(() => {
    const ctrl = globalStoreRef.abortControllers[threadId];
    ctrl?.abort();

    // Add "Run aborted" message
    const currentRunId = getRunIdForThread(threadId);
    if (currentRunId) {
      const currentMessages = globalStoreRef.messagesByThread[threadId] ?? [];

      let lastHumanMessageIndex = -1;
      for (let i = currentMessages.length - 1; i >= 0; i--) {
        if (currentMessages[i].type === 'human') {
          lastHumanMessageIndex = i;
          break;
        }
      }

      const messagesToKeep = lastHumanMessageIndex >= 0
        ? currentMessages.slice(0, lastHumanMessageIndex + 1)
        : currentMessages;

      const abortedMessage = {
        type: 'ai',
        id: `${currentRunId}-aborted`,
        content: "Run aborted",
        metadata: {
          custom: {
            aborted: true,
            thread_id: threadId
          }
        }
      };

      globalStoreRef.messagesByThread = {
        ...globalStoreRef.messagesByThread,
        [threadId]: [...messagesToKeep, abortedMessage],
      };
    }

    // Clean up thread-specific run ID and progress when cancelling
    delete globalStoreRef.runIdsByThread[threadId];
    delete globalStoreRef.abortControllers[threadId];
    delete globalStoreRef.aiStepsByThread[threadId];
    delete globalStoreRef.interruptByThread[threadId];
    progressUtils.clearProgress(threadId);
    setFollowupSuggestions([]);
    forceRender();
  }, [threadId]);

  globalStoreRef.activeThreadId = threadId;

  const currentMessages = globalStoreRef.messagesByThread[threadId] ?? [];
  const currentInterrupt = globalStoreRef.interruptByThread[threadId] ?? undefined;

  return {
    interrupt: currentInterrupt,
    messages: currentMessages,
    sendMessage,
    cancel,
    setMessages: (tid, msgs) => {
      globalStoreRef.messagesByThread = {
        ...globalStoreRef.messagesByThread,
        [tid]: msgs,
      };

      const existingMessages = globalStoreRef.messagesByThread[tid];
      const existingProgress = progressUtils.getProgress(tid);
      const existingRunId = globalStoreRef.runIdsByThread[tid];

      if ((!msgs || msgs.length === 0) && !existingMessages && existingProgress.progressValue === 0 && Object.keys(existingProgress.nodeProgressMap).length === 0 && !existingRunId) {
        // This is a completely new thread, initialize empty state
        progressUtils.initializeProgress(tid);
      }

      forceRender();
    },
    followupSuggestions
  };
};
