import { globalStoreRef } from "./globalStore.jsx";

export const progressUtils = {
  initializeProgress: (threadId) => {
    if (!globalStoreRef || !globalStoreRef.progressByThread) return;

    if (globalStoreRef.progressByThread[threadId]) return;

    globalStoreRef.progressByThread = {
      ...globalStoreRef.progressByThread,
      [threadId]: {
        progressValue: 0,
        nodeProgressMap: {}
      },
    };

    globalStoreRef.lastUpdate = Date.now();
  },

  updateProgress: (threadId, progressUpdate) => {
    if (!progressUpdate || !progressUpdate.identifier) {
      return;
    }

    if (globalStoreRef && globalStoreRef.progressByThread) {
      const currentProgress = globalStoreRef.progressByThread[threadId] || {
        progressValue: 0,
        nodeProgressMap: {}
      };

      const newNodeProgressMap = {
        ...currentProgress.nodeProgressMap,
        [progressUpdate.identifier]: progressUpdate
      };

      const totalProgress = progressUpdate.type === "overwrite"
        ? progressUpdate.percentage
        : Math.min(currentProgress.progressValue + progressUpdate.percentage, 100);

      const newProgressState = {
        progressValue: totalProgress,
        nodeProgressMap: newNodeProgressMap
      };

      globalStoreRef.progressByThread = {
        ...globalStoreRef.progressByThread,
        [threadId]: newProgressState
      };

      globalStoreRef.lastUpdate = Date.now();
    }
  },

  getProgress: (threadId) => {
    if (globalStoreRef && globalStoreRef.progressByThread) {
      return globalStoreRef.progressByThread[threadId] || {
        progressValue: 0,
        nodeProgressMap: {}
      };
    }
    return { progressValue: 0, nodeProgressMap: {} };
  },

  setProgress: (threadId, progress) => {
    if (globalStoreRef && globalStoreRef.progressByThread) {
      globalStoreRef.progressByThread = {
        ...globalStoreRef.progressByThread,
        [threadId]: progress
      };
      globalStoreRef.lastUpdate = Date.now();
    }
  },

  clearProgress: (threadId) => {
    if (globalStoreRef && globalStoreRef.progressByThread) {
      delete globalStoreRef.progressByThread[threadId];
      globalStoreRef.lastUpdate = Date.now();
    }
  },

  getAllProgress: () => {
    if (globalStoreRef && globalStoreRef.progressByThread) {
      return globalStoreRef.progressByThread;
    }
    return {};
  }
}; 
