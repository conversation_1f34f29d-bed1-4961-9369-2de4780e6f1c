const blendedSlugs = {
    "blended:spend" : {
        title : "blended.spend",
        description: "blended.spend-desc",
        format_type: "currency"
    },
    // Catalog-scoped blended ad spend (used only in product performance catalog context)
    "blended:catalog-spend" : {
        title : "blended.catalog-spend",
        description: "blended.catalog-spend-desc",
        format_type: "currency",
        sources: ["shopify", "facebook-marketing", "google-ads"]
    },
    "blended:impressions" : {
        title : "blended.impressions",
        description: "blended.impressions-desc"
    },
    "blended:clicks" : {
        title : "blended.clicks",
        description: "blended.clicks-desc"
    },
    "blended:ctr" : {
        title : "blended.ctr",
        description: "blended.ctr-desc",
        format_type: "percent"
    },
    "blended:cpc" : {
        title : "blended.cpc",
        description: "blended.cpc-desc",
        format_type: "currency"
    },
    "blended:cac" : {
        title : "blended.cac",
        description: "blended.cac-desc",
        format_type: "currency",
        sources: ["shopify", "facebook-marketing", "google-ads"]
    },
    "blended:roas" : {
        title : "blended.roas",
        description: "blended.roas-desc",
        format_type: "roas",
        sources: ["shopify", "facebook-marketing", "google-ads"]
    },
    // Catalog-scoped blended ROAS (used only in product performance catalog context)
    "blended:catalog-roas" : {
        title : "blended.catalog-roas",
        description: "blended.catalog-roas-desc",
        format_type: "roas",
        sources: ["shopify", "facebook-marketing", "google-ads"]
    },
    "blended:cpm" : {
        title : "blended.cpm",
        description: "blended.cpm-desc",
        format_type: "currency"
    },
}

const blendedMetrics = Object.keys(blendedSlugs).reduce((acc, key) => {
    acc[key] = {
        id: key,
        sources: blendedSlugs[key].sources ?? ["facebook-marketing", "google-ads"],
        accessor: key,
        title : blendedSlugs[key].title ?? key,
        description: blendedSlugs[key].description ?? "",
        format_type: blendedSlugs[key].format_type ?? ""
    }
    return acc;
}, {});

export default blendedMetrics;