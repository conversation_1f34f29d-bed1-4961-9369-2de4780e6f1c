import React from "react";
import { useTranslation } from "react-i18next";

import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDTooltip from "@/components/MDTooltip";

import logoShopify from "@/assets/images/shopify-logo.svg";
import logoFacebook from "@/assets/images/facebook-logo.png";
import logoGoogleAds from "@/assets/images/google-ads-logo.png";
import logoGoogleAnalytics from "@/assets/images/google-analytics-logo.png";
import {SOURCE_FB, SOURCE_SHOPIFY, SOURCE_GOOGLE_ADS, SOURCE_GOOGLE_ANALYTICS, getMetricBySlug} from "@/layouts/dashboards/metrics/metadata"

const metricStyle = {
  whiteSpace: "nowrap",
  display: "inline-block",
  minWidth: "fit-content"
}

export const sourceLogo = (source) => {
  switch (source) {
    case SOURCE_GOOGLE_ADS:
      return logoGoogleAds;
    case SOURCE_GOOGLE_ANALYTICS:
      return logoGoogleAnalytics;
    case SOURCE_FB:
      return logoFacebook;
    case SOURCE_SHOPIFY:
      return logoShopify;
  }
}

const MetricTitle = ({metric, removeLogoInTitle, isTableColumn = false, integrationStates = {}}) => {

    const {t} = useTranslation();
    const {title, description, sources} = getMetricBySlug(metric);

    if (!description) {
      return <MDTypography variant="button" fontWeight="regular" color="dark" mr={0.4} sx={metricStyle}>{t(title)}</MDTypography>
    }

    let logoImages = (sources ?? []).map(source => sourceLogo(source));
    
    // Apply greying out for disconnected integrations if integrationStates is provided
    const getLogoStyle = (source) => {
        if (!integrationStates || Object.keys(integrationStates).length === 0) {
            return {}; // No integration states provided, use default styling
        }
        
    // Shopify is always connected, never grey out
    if (source === SOURCE_SHOPIFY) {
      return {};
    }
    let isConnected = false;
    if (source === SOURCE_FB && integrationStates.hasMetaConnection === true) {
      isConnected = true;
    } else if (source === SOURCE_GOOGLE_ADS && integrationStates.hasGoogleAdsConnection === true) {
      isConnected = true;
    }
    return isConnected ? {} : { 
      filter: 'grayscale(100%)', 
      opacity: 0.5 
    };
    };
  
    if (removeLogoInTitle) {
        return (
          <MDTooltip className="tooltip" arrow={false} placement="top" title={t(title)} description={t(description)} logos={logoImages}>
              <u><MDTypography variant="caption" color="dark" fontWeight="bold">{t(title)}</MDTypography></u>
          </MDTooltip>
        )
    }

    // For table column, the logo images will appear above the title and smaller; otherwise logo images will appear to the left of title.
    if (isTableColumn) {
      return (
        <MDTooltip placement="top" title={t(title)} description={t(description)} logos={logoImages}>
          <MDBox style={{borderBottom: "1px dotted #ccc", cursor: "default"}} display="flex" flexDirection="column" alignItems="flex-start" p={0}>
            {logoImages.length > 1 ? (
              <MDBox display="flex" flexDirection="row">
                {logoImages.map((l, ind) => {
                  const source = sources[ind];
                  const logoStyle = getLogoStyle(source);
                  return (
                    <MDBox 
                      component="img" 
                      src={l} 
                      alt="integration-logo" 
                      width="1rem" 
                      mx={0.1} 
                      pl={0.5} 
                      key={ind}
                      sx={logoStyle}
                    />
                  );
                })}
              </MDBox>
            ) : (
              logoImages.map((l, ind) => {
                const source = sources[ind];
                const logoStyle = getLogoStyle(source);
                return (
                  <MDBox 
                    component="img" 
                    src={l} 
                    alt="integration-logo" 
                    width="1rem" 
                    mx={0.1} 
                    pl={0.5} 
                    key={ind}
                    sx={logoStyle}
                  />
                );
              })
            )}
            <MDTypography component="span" fontWeight="regular" ml={0.4} fontSize="13.5px" color="dark" textTransform="capitalize"
              variant="button" sx={metricStyle}>
                {t(title)}
            </MDTypography>
          </MDBox>
        </MDTooltip>
      )
    }

    return (
      <MDTooltip placement="top" title={t(title)} description={t(description)} logos={logoImages}>
        <MDBox style={{borderBottom: "1px dotted #ccc", cursor: "default"}} display="flex" alignItems="center" p={0}>
          {logoImages.map((l, ind) => {
            const source = sources[ind];
            const logoStyle = getLogoStyle(source);
            return (
              <MDBox 
                component="img" 
                src={l} 
                alt="integration-logo" 
                width="1rem" 
                mx={0.1} 
                key={ind}
                sx={logoStyle}
              />
            );
          })}
          <MDTypography component="span" fontWeight="regular" ml={0.4} fontSize="13.5px" color="dark" textTransform="capitalize"
            variant="button" sx={metricStyle}>
              {t(title)}
          </MDTypography>
        </MDBox>
      </MDTooltip>
    )
}

export default MetricTitle;