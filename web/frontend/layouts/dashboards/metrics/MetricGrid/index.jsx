// @mui material components
import Grid from "@mui/material/Grid";
import Card from "@mui/material/Card";
import {Link} from "react-router-dom";
// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDBadge from "@/components/MDBadge";
import MDButton from "@/components/MDButton";
import React, {useState, useEffect} from "react";
import ComplexStatisticsCard from "@/examples/Cards/StatisticsCards/ComplexStatisticsCard";
import numeral from 'numeral';
// @mui material components
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import Skeleton from "@mui/material/Skeleton";
import PillBar from '@/components/PillBar';
import SignalCellularAltIcon from '@mui/icons-material/SignalCellularAlt';
import Divider from '@mui/material/Divider';
import DefaultStatisticsCard from "@/examples/Cards/StatisticsCards/DefaultStatisticsCard";

// Material Dashboard 2 PRO React examples
import Chart from "@/layouts/pages/widgets/components/Chart";
import Icon from "@mui/material/Icon";
import { useTranslation } from "react-i18next";
import {tracker, useMaterialUIController} from "@/context";
import MDTooltip from "@/components/MDTooltip";
import FeatureBlurBox from "@/components/FeatureBlurBox";
import MetricTitle from "@/layouts/dashboards/metrics/MetricTitle";
import {getMetricFormatterFn} from '@/util.jsx';
import { ArrowDownRightIcon, ArrowUpRightIcon } from "@/examples/Icons";

// will be green if metric goes down, red if it goes up
const reverse_color_metrics = ["blended:spend", "blended:cac", "blended:cpc"]

const AccountMetricHighlightCard = ({ metric, loading, metricData, chartToggle }) => {
    const { accessor } = metric;
    const {currency = "", current = [], previous = [], currentAgg = {}, previousAgg = {}} = metricData;

    const {t} = useTranslation();
    let mTitle = (<MetricTitle metric={metric.id} />)
    let formatter = getMetricFormatterFn(metric.format_type ?? "")

    let count_display = <Skeleton variant="text" width={100} />
    let count_display_prev = ""
    let diffLabel = <Skeleton variant="text" width={50} />
    let vsLabel = ""

    if (!loading) {
      let total = currentAgg[accessor] ?? 0
      let totalPrev = previousAgg[accessor] ?? 0
      let val_diff = totalPrev > 0 ? (100 * (total - totalPrev) / totalPrev) : 0

      count_display = (<MDTooltip placement="bottom" title={numeral(total).format('0,0.[00]')}>
                <MDBox component="span">{formatter(total, currency)}</MDBox>
        </MDTooltip>)
      count_display_prev = (<MDTooltip placement="bottom" title={numeral(totalPrev).format('0,0.[00]')}>
            <MDBox component="span">{formatter(totalPrev, currency)}</MDBox>
        </MDTooltip>)

      let icon = val_diff > 0 ? <ArrowUpRightIcon  /> : <ArrowDownRightIcon />
      let diff_percent = numeral(val_diff).format('0.0a') + '%'

      let color = val_diff > 0 ? "success" : "error"
      if (reverse_color_metrics.includes(metric.id)) {
        color = val_diff > 0 ? "error" : "success"
      }

        vsLabel = <MDTypography variant="caption" fontWeight="regular" color="dark">
            {totalPrev > 0
                ? <div>{chartToggle ? "vs" : "was"} {count_display_prev}</div>
                : <><>{"-"}</> <MDTooltip title={t("no-data-time-period")}><InfoOutlinedIcon>{"-"}</InfoOutlinedIcon></MDTooltip></>
            }
        </MDTypography>

        diffLabel = val_diff != 0 ? <MDBadge
            size="md"
            sx={{
              "& .MuiBadge-badge": {
                ml : 0
              }
            }}
            color={color}
            badgeContent={
                <MDBox display="flex" alignItems="center" sx={{fontSize:"11px", fontWeight:"500"}}><>{icon}</> &nbsp; <>{diff_percent}</></MDBox>
            }
            variant="contained" /> : "";
    }

    let chart = React.useMemo(() => {

      let datasetData = current.map((item) => item[accessor])
      let datasetDataPrev = previous.map((item) => item[accessor])
      if (loading) {
        return (
          <>
            <Skeleton variant="rounded" width={"100%"} height={100} />
          </>
        )
      }


      let labels = [
        current.map((item) => item.bucketName),
        previous.map((item) => item.bucketName)
      ];

      return (
        <Chart customHeight="7rem" chart={{
          labels: labels[0].length > labels[1].length ? labels[0] : labels[1],
          customOptions : {
            xGridDisplay : false,
            tooltipTitle: (items) => {
              return ""
            },
            tooltipLabel: (items) => {
                let label = items.dataset.label ?? "";
                let datasetLabels = labels[items.datasetIndex] ?? [];
                let label1 = datasetLabels[items.dataIndex] ?? label;

                return ` ${label1}: ${formatter(items.raw, currency)}`;
            }
          },
          datasets: [
            { label: " Current", color: "dark", data: datasetData},
            { label: " Previous", color: "warning", data: datasetDataPrev, borderDash: [3]}
          ]
        }}/>
      )
    }, [current, previous, currentAgg, previousAgg, loading])

    return (
      <MDBox key={accessor}>
        {!chartToggle ?
          <DefaultStatisticsCard
            key={accessor}
            isEmbedded={true}
            title={mTitle}
            count={count_display}
            diffLabel={diffLabel}
            vsLabel={vsLabel}
          />
        : <ComplexStatisticsCard
            isEmbedded={true}
            key={accessor}
            title={mTitle}
            count={count_display}
            diffLabel={diffLabel}
            vsLabel={vsLabel}
            chart={chart}
          />
        }
      </MDBox>
    )
};


export default function MetricGrid({metricList, title, loading, metricData, reportLink, footerHelp, feature}) {

    const [chartToggle, setChartToggle] = useState(true) // enabled by default
    const {t} = useTranslation();

    let chartToggleOptions = [
      {label: <SignalCellularAltIcon sx={{fontSize:"14px !important"}} />, value: true},
      {label: "#", value: false},
    ];

    return (
      <Card>
        <Grid container spacing={3} p={1.6} direction="row" justifyContent="space-between" alignItems="flex-end">
          {title && <Grid item>
            <MDTypography variant="h5" color="secondary" fontWeight="regular" className="card-title-default">
                {t(title)}
            </MDTypography>
          </Grid>}
          <Grid item>
            <MDBox display="flex" direction="row" alignItems="center">
                <MDTooltip title={chartToggle ? t("hide-chart") : t("show-chart")} placement="top">
                    <MDBox>
                    <PillBar
                        name="type"
                        options={chartToggleOptions}
                        value={chartToggle}
                        onChange={() => setChartToggle(!chartToggle)}
                    />
                    </MDBox>
                </MDTooltip>
            </MDBox>
          </Grid>
        </Grid>
        <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />
        <FeatureBlurBox feature={feature} >
        <Grid container spacing={1.2} p={1.6} pb={2.4}>
            {metricList.map((metric) => {
                return (
                    <Grid item xs={12} md={6} lg={3} key={metric.accessor}>
                        <AccountMetricHighlightCard
                          metric={metric}
                          metricData={metricData} 
                          chartToggle={chartToggle}
                          loading={loading}
                        />
                    </Grid>
                )
            })}
        </Grid>
        </FeatureBlurBox>
        {(reportLink || footerHelp) && <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />}
        {(reportLink || footerHelp) && <MDBox p={1.6} pt={1} display="flex" direction="row" justifyContent="space-between" alignItems="center" sx={{width :"100%"}}>
            {!!footerHelp && <MDBox>{footerHelp}</MDBox>}
            {!!reportLink && <MDButton variant="outlined" color={"dark"} component={Link} to={reportLink} size="small" onClick={() => tracker.mixpanel.track("FullReport Viewed", {reportLink: reportLink})} >
                <MDTypography variant={"button"} color={"dark"} fontWeight="regular" verticalAlign="middle" alignItems="center">
                    {t("deepdive-btn")} &nbsp;
                    <Icon fontSize="default" className={"mdi mdi-arrow-right"} />
                </MDTypography>
            </MDButton>}
        </MDBox>}
      </Card>
    )
}