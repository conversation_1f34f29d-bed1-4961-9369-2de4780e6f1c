import React from "react";
import {tracker, useMaterialUIController} from "@/context";
// @mui material components
import Divider from "@mui/material/Divider";
import PillBar from '@/components/PillBar';
import Card from "@mui/material/Card";
import Icon from "@mui/material/Icon";
// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import { useTranslation } from 'react-i18next';
import MDProgress from "@/components/MDProgress";
import MDTooltip from "@/components/MDTooltip";

function Sidenav({loading, segmentSelection, periodSelection, setHoverSegment}) {
    const [controller] = useMaterialUIController();
    const { miniSidenav, loginConfig, darkMode, shopConfig } = controller;
    const { t } = useTranslation();

    let showSubscriptionBanner = false && shopConfig.subscription_enabled
    && shopConfig.planDetails?.planType == "free";

    const renderSidenavItems = segmentSelection.options.map(({ segment, icon, title, color, percent, description}, key) => {
    const itemKey = `item-${key}`;

    return (
      <MDBox key={itemKey} component="li" pt={key === 0 ? 0 : 1}>
        <MDTooltip title={t(description)} placement="right">
        <MDBox
          onClick={() => segmentSelection.onChange(segment)}
          onMouseEnter={() => {
            setHoverSegment(segment)
          }}
          onMouseLeave={() => {
            setHoverSegment(null)
          }}
          sx={({
            borders: { borderRadius },
            functions: { pxToRem },
            palette: { light },
            transitions,
          }) => ({
            cursor : "pointer",
            display: "flex",
            alignItems: "center",
            borderRadius: borderRadius.md,
            padding: `${pxToRem(10)} ${pxToRem(10)}`,
            backgroundColor: segmentSelection.segment === segment ? light.main : "transparent",
            transition: transitions.create("background-color", {
              easing: transitions.easing.easeInOut,
              duration: transitions.duration.shorter,
            }),

            "&:hover": {
              backgroundColor: light.main,
            },
          })}
        >
          <MDBox mr={1} lineHeight={1} color={darkMode ? "white" : "dark"} >
            <MDBox
                display="grid"
                justifyContent="center"
                alignItems="center"
                bgColor={color}
                color="white"
                width="2rem"
                height="2rem"
                shadow="xs"
                mr={0.2}
                borderRadius="md"
                variant="gradient"
            >
                <Icon fontSize="small" className={icon} />
            </MDBox>
          </MDBox>
          <MDBox display="flex" flexDirection="row" justifyContent="space-between" width="100%">
            <MDTypography variant="button" fontWeight="regular" textTransform="capitalize" width={"80%"} sx={{lineHeight: 1.8}}>
                <>{t(title)}</>
                <MDProgress color={color} value={loading ? 0 : percent} variant="contained"/>
            </MDTypography>
            <MDBox>
                {!loading && <MDTypography variant="button" fontWeight="medium" color="text">
                    {percent}%
                </MDTypography>}
            </MDBox>
          </MDBox>
        </MDBox>
        </MDTooltip>
      </MDBox>
    );
  });

  return (
    <Card
      sx={{
        borderRadius: ({ borders: { borderRadius } }) => borderRadius.lg,
        position: "sticky",
        top: showSubscriptionBanner ? "4rem" : "1%",
      }}
    >
        {!!periodSelection && (
            <MDBox display="flex" alignItems="center" flexDirection="column" justifyContent="flex-start" p={2}>
                <MDTypography variant="button" sx={{fontSize:"13px"}}>
                    {t("time-period")}
                </MDTypography>
                <PillBar
                    name="period"
                    options={periodSelection.options}
                    value={periodSelection.period}
                    onChange={periodSelection.onChange}
                />
            </MDBox>
        )}
        {!!periodSelection && <Divider sx={{mx: 0}} />}
      <MDBox
        component="ul"
        display="flex"
        flexDirection="column"
        p={1}
        m={0}
        sx={{ listStyle: "none" }}
      >
        {renderSidenavItems}
      </MDBox>
    </Card>
  );
}

export default Sidenav;