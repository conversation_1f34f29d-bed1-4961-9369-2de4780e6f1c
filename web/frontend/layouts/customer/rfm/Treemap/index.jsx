import React from 'react';
import themeColors from "@/assets/theme/base/colors";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import MDTypography from '@/components/MDTypography';
import Card from '@mui/material/Card';
import MDBox from '@/components/MDBox';
import MDTooltip from '@/components/MDTooltip';
import Icon from '@mui/material/Icon';
import Divider from '@mui/material/Divider';
import numeral from 'numeral';
import {getRFMSegments} from "@/layouts/customer/rfm";
import { tracker, useMaterialUIController, useCancellableAxios } from "@/context";
import Grid from "@mui/material/Grid";
import MDButton from "@/components/MDButton";
import { useNavigate } from "react-router-dom";

import {Spin} from "antd";
import Empty from "@/components/EmptyChart";
import CircularProgress from "@mui/material/CircularProgress";
import premiumTag from "@/assets/images/premium-tag.png";
import PillBar from '@/components/PillBar';
import MDProgress from "@/components/MDProgress";

function Sidenav({loading, segmentSelection, setHoverSegment, setExpandSegment}) {
    const [controller] = useMaterialUIController();
    const { miniSidenav, loginConfig, darkMode, shopConfig } = controller;
    const { t } = useTranslation();
    const navigate  = useNavigate()

    const renderSidenavItems = segmentSelection.options.map(({ segment, icon, title, color, percent, description}, key) => {
    const itemKey = `item-${key}`;

    return (
      <MDBox key={itemKey} component="li" pt={key === 0 ? 0 : 1}>
        <MDBox
          onClick={() => {
            navigate(`/customers/rfm?segment=${segment}`)
          }}
          onMouseEnter={() => {
            setHoverSegment(segment)
          }}
          onMouseLeave={() => {
            setHoverSegment(null)
          }}
          sx={({
            borders: { borderRadius },
            functions: { pxToRem },
            palette: { light },
            transitions,
          }) => ({
            display: "flex",
            cursor : "pointer",
            alignItems: "center",
            borderRadius: borderRadius.md,
            padding: `${pxToRem(5)} ${pxToRem(5)}`,
            transition: transitions.create("background-color", {
              easing: transitions.easing.easeInOut,
              duration: transitions.duration.shorter,
            }),

            "&:hover": {
              backgroundColor: light.main,
            },
          })}
        >
          <MDBox mr={1} lineHeight={1} color={darkMode ? "white" : "dark"} >
            <MDBox
                display="grid"
                justifyContent="center"
                alignItems="center"
                bgColor={color}
                color="white"
                width="2rem"
                height="2rem"
                shadow="xs"
                mr={0.2}
                borderRadius="md"
                variant="gradient"
            >
                <Icon fontSize="small" className={icon} />
            </MDBox>
          </MDBox>
          <MDBox display="flex" flexDirection="row" justifyContent="space-between" width="100%">
            <MDTypography variant="button" fontWeight="regular" textTransform="capitalize" width={"80%"} sx={{lineHeight: 1.8}}>
                <>{t(title)}</>
                <MDProgress color={color} value={loading ? 0 : percent} variant="contained"/>
            </MDTypography>
            <MDBox>
                {!loading && <MDTypography variant="button" fontWeight="medium" color="text">
                    {percent}%
                </MDTypography>}
            </MDBox>
          </MDBox>
        </MDBox>
      </MDBox>
    );
  });

  return (
      <MDBox
        component="ul"
        display="flex"
        flexDirection="column"
        width="100%"
        my={1}
        sx={{ listStyle: "none" }}
      >
        {renderSidenavItems}
      </MDBox>
  );
}

const rfmgrid = [

    { fm: 5, r: 5, segment: "champion", display: true, borders:{ left: true, top: true, right: true, bottom: true}},

    { fm: 5, r: 3, segment: "loyal" , borders:{ left: true, top: true}},
    { fm: 5, r: 4, segment: "loyal", display: true, borders:{ top: true, right: true}},
    { fm: 4, r: 3, segment: "loyal" , borders:{ left: true, bottom: true}},
    { fm: 4, r: 4, segment: "loyal" , borders:{ bottom: true}},
    { fm: 4, r: 5, segment: "loyal" , borders:{ top: true, right: true, bottom: true}},

    { fm: 4, r: 1, segment: "sleepers" , borders:{ left: true, top: true}},
    { fm: 4, r: 2, segment: "sleepers", display: true, borders:{ top: true, right: true, bottom: true}},
    { fm: 3, r: 1, segment: "sleepers" , borders:{ left: true, right: true, bottom: true}},

    { fm: 3, r: 2, segment: "need_attention", borders:{ left: true, top: true}},
    { fm: 3, r: 3, segment: "need_attention", display: true, borders:{ top: true, right: true}},
    { fm: 2, r: 2, segment: "need_attention" , borders:{ left: true, bottom: true}},
    { fm: 2, r: 3, segment: "need_attention" , borders:{ right: true, bottom: true}},

    { fm: 3, r: 4, segment: "promising" , borders:{ left: true, top: true}},
    { fm: 3, r: 5, segment: "promising", display: true, borders:{ top: true, right: true}},
    { fm: 2, r: 4, segment: "promising" , borders:{ left: true, bottom: true}},
    { fm: 2, r: 5, segment: "promising", borders:{ right: true, bottom: true}},

    { fm: 1, r: 1, segment: "lost" , borders:{ left: true, bottom: true}},
    { fm: 2, r: 1, segment: "lost" , borders:{ left: true, top: true, right: true}},
    { fm: 1, r: 2, segment: "lost", display: true, borders:{ top: true, right: true, bottom: true}},

    { fm: 5, r: 1, segment: "should_not_loose", borders:{ left: true, top: true, bottom: true}},
    { fm: 5, r: 2, segment: "should_not_loose", display: true, borders:{ top: true, right: true, bottom: true}},

    { fm: 1, r: 3, segment: "cold_leads" , display: true, borders:{ left: true, top: true, right: true, bottom: true}},
    { fm: 1, r: 4, segment: "warm_leads" , display: true, borders:{ left: true, top: true, right: true, bottom: true}},
    { fm: 1, r: 5, segment: "new_customers", display: true, borders:{ top: true, right: true, bottom: true}}
];

export const RfmSegmentMap = ({segments, hoverSegment}) => {

    const {t} = useTranslation();

    // Generate a grid that covers all possible combinations of R and FM values
    const grid = [];
    for (let r = 0; r <= 5; r++) {
      for (let fm = 0; fm <= 5; fm++) {
        const segment = rfmgrid.find(seg => seg.r === r && seg.fm === fm);
        if (segment) {
          grid.push(segment);
        } else {
          grid.push({r, fm, segment: "unknown"});
        }
      }
    }


    return (
        <MDBox display="flex" flexDirection="row" mx="auto" my={2}>
            <MDBox display="flex" flexDirection="column" alignItems="center" justifyContent="center" width="100%">
            <MDBox className="rfm-container" height="420px" width="420px">
                {grid.map((s, index) => {
                    let segment = null
                    if (Array.isArray(segments)) {
                        segment = segments.find(seg => seg.segment === s.segment);
                    }
                    const style = {
                        gridRow: 6 - s.fm,
                        gridColumn: s.r,
                        backgroundColor: (s.segment === "unknown" || !segment ? "#CCC" : segment.color),
                        opacity: !!hoverSegment ?  (hoverSegment === s.segment ? 1 : 0.3) : 1,
                        borderLeft: s.borders && s.borders.left ? "0.5px solid #ccc" : "none",
                        borderRight: s.borders && s.borders.right ? "0.5px solid #ccc" : "none",
                        borderTop: s.borders && s.borders.top ? "0.5px solid #ccc" : "none",
                        borderBottom: s.borders && s.borders.bottom ? "0.5px solid #ccc" : "none",
                    };

                    if (s.segment === "unknown" || !segment) {
                        return (
                            <div
                                key={index}
                                style={{...style, opacity: 1, backgroundColor: "initial"}}
                            >
                            <MDBox
                                width="100%"
                                height="100%"
                                display="flex"
                                p={1.5}
                                alignItems="center"
                                justifyContent="center"
                                flexDirection="column"
                            >
                                <MDTypography variant="h4" color="dark" textAlign="center">
                                    {s.fm > 0 ? `${s.fm}` : (s.r > 0 ? `${s.r}` : "")}
                                </MDTypography>
                            </MDBox>
                            </div>
                        )
                    }



                    let useColor = segment.color
                    if (segment.color && segment.color in themeColors) {
                        useColor = themeColors[segment.color].main + "D9" // for 85% opacity
                    }

                    return (
                        <div
                            key={index}
                            style={style}
                            >
                            <MDTooltip title={<><b>{t(segment.title)}</b><br/>{t(segment.description)}</>} placement="top" arrow>
                            <MDBox bgColor={useColor} width="100%" height="100%" pt={1} pr={1} m={0} display="flex" alignItems="right" justifyContent="flex-start" flexDirection="column">
                                {s.display && <MDTypography variant="h4" color="white" textAlign="right">
                                    {segment.percent > 0 ? `${Math.round(segment.percent)}%`: "0%"}
                                </MDTypography>}
                                {s.display && <MDTypography variant="button" color="white" textAlign="right" fontWeight="medium">
                                    {segment.count > 0 ? numeral(segment.count).format("0,0") : ""}
                                </MDTypography>}
                            </MDBox>
                            </MDTooltip>
                        </div>
                    )
                })}
            </MDBox>
            <MDTypography variant="h6" color="dark" textAlign="center" width="100%" fontWeight="medium">
                {t("r-axis-title")}
            </MDTypography>
            </MDBox>
            <MDBox display="flex" flexDirection="column" alignItems="center" justifyContent="center" style={{textOrientation: "mixed", writingMode: "vertical-lr", transform: "rotate(180deg)" }}>
            <MDTypography component={"span"} variant="h6" color="dark" textAlign="center" fontWeight="medium">
                {t("fm-axis-title")}
            </MDTypography>
            </MDBox>
        </MDBox>
    )
};

export default function RFMTreeMap({reportLink}) {
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, loginConfig} = controller;

    const [period, setPeriod] = React.useState("all_time");
    const [segments, setSegments] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [hoverSegment, setHoverSegment] = React.useState(null);

    const {t} = useTranslation();
    const axiosInstance = useCancellableAxios();

    let periodOptions = [
        {label: t("all-time"), value: "all_time"},
        {label: t("last-1y"), value: "1y"},
    ];

    let handlePeriodChange = (value) => {
        setPeriod(value)
    }

    React.useEffect(() => {
        setLoading(true)
        getRFMSegments(selectedShop, period, axiosInstance).then((data) => {
            setSegments(data)
            setLoading(false)
        })
    }, [selectedShop, period]);

    return (
        <Card>
        <Grid container spacing={3} p={1.6} direction="row" justifyContent="space-between" alignItems="flex-end">
          <Grid item>
            <MDTypography variant="h5" color="secondary" fontWeight="regular" className="card-title-default" display="flex" alignItems="center">
                {t("rfm-summary")} <MDBox component="img" src={premiumTag} alt="Brand" width="1rem" ml={0.5} />
            </MDTypography>
          </Grid>
            <Grid item>
            <MDBox display="flex" direction="row" alignItems="center">
                <MDTypography variant="button" textTransform="capitalize" sx={{fontSize:"13px"}} mr={1}>
                    {t("time-period")}
                </MDTypography>
                <PillBar
                    name="period"
                    options={periodOptions}
                    value={period}
                    onChange={handlePeriodChange}
                />
            </MDBox>
            </Grid>
        </Grid>
        <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />
        <Grid container spacing={1.2} p={1.6} pb={2.4}>
            <Grid item xs={12} md={12} lg={12}>
            <Spin indicator={<CircularProgress color="secondary" />} spinning={loading}>
                {segments.length == 0 && <Empty/>}
                {segments.length != 0 &&
                    <MDBox display="flex" flexDirection="row"  mb={1} ml={2} mt={2}>
                        <MDBox width="35%">
                            <Sidenav segmentSelection={{options: segments}} loading={loading} setHoverSegment={setHoverSegment} />
                        </MDBox>
                        <MDBox display="flex">
                            <Divider orientation="vertical" color="dark" />
                        </MDBox>
                        <RfmSegmentMap segments={segments} hoverSegment={hoverSegment} />
                    </MDBox>
                }
            </Spin>
            </Grid>
        </Grid>
        <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />
        <MDBox p={1.6} pt={1} display="flex" direction="row" justifyContent="end" sx={{width :"100%"}}>
                            <MDButton variant="outlined" color={"dark"} component={Link} to={"/customers/rfm"} size="small" onClick={() => tracker.mixpanel.track("FullReport Viewed", {reportLink: reportLink})} >
                <MDTypography variant={"button"} color={"dark"} fontWeight="regular" verticalAlign="middle" alignItems="center">
                    {t("deepdive-btn")} &nbsp;
                    <Icon fontSize="default" className={"mdi mdi-arrow-right"} />
                </MDTypography>
            </MDButton>
        </MDBox>
        </Card>
    )
}
