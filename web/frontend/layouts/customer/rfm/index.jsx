import React, {useState, useEffect} from "react";
import {tracker, useMaterialUIController} from "@/context";
import {useNavigate} from "react-router-dom";


// Material Dashboard 2 PRO React examples
import DashboardLayout from "@/examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "@/examples/Navbars/DashboardNavbar";
import Footer from "@/examples/Footer";
import { useSearchParams } from 'react-router-dom'
import DashboardFeatureBlock from "@/examples/DashboardFeatureBlock";

// @mui material components
import Grid from "@mui/material/Grid";
import Card from "@mui/material/Card";
import Icon from "@mui/material/Icon";
import CircularProgress from "@mui/material/CircularProgress";
import dayjs from "dayjs";
import ReviewBar from "@/examples/ReviewBar";
import MDExport from "@/components/MDExport";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDButton from "@/components/MDButton";
import MDTypography from "@/components/MDTypography";
import DataTable from "@/examples/Tables/DataTable";
import Sidenav from "@/layouts/customer/rfm/components/Sidenav";
import { useCancellableAxios, fetchIntegrations } from "@/context";
import { toast } from "react-toastify";
import {RfmSegmentMap} from "@/layouts/customer/rfm/Treemap";
import Switch from "@mui/material/Switch";


import AppBar from "@mui/material/AppBar";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";

import NiceModal from '@ebay/nice-modal-react';
import PaywallDialog from '@/components/Paywall';
import premiumTag from "@/assets/images/premium-tag.png";
import { useTranslation } from 'react-i18next';
import { useLocation } from "react-router-dom";
import MDAvatar from "@/components/MDAvatar";
import klaviyoLogoIcon from '@/assets/images/logos/klaviyo-logo.svg';
import klaviyoLogo from '@/assets/images/logos/klaviyo.png';
import {KlaviyoJobsDialog, KlaviyoConnectDialog} from '@/layouts/customer/rfm/components/Sync';
import SelectedSegment from "@/layouts/customer/rfm/components/SelectedSegment";
import axios from "axios";
import { SOURCE_KLAVIYO } from "@/layouts/dashboards/metrics/metadata";

let defaultSegments = {
    champion : {
        title : "champion-title",
        segment : "champion",
        description : "champion-desc",
        tooltip : "champion-tooltip",
        color : "primary",
        icon : "mdi mdi-crown",
    },
    loyal : {
        title : "loyal-title",
        segment : "loyal",
        description : "loyal-desc",
        tooltip: "loyal-tooltip",
        color : "info",
        icon : "mdi mdi-account-heart",
    },
    promising : {
        title : "promising-title",
        segment : "promising",
        description : "promising-desc",
        tooltip: "promising-tooltip",
        color : "success",
        icon : "mdi mdi-account-star",
    },
    need_attention : {
        title : "need_attention-title",
        segment : "need_attention",
        description : "need_attention-description",
        tooltip : "need_attention-tooltip",
        color : "error",
        icon : "mdi mdi-account-alert",
    },
    should_not_loose : {
        title : "should_not_loose-title",
        segment : "should_not_loose",
        description : "should_not_loose-description",
        tooltip : "should_not_loose-tooltip",
        color : "warning",
        icon : "mdi mdi-account-clock-outline",
    },
    sleepers : {
        title : "sleepers-title",
        segment : "sleepers",
        description : "sleepers-description",
        tooltip : "sleepers-tooltip",
        color : "secondary",
        icon : "mdi mdi-sleep",
    },
    lost : {
        title: "lost-title",
        segment : "lost",
        description: "lost-description",
        tooltip: "lost-tooltip",
        color : "dark",
        icon : "mdi mdi-account-off",
    },
    new_customers : {
        title : "new-cust",
        segment : "new_customers",
        description : "new_customers-desc",
        tooltip : "new_customers-tooltip",
        color : "success",
        icon : "mdi mdi-account-plus",
    },
    warm_leads : {
        title : "warm_leads-title",
        segment : "warm_leads",
        description : "warm_leads-description",
        tooltip : "warm_leads-tooltip",
        color : "warning",
        icon : "mdi mdi-account-arrow-up",
    },
    cold_leads : {
        title: "cold_leads-title",
        segment : "cold_leads",
        description: "cold_leads-description",
        tooltip: "cold_leads-tooltip",
        color : "secondary",
        icon : "mdi mdi-account-badge",
    }
    // abandoned_checkouts : {
    //     title : "abandoned_checkouts-title",
    //     segment : "abandoned_checkouts",
    //     description : "abandoned_checkouts-description",
    //     tooltip : "abandoned_checkouts-tooltip",
    //     color : "secondary",
    //     icon : "mdi mdi-cart-off",
    // },
    // callback_requests : {
    //     title : "callback_requests-title",
    //     segment : "callback_requests",
    //     description : "callback_requests-description",
    //     tooltip : "callback_requests-tooltip",
    //     color : "secondary",
    //     icon : "mdi mdi-phone-incoming",
    // }
}

let defaultTableData = {
    columns: [
        { Header: "name", accessor: "name" },
        { Header: "email", accessor: "email" },
        { Header: "rfm-segment", accessor: "rfmSegment" },
        { Header: "rfm-score", accessor: "rfmScore" },
        { Header: "Spend", accessor: "totalSpend" },
        { Header: "last-order-on", accessor: "lastOrderCreatedAt" },
        { Header: "total-orders", accessor: "totalOrders" },
        { Header: "phone", accessor: "phone" },
    ],
    rows : []
}

export var getRFMSegments = async function (selectedShop, period, axiosInstance) {
    let result = {}
    let totalCustomers = 0

    try {
      const response = await axiosInstance.post('/api/getcubedata', {
        selectedShop: selectedShop,
        type: "rfm_segment_wise_count",
        query_data: { period: period }
      });
      let allSegments = response.data?.data || [];

      for (var k in allSegments) {
        let oneSegment = allSegments[k]
        if (!(oneSegment['RfmSegmentsAgg.rfmSegment'] in result)) {
          result[oneSegment['RfmSegmentsAgg.rfmSegment']] = {
            'customer_count': 0,
            'percentage': 0,
          }
        }
        result[oneSegment['RfmSegmentsAgg.rfmSegment']]['customer_count'] += oneSegment['RfmSegmentsAgg.customerCount']
        // result[oneSegment['RfmSegmentsAgg.rfmSegment']]['percentage'] = Math.max(oneSegment['RfmSegmentsAgg.percentage'], result[oneSegment['RfmSegmentsAgg.rfmSegment']]['percentage'])
        totalCustomers += oneSegment['RfmSegmentsAgg.customerCount']
      }
    } catch (error) {
        console.log(error)
        return Object.values(defaultSegments).map(s => {s.value = '-'; return s})
    }

    let segments = []
    for (var k in defaultSegments) {
        let cust_count = !!result[k] ? result[k]['customer_count'] : 0;
        let percentage = !!result[k] && totalCustomers > 0 ? (Math.round(result[k]['customer_count']/totalCustomers * 1000) / 10) : 0;
        segments.push({...defaultSegments[k], count : cust_count, percent : percentage, total: totalCustomers})
    }

    return segments
}


var getRFMSegmentTable = async function (selectedShop, selectedSegment, period, axiosInstance) {
    if (selectedSegment != "champion") { // default one is champion
        tracker.mixpanel.track("Data Requested", {report: "rfm", segment: selectedSegment, period});
    }

    try {
      let tableResult = await axiosInstance.post('/api/getcubedata', {
        selectedShop: selectedShop,
        type: "rfm_segments_table",
        query_data: { selectedSegment: selectedSegment, period: period }
      });
      return tableResult.data?.data || [];
    } catch (error) {
        console.log(error)
        return []
    }
}


function getTableData(data, t) {

    let tableData = JSON.parse(JSON.stringify(defaultTableData));

    if (!data || data.length == 0) {
        return tableData
    }

    for (var k in data) {
        let oneRow = data[k]
        tableData.rows.push({
            customerId : oneRow["RfmSegments.customerId"],
            name : oneRow["RfmSegments.customerFirstName"] + " " + oneRow["RfmSegments.customerLastName"],
            email : oneRow["RfmSegments.customerEmail"],
            phone : oneRow["RfmSegments.customerPhone"],
            rfmScore : oneRow["RfmSegments.rfmScore"],
            totalSpend : oneRow["RfmSegments.totalSpend"],
            lastOrderCreatedAt : dayjs(oneRow["RfmSegments.lastOrderCreatedAt"]).format("MMM D, YYYY"),
            totalOrders : oneRow["RfmSegments.totalOrders"],
            rfmSegment : (oneRow["RfmSegments.rfmSegment"] in defaultSegments) ? t(defaultSegments[oneRow["RfmSegments.rfmSegment"]].title) : oneRow["RfmSegments.rfmSegment"],
        })
    }

    return tableData
}


function RFMIntroCard () {

    const { t } = useTranslation();
    const location = useLocation();
    const { pathname } = location;

    return (
        <MDBox mt={1} mb={1} >
            <Grid container spacing={3} justifyContent="center" alignItems={"center"}>
                <Grid item xs={11} mx="auto">
                    <MDBox py={3}>
                    <MDBox display="flex" alignItems="center">
                        <MDBox lineHeight={1}>
                            <MDTypography variant="h6" fontWeight="medium" mb={1} className="card-title-default">
                                {t("rfm-analysis")}
                            </MDTypography>
                            <MDTypography variant="button" fontWeight="regular" mb={2}>
                                <span dangerouslySetInnerHTML={{__html: t("rfm-intro-desc")}} />
                            </MDTypography>
                            <MDButton
                                variant="text"
                                color="secondary"
                                size="small"
                                onClick={() => {
                                    tracker.mixpanel.track("View Article", {report: "rfm"}); 
                                }}
                                target="_blank"
                                component="a"
                                href={"http://help.datadrew.io/en/articles/8340868-rfm-analysis"}
                                rel="noreferrer"
                                >
                                {t("learn-more")}
                                &nbsp; <Icon>open_in_new</Icon>
                            </MDButton>
                            {/*<Link to="/book-call">
                            <MDButton
                                variant="outlined"
                                color="dark"
                                size="small"
                                onClick={() => {tracker.event("Clicked BookCall", {page: pathname, source: "rfm"})}}
                                target="_blank"
                                >
                                <Icon>event_available</Icon>&nbsp;
                                {t("book-call")}
                            </MDButton>
                            </Link> */}
                        </MDBox>
                    </MDBox>
                    </MDBox>
                </Grid>
            </Grid>
            </MDBox>
    )
}

function KlaviyoWeeklySync({klaviyoIntg, integrationLoader, handleKlaviyoConnect}) {

    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, shopConfig} = controller;

    let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;
    let isSubscriptionInActive = isSubscriptionEnabled && !(shopConfig.planDetails?.features?.rfm_segments_tagging ?? false);
    const {t} = useTranslation();

    const [klaviyoJob, setKlaviyoJob] = useState({});
    const [klaviyoJobLoader, setKlaviyoJobLoader] = useState(false);
    const [klaviyoWeeklyRunLoader, setKlaviyoWeeklyRunLoader] = useState(false);
    const [syncSubmitted, setSyncSubmitted] = useState(false);
    const [showSyncNow, setShowSyncNow] = useState(false);

    const axiosInstance = useCancellableAxios();

    const fetchKlaviyoJobs = () => {
        let reqData = {
            job_type: "klaviyo_weekly_sync"
        };
        if (!!selectedShop) {
            reqData.selectedShop = selectedShop;
        }
    
        setKlaviyoJobLoader(true);
        axiosInstance.post('/api/automation/jobs', reqData)
            .then((response) => {
                if (Array.isArray(response.data) && response.data.length > 0) {
                    setKlaviyoJob(response.data[0]);
                } else {
                    setKlaviyoJob({});
                }
                setKlaviyoJobLoader(false);
            })
            .catch((error) => {
                console.log(error)
                setKlaviyoJobLoader(false);
        })
    }

    useEffect(() => {
        fetchKlaviyoJobs();
    }, [selectedShop]);

    const handleKlaviyoSyncChange = (e) => {
        let reqData = {
            job_type: "klaviyo_weekly_sync",
            enabled: e.target.checked,
            request_id: klaviyoIntg.request_id
        }

        if (!!klaviyoJob && !!klaviyoJob.job_id) {
            reqData.job_id = klaviyoJob.job_id;
        }

        if (!!selectedShop) {
            reqData.selectedShop = selectedShop;
        }

        setKlaviyoJobLoader(true);
        axiosInstance.post('/api/automation/klaviyo/sync-weekly', reqData)
            .then((response) => {
                setKlaviyoJobLoader(false);
                if (response.data.error) {
                    toast.error(response.data.error);
                    return;
                }

                if (!klaviyoJob || !klaviyoJob.job_id) {
                    setShowSyncNow(true);
                }

                toast.success(t("saved-successfully"));
                fetchKlaviyoJobs();
            })
            .catch((error) => {
                !axios.isCancel(error) && toast.error(t("something-went-wrong"));
                console.log(error)
                setKlaviyoJobLoader(false);
        })
    }

    const runKlaviyoWeeklySync = () => {
        if (klaviyoWeeklyRunLoader) {
            return 
        }

        if (!klaviyoJob.job_id) {
            toast.error(t("something-went-wrong"));
            return 
        }

        let reqData = {
            job_id: klaviyoJob.job_id
        }

        if (!!selectedShop) {
            reqData.selectedShop = selectedShop;
        }

        setKlaviyoWeeklyRunLoader(true);
        axiosInstance.post('/api/automation/klaviyo/sync-weekly/run', reqData)
            .then((response) => {
                setKlaviyoWeeklyRunLoader(false);
                if (response.data.error) {
                    toast.error(response.data.error);
                    return;
                }
                setSyncSubmitted(true);
            })
            .catch((error) => {
                !axios.isCancel(error) && toast.error(t("something-went-wrong"));
                console.log(error)
                setKlaviyoWeeklyRunLoader(false);
        })
    }

    return (
        <Card>
            <MDBox
                display="flex"
                justifyContent="space-between"
                alignItems={{ xs: "flex-start", sm: "center" }}
                flexDirection={{ xs: "column", sm: "row" }}
                p={2}
                >
                <MDBox display="flex" alignItems="center">
                    <MDAvatar size="sm" src={klaviyoLogoIcon} alt="klaviyo logo" variant="rounded" />
                    <MDBox ml={2} lineHeight={0}>
                    <MDTypography variant="h5" fontWeight="medium" textTransform="capitalize" className="card-title-default">
                        {t("tag-klaviyo")}
                    </MDTypography>
                    <MDTypography variant="button" color="secondary" fontWeight="regular">
                        {t("tag-klaviyo-description")}
                    </MDTypography>
                    </MDBox>
                </MDBox>
                <MDBox
                    display="flex"
                    justifyContent="flex-end"
                    alignItems="center"
                    width={{ xs: "100%", sm: "auto" }}
                    mt={{ xs: 1, sm: 0 }}
                >
                    <MDBox lineHeight={0} ml={2}>
                    {integrationLoader && <CircularProgress size={20} color="dark" />}
                    {!integrationLoader && !klaviyoIntg.request_id && <MDButton variant="outlined" color={"dark"} size="small" onClick={handleKlaviyoConnect}>
                        {t("connect")}
                        {isSubscriptionInActive && <MDBox component="img" src={premiumTag} alt="Brand" width="1rem" ml={0.8} />}
                    </MDButton>}
                    {!integrationLoader && !!klaviyoIntg.request_id && <MDTypography color="secondary" variant="button"  sx={{fontSize:"13px"}} size="small" fontWeight="regular">
                        {t("connected")}
                    </MDTypography>}
                    </MDBox>
                </MDBox>
            </MDBox>
            {!integrationLoader && !!klaviyoIntg.request_id && 
            <MDBox bgColor={"grey-100"}
                borderRadius="lg"
                m={2} p={2}>
                <MDBox
                    display="flex"
                    justifyContent="space-between"
                    alignItems="center"
                >
                    <MDTypography variant="button" color="secondary" fontWeight="regular">
                        {t("tag-all-segments")}
                    </MDTypography>
                    {klaviyoJobLoader && <CircularProgress size={20} color="dark" />}
                    {!klaviyoJobLoader && <Switch
                        checked={klaviyoJob.active_flag ?? false}
                        onChange={handleKlaviyoSyncChange}
                    />}
                </MDBox>
                {!klaviyoJobLoader && klaviyoJob.job_id && 
                    <MDTypography variant="button" color="text" fontWeight="medium">
                        {t("update-schedule")}
                    </MDTypography>
                }
                {!klaviyoJobLoader && klaviyoJob.job_id && <MDBox
                    mt={1}
                >
                    <MDBox display="flex" flexDirection="column" mb={1}>
                        <MDTypography variant="button" color="text" fontWeight="regular">
                            {t("last-update")}: {klaviyoJob.last_run_at ? dayjs(klaviyoJob.last_run_at).format("MMM D, YYYY") : "-"}
                        </MDTypography>
                        <MDTypography variant="button" color="text" fontWeight="regular">
                            {t("next-update")}: {klaviyoJob.next_run_at ? dayjs(klaviyoJob.next_run_at).format("MMM D, YYYY") : "-"}
                        </MDTypography>
                    </MDBox>
                    {showSyncNow && !syncSubmitted && <MDButton variant="outlined" color="dark" size="small" onClick={runKlaviyoWeeklySync}>
                        {klaviyoWeeklyRunLoader 
                            ? <CircularProgress size={20} color="inherit" />
                            : t("update-now")}
                    </MDButton>}
                    {showSyncNow && syncSubmitted && <MDTypography variant="button" color="text" fontWeight="regular">
                        {t("sync-submitted")}
                    </MDTypography>}
                </MDBox>}
            </MDBox>}
        </Card>
    )
}


function RFMSummary({hoverSegment, segments, handleKlaviyoConnect, klaviyoIntg, integrationLoader}) {
    const {t} = useTranslation();
    const navigate = useNavigate();

    const memoizedKlaviyoWeeklySync = React.useMemo(() => {
        return (
            <KlaviyoWeeklySync
                klaviyoIntg={klaviyoIntg}
                integrationLoader={integrationLoader}
                handleKlaviyoConnect={handleKlaviyoConnect}
            />
        );

    }, [klaviyoIntg, integrationLoader]); // Dependencies array

    const handleNavigate = () => {
        tracker.mixpanel.track("Clicked RFM Benchmarks")
        const stateToPass = { sectionKey: 'section-rfm-benchmarks' };
        navigate('/benchmarks', { state: stateToPass });
    };


    return (
        <MDBox mb={3}>
            <Card>
                <RFMIntroCard />
                <MDBox my={2}></MDBox>
                <RfmSegmentMap segments={segments} hoverSegment={hoverSegment} />
                <MDBox my={2}></MDBox>
            </Card>
            <MDBox mt={3}></MDBox>
            {memoizedKlaviyoWeeklySync}
            <MDBox mt={3}></MDBox>
            <Card>
            <MDBox
                display="flex"
                justifyContent="space-between"
                alignItems={{ xs: "flex-start", sm: "center" }}
                flexDirection={{ xs: "column", sm: "row" }}
                p={2}
                >
                <MDBox display="flex" alignItems="center">
                    <MDAvatar size="sm" shadow="lg" variant="rounded" color="dark">
                        <MDBox><Icon color="white" fontSize="small">leaderboard</Icon></MDBox>
                    </MDAvatar>
                    <MDBox ml={2} lineHeight={0}>
                    <MDTypography variant="h5" fontWeight="medium" textTransform="capitalize" className="card-title-default">
                        {t("rfm-benchmarks")}
                    </MDTypography>
                    <MDTypography variant="button" color="secondary" fontWeight="regular">
                        {t("rfm-benchmarks-desc")}
                    </MDTypography>
                    </MDBox>
                </MDBox>
                <MDBox
                    display="flex"
                    justifyContent="flex-end"
                    alignItems="center"
                    width={{ xs: "100%", sm: "auto" }}
                    mt={{ xs: 1, sm: 0 }}
                >
                    <MDBox lineHeight={0} ml={2}>
                        <MDButton color="secondary" variant="outlined" onClick={handleNavigate} sx={{px:0}}>
                            <Icon fontSize="medium">arrow_outward</Icon>&nbsp;
                        </MDButton>
                    </MDBox>
                </MDBox>
            </MDBox>
            </Card>
        </MDBox>
    )
}


function RFMSegmentDetails({segments, period, selectedSegment, loading, handleKlaviyoConnect, handleKlaviyoTag, klaviyoIntg, integrationLoader}) {

    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, loginConfig, shopConfig} = controller;
    const axiosInstance = useCancellableAxios();
    const tableRef = React.useRef(null)

    const [tableData, setTableData] = React.useState(defaultTableData);
    const [tableLoader, setTableLoader] = React.useState(false)

    let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;
    let isSubscriptionInActive = isSubscriptionEnabled && !(shopConfig.planDetails?.features?.rfm_segments ?? false);
    const {t} = useTranslation();

    let tableSegment = {}
    for (var k in segments) {
        let oneSegment = segments[k]
        if (oneSegment.segment == selectedSegment) {
            oneSegment.selected = true
            tableSegment = oneSegment
        } else {
            oneSegment.selected = false
        }
    }

    React.useEffect(() => {
        setTableLoader(true)
        getRFMSegmentTable(selectedShop, selectedSegment, period, axiosInstance)
            .then((data) => {
                setTableLoader(false)
                setTableData(getTableData(data, t))
            }).catch(err => {
                console.log(err)
            })
    }, [selectedShop, selectedSegment, period]);

    if (tableData && tableData.columns) {
        tableData.columns = tableData.columns.map(c => {
            c.Header = t(c.Header)
            return c
        })
    }


    let handleKlaviyo  = !!klaviyoIntg.request_id ? handleKlaviyoTag : handleKlaviyoConnect

    return (
        <>
            <MDBox mb={3}>
                <SelectedSegment
                    embedded={false}
                    loading={loading}
                    segment={tableSegment}
                />
            </MDBox>
            <MDBox mb={3}>
                <Card ref={tableRef}>
                    {(tableLoader || !tableData || tableData.length == 0) ?
                    // circular loader for table section
                        <MDBox display="flex" justifyContent="center" height={300} alignItems="center">
                            <CircularProgress color="dark" />
                        </MDBox>
                        : (!isSubscriptionInActive ? 
                            (
                                <MDBox>
                                <MDBox p={2} lineHeight={1} display="flex" flexDirection="row" alignItems="center" justifyContent="space-between">
                                    <MDTypography variant="h5" fontWeight="regular" color="secondary" className="card-title-default">
                                        {t("section-rfm-customers")}
                                    </MDTypography>
                                    <MDBox display="flex"  flexDirection="row" alignItems="center" justifyContent="space-between">
                                        <MDBox ml={1}>
                                            {!integrationLoader && <MDButton variant="outlined" color={"dark"} size="small" onClick={handleKlaviyo}>
                                                <MDBox component="img" src={klaviyoLogo} alt="klaviyoLogo" width="1.2rem" mr={0.4}  />
                                                &nbsp;{klaviyoIntg.request_id ? t("tag-segment", {segment : t(tableSegment.title)}) : t("configure-klaviyo-title")}
                                                {isSubscriptionInActive && <MDBox component="img" src={premiumTag} alt="Brand" width="1rem" ml={0.8} />}
                                            </MDButton>}
                                            {integrationLoader && <CircularProgress size={20} color="dark" />}
                                        </MDBox>
                                        <MDBox ml={1}>
                                            <MDExport
                                                feature="rfm_segments_export"
                                                disabled={!(loginConfig.allow_customer_export ?? false)}
                                                formats={["xlsx", "csv"]}
                                                options={{
                                                    type: "rfm_segments_export",
                                                    fileName: `${tableSegment.segment}_customers_rfm`,
                                                    parameters: {
                                                        period: period,
                                                        segment: selectedSegment,
                                                    },
                                                }}
                                            />
                                        </MDBox>
                                    </MDBox>
                                </MDBox>
                                <DataTable
                                    isBasic={false}
                                    table={tableData}
                                    showTotalEntries={false}
                                    entriesPerPage={false}
                                    canSearch={false}
                                    isSorted={true}
                                />
                                </MDBox>
                                )
                            : <MDBox display="flex" justifyContent="center" height={400} alignItems="center">
                                <MDBox display="flex" flexDirection="column" alignItems="center">
                                    <MDBox component="img" src={premiumTag} alt="Brand" width="2rem" mb={1} />
                                    <MDTypography variant="caption" fontWeight="bold" color="text" textTransform="capitalize">
                                        {t("upgrade-rfm")}
                                    </MDTypography>
                                    <MDBox mt={2}>
                                        <MDButton variant="contained" color="info" size="small" onClick={() => {
                                            tracker.event("Paywall", {feature: "rfm_segments"});
                                            NiceModal.show(PaywallDialog, {feature : "rfm_segments"})
                                        }}>
                                            {t("start-trial")}
                                        </MDButton>
                                    </MDBox>
                                </MDBox>
                            </MDBox>)
                    }
                </Card>
            </MDBox>
        </>
    )
}

function CustomersRFMIndex() {
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, shopConfig, integrations} = controller;

    const [searchParams, setSearchParams] = useSearchParams();
    let defaultSegment = searchParams.get('segment')

    const [segments, setSegments] = React.useState(Object.values(defaultSegments));
    const [loading, setLoading] = React.useState(true);

    const [selectedSegment, setSelectedSegment] = React.useState(defaultSegment ?? "champion")
    const [period, setPeriod] = React.useState("all_time")

    const [tabValue, setTabValue] = useState(defaultSegment ? "rfm-segments" : "rfm-summary");
    const [hoverSegment, setHoverSegment] = React.useState(null);

    const integrationLoader = integrations.loading ?? false;

    let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;
    let isRFMSegmentsTaggingInActive = isSubscriptionEnabled && !(shopConfig.planDetails?.features?.rfm_segments_tagging ?? false);

    const {t} = useTranslation();
    const axiosInstance = useCancellableAxios();

    React.useEffect(() => {
        setLoading(true)
        getRFMSegments(selectedShop, period, axiosInstance).then((data) => {
            setSegments(data)
            setLoading(false)
        })
    }, [selectedShop, period]);

    let periodOptions = [
        {label: t("all-time"), value: "all_time"},
        {label: t("last-1y"), value: "1y"},
    ];

    let handlePeriodChange = (value) => {
        setPeriod(value)
    }

    const handleSetTabValue = (event, newValue) => setTabValue(newValue);
    const selectedSegmentOnChange = (segment) => {
        setTabValue("rfm-segments")
        setSelectedSegment(segment)
    }

    let klaviyoIngs = SOURCE_KLAVIYO in integrations ? integrations[SOURCE_KLAVIYO] : [];
    let klaviyoIntg = klaviyoIngs.length > 0 ? klaviyoIngs[0] : {};



    const handleKlaviyoConnect = () => {
        tracker.event("Connect Klaviyo", {
            report: "rfm",
            shop : selectedShop
        })

        if (isRFMSegmentsTaggingInActive) {
            tracker.event("Paywall", {feature: "rfm_segments_tagging"});
            NiceModal.show(PaywallDialog, {feature : "rfm_segments_tagging"})
            return false
        }

        NiceModal.show(KlaviyoConnectDialog, {
            onSuccess : () => {
                NiceModal.hide(KlaviyoConnectDialog);
                fetchIntegrations(dispatch, selectedShop);
            }
        })
        return true
    }

    const handleKlaviyoTag = () => {
        tracker.event("Tag Klaviyo", {
            report: "rfm",
            shop : selectedShop
        })

        if (isRFMSegmentsTaggingInActive) {
            tracker.event("Paywall", {feature: "rfm_segments_tagging"});
            NiceModal.show(PaywallDialog, {feature : "rfm_segments_tagging"})
            return false
        }

        let tableSegment = {}
        for (var k in segments) {
            let oneSegment = segments[k]
            if (oneSegment.segment == selectedSegment) {
                tableSegment = oneSegment
                break;
            }
        }

        NiceModal.show(KlaviyoJobsDialog, {
            integration: klaviyoIntg,
            selectedSegment : tableSegment,
            selectedPeriod : period
        })
        return true
    }

    return (
        <DashboardLayout>
        <DashboardNavbar />
        <DashboardFeatureBlock feature={"rfm_segments"} />
        <Grid container spacing={3} mt={3} mb={3}>
            <Grid item xs={12} lg={3.5} sx={{pt: "0 !important"}}>
                <Sidenav
                    loading={loading}
                    segmentSelection={{
                        segment : selectedSegment,
                        options : segments,
                        onChange : selectedSegmentOnChange
                    }}
                    periodSelection={{
                        period : period,
                        options : periodOptions,
                        onChange : handlePeriodChange
                    }}
                    setHoverSegment={setHoverSegment}
                />
            </Grid>

            <Grid item xs={12} lg={8.5} sx={{pt: "0 !important"}}>
                <MDBox mb={3}> 
                    <Grid container>
                        <Grid item xs={12} sm={8} lg={6}>
                            <AppBar position="static">
                                <Tabs orientation={"horizontal"} value={tabValue} onChange={handleSetTabValue}>
                                    <Tab label={t("rfm-summary")} value="rfm-summary" />
                                    <Tab label={t("segment-details")} value="rfm-segments" />
                                </Tabs>
                            </AppBar>
                        </Grid>
                    </Grid>
                </MDBox>

                {tabValue == "rfm-summary" && <RFMSummary
                    hoverSegment={hoverSegment}
                    segments={segments}

                    handleKlaviyoConnect={handleKlaviyoConnect}
                    integrationLoader={integrationLoader}
                    klaviyoIntg={klaviyoIntg}
                />}

                {tabValue == "rfm-segments" && <RFMSegmentDetails 
                    segments={segments}
                    period={period}
                    selectedSegment={selectedSegment}
                    loading={loading}

                    handleKlaviyoConnect={handleKlaviyoConnect}
                    handleKlaviyoTag={handleKlaviyoTag}
                    integrationLoader={integrationLoader}
                    klaviyoIntg={klaviyoIntg}
                />}
            </Grid>
        </Grid>
        <ReviewBar/>
        <Footer />
        </DashboardLayout>
    );
}


export default CustomersRFMIndex;
