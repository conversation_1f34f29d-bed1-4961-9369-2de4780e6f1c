// @mui material components
import Card from "@mui/material/Card";
// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDButton from "@/components/MDButton";
import MDAvatar from "@/components/MDAvatar";
import { useTranslation } from "react-i18next";
import { useMaterialUIController } from "@/context";
import { useNavigate } from "react-router-dom";
import NiceModal from "@ebay/nice-modal-react";
import logoShopify from "@/assets/images/shopify-logo.svg";
import { AddStoreDialog } from '@/layouts/authentication/add-store';
import IdentificationModal from "../IdentificationModal";

function MultiStoreBanner() {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [controller] = useMaterialUIController();
    const { loginConfig } = controller;

    // Show in both user flow and shop flow
    const isShopFlow = !loginConfig.userData || !loginConfig.userData.email;
    const addNewStore = () => {
        if (isShopFlow) {
            // For shop flow, navigate to signin page to add new store
            NiceModal.show(IdentificationModal, {});
        } else {
            // For user flow, open the AddStoreDialog
            NiceModal.show(AddStoreDialog, {});
        }
    };

    // Only show the banner to owners or admins in the user flow.
    const isOwnerOrAdmin = !isShopFlow && (loginConfig.userData?.workspace_type === 'owner' || loginConfig.userData?.workspace_type === 'admin');
    const shouldShowBanner = isShopFlow || isOwnerOrAdmin;

    if (!shouldShowBanner) {
        return null;
    }

    return (
        <Card id="multi-store-banner">
            <MDBox
                p={2}
                display="flex"
                justifyContent="space-between"
                alignItems={{ xs: "flex-start", sm: "center" }}
                flexDirection={{ xs: "column", sm: "row" }}
            >
                <MDBox display="flex" alignItems="center">
                    <MDAvatar
                        src={logoShopify}
                        alt="shopify logo"
                        variant="rounded"
                        size="sm"
                    />
                    <MDBox ml={2} lineHeight={0}>
                        <MDTypography variant="h6" fontWeight="medium" color="dark">
                            {t("managing-multi-stores")}
                        </MDTypography>
                        <MDTypography variant="button" color="dark">
                            {t("multi-store-view")}
                        </MDTypography>
                    </MDBox>
                </MDBox>
                <MDBox
                    display="flex"
                    justifyContent="flex-end"
                    alignItems="center"
                    width={{ xs: "100%", sm: "auto" }}
                    mt={{ xs: 1, sm: 0 }}
                >
                    <MDBox lineHeight={0} mx={2}>
                        <MDButton
                            variant="outlined"
                            color="dark"
                            size="small"
                            onClick={addNewStore}
                        >
                            {t("add-new-store")}
                        </MDButton>
                    </MDBox>
                </MDBox>
            </MDBox>
        </Card>
    );
}

export default MultiStoreBanner;
