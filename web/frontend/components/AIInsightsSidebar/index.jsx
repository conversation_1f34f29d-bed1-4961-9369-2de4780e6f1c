import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import {
  Card,
  IconButton,
  Chip,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>Field
} from '@mui/material';
import {
  TrendingUp,
  Warning,
  TrackChanges,
  AttachMoney,
  Close,
  Psychology,
  Refresh,
  ExpandMore,
  ExpandLess,
  Add,
  DeleteOutline
} from '@mui/icons-material';
import MDBox from '@/components/MDBox';
import MDTypography from '@/components/MDTypography';
import { sendMessage } from "@/layouts/askprashna/langgraph-api";
import { getRouteStaticDataFromPathname, getRouteKeyFromPathname } from "@/router/constants";
import { FLOW_AUTO_INSIGHTS } from "@/layouts/askprashna/langgraph-api/constants";
import { useMaterialUIController, useCancellableAxios } from "@/context";
import MDButton from '@/components/MDButton';
import { FinalAnswerText } from "@/layouts/askprashna/components/AssistantMessage/index.jsx";

const iconMap = {
  TrendingUp,
  Warning,
  TrackChanges,
  Target: TrackChanges,
  AttachMoney
};

const getSeverityColor = (severity) => {
  switch (severity) {
    case 'high':
      return { bg: '#fef2f2', text: '#dc2626' };
    case 'medium':
      return { bg: '#fffbeb', text: '#d97706' };
    case 'low':
      return { bg: '#f0fdf4', text: '#16a34a' };
    default:
      return { bg: '#f1f5f9', text: '#64748b' };
  }
};

const InsightCard = ({ insight }) => {
  const severityColors = getSeverityColor(insight.severity);
  const IconComponent = iconMap[insight.icon] || iconMap.TrendingUp;

  return (
    <Card
      elevation={0}
      sx={{
        p: 2,
        border: '1px solid #e5e7eb',
        borderRadius: '12px',
        backgroundColor: '#ffffff',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        '&:hover': {
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
          transform: 'translateY(-2px)',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
        },
        transition: 'all 0.2s ease-in-out'
      }}
    >
      <MDBox display="flex" alignItems="flex-start" justifyContent="space-between" mb={2}>
        <MDBox display="flex" alignItems="center" gap={2}>
          <MDBox
            sx={{
              width: 40,
              height: 40,
              borderRadius: '10px',
              background: `linear-gradient(135deg, ${insight.iconColor}15 0%, ${insight.iconColor}25 100%)`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: `1px solid ${insight.iconColor}30`
            }}
          >
            <IconComponent sx={{ color: insight.iconColor, fontSize: 20 }} />
          </MDBox>
          <MDTypography variant="subtitle1" sx={{ flex: 1, color: '#1f2937', fontSize: '16px', fontWeight: '500' }}>
            {insight.title}
          </MDTypography>
        </MDBox>
        <Chip
          label={insight.severity}
          size="small"
          sx={{
            backgroundColor: severityColors.bg,
            color: severityColors.text,
            fontSize: '0.75rem',
            fontWeight: 700,
            textTransform: 'capitalize',
            borderRadius: '8px',
            height: '24px'
          }}
        />
      </MDBox>
      
      <MDTypography variant="body2" color="#6b7280" mb={2} sx={{ lineHeight: 1.6, fontSize: '14px' }}>
        {insight.description}
      </MDTypography>
      
      <MDBox
        sx={{
          p: 1.5,
          mb: 2,
          background: 'linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%)',
          borderRadius: '12px',
          width: 'fit-content',
          // border: '1px solid #d1d5db'
        }}
      >
        <MDTypography variant="body2" fontWeight="700" color="#1f2937" sx={{ fontSize: '14px' }}>
          {insight.metric}
        </MDTypography>
      </MDBox>
      
      <MDBox mb={2}>
        {insight.products.map((product, index) => (
          <MDTypography key={index} variant="body2" sx={{ mb: 0.5, color: '#4b5563', fontSize: '14px' }}>
            • {product}
          </MDTypography>
        ))}
      </MDBox>
    </Card>
  );
};

const AIInsightsSidebar = ({ open, onClose, sx = {}, filters = {} }) => {
  const [insightsType, setInsightsType] = useState('markdown');
  const [insights, setInsights] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState('');
  const [error, setError] = useState('');
  const [servingFromCache, setServingFromCache] = useState(true);
  const [customDescription, setCustomDescription] = useState(null);
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);
  const [recipes, setRecipes] = useState([]);
  const [finalAnswer, setFinalAnswer] = useState(null);

  const addRecipe = () => setRecipes((prev) => [...prev, '']);
  const updateRecipe = (index, value) => {
    setRecipes((prev) => prev.map((r, i) => (i === index ? value : r)));
  };
  const removeRecipe = (index) => {
    setRecipes((prev) => prev.filter((_, i) => i !== index));
  };

  const handleRerun = () => {
    setIsDescriptionExpanded(false);
    fetchInsights(true);
  };
  
  // Access context normally - the provider should be available
  const [controller] = useMaterialUIController();
  const { selectedShop } = controller;
  const axiosInstance = useCancellableAxios();
  const { pathname } = useLocation();

  const componentId = getRouteKeyFromPathname(pathname);
  const routeStaticData = getRouteStaticDataFromPathname(pathname);

  // Handle escape key to close sidebar
  useEffect(() => {
    const handleEscape = (event) => {
      if (event.key === 'Escape' && open) {
        onClose();
      }
    };

    if (open) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [open, onClose]);

  // Populate default custom description from constants when routeKey changes
  useEffect(() => {
    const defaultDescription = routeStaticData?.description ?? '';
    setCustomDescription((prev) => (prev && prev.trim().length > 0 ? prev : defaultDescription));
  }, [pathname]);

  // Fetch insights when sidebar opens
  useEffect(() => {
    if (open && pathname && selectedShop && axiosInstance) {
      fetchInsights();
    }
  }, [open, pathname, selectedShop, axiosInstance]);

  const fetchInsights = async (skipCache = false) => {
    setLoading(true);
    setError('');
    setInsights([]);
    setFinalAnswer('');
    setCurrentStep('');

    try {
      /**
       * 1. Check if there are cached insights; if yes, use them
       * 2. If no cached insights, send request to the assistant
       * 
       * TODO: modularize this logic into separate functions
       */

      if (!skipCache) {
        setCurrentStep("Checking for previously generated AI insights in store");
        const cachedResponse = await axiosInstance.post('/api/agent/get-cached-response', {
          flow: 'auto_insights',
          componentId: componentId,
          filters: filters,
          selectedShop: selectedShop
        });

        if (cachedResponse?.data?.cache_hit) {
          setInsights(cachedResponse?.data?.data?.insights ?? []);
          setFinalAnswer(cachedResponse?.data?.data?.final_answer ?? "");
          setLoading(false);
          setServingFromCache(true);
          return;
        }
      }

      // Use the existing sendMessage function with auto-insights parameters
      setCurrentStep("Requesting Insights from Datadrew AI");
      const stream = await sendMessage({
        threadId: null, // langgraph will create a new thread internally
        messages: [
          {
            type: 'human',
            content: `Generate insights for the attached products`
          }
        ],
        flow: FLOW_AUTO_INSIGHTS,
        flow_data: {
          componentId: componentId,
          componentName: routeStaticData?.name ?? "",
          componentDescription: (customDescription || routeStaticData?.description) ?? "",
          recipePrompts: (recipes || []).map((r) => (r || '').trim()).filter((r) => r.length > 0),
          filters: filters,
          insightsType: insightsType
        }
      }, axiosInstance, selectedShop);

      // Process the stream using the same pattern as existing code
      const reader = stream.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const chunkData = JSON.parse(line.slice(6));
              processStreamChunk(chunkData);
            } catch (parseError) {
              console.error('Error parsing chunk:', parseError);
            }
          }
        }
        setServingFromCache(false);
      }
    } catch (error) {
      console.error('Error fetching insights:', error);
      setError('Failed to load insights');
      setLoading(false);
    }
  };

  // Process stream chunks similar to useLangGraphMessages
  const processStreamChunk = (chunk) => {    
    const {
      current_step,
      insights,
      final_answer,
      progress_update
    } = chunk;

    if (current_step) setCurrentStep(current_step);

    if (insights && insights.length > 0) {
      setInsights(insights);
      setLoading(false);
      return;
    }

    if (final_answer) {
      setFinalAnswer(final_answer);
      setLoading(false);
      return;
    }

    if (progress_update && progress_update.percentage == 100) {
      setLoading(false);
      if (!insights || !insights.length) {
        setError("Insights not available right now. Please try again later.");
      }
      return;
    }

    if (chunk.event === "error") {
      const { message } = chunk.data || {};
      console.error("Error from stream:", message);
      setError(message || 'An error occurred while generating insights');
      setLoading(false);
      setInsights([]);
      return;
    }
  };

  const renderInsights = () => {
    if (insights && insights.length > 0) {
      return (
        <MDBox sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
          {insights.map((insight) => (
            <InsightCard key={insight.id} insight={insight} />
          ))}
        </MDBox>
      );
    }

    if (finalAnswer) {
      try {
        return (
          <MDBox sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
            <FinalAnswerText text={finalAnswer} />
          </MDBox>
        );
      } catch (error) {
        console.error("Error rendering AI Insights markdown:", error);
      }
    }

    return null;
  };

  const renderCustomizationSection = () => (
    <MDBox sx={{
      background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
      borderRadius: '12px',
      border: '1px solid #e5e7eb',
      mb: 2,
      overflow: 'hidden'
    }}>
      {/* Collapsible Header */}
      <MDBox
        sx={{
          p: 1.5,
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.02)'
          },
          transition: 'background-color 0.2s ease'
        }}
        onClick={() => setIsDescriptionExpanded(!isDescriptionExpanded)}
      >
        <MDTypography variant="subtitle1" fontWeight="600" sx={{ color: '#1f2937', fontSize: '14px' }}>
          Customize Description & Recipes
        </MDTypography>
        {isDescriptionExpanded ? <ExpandLess /> : <ExpandMore />}
      </MDBox>

      {/* Collapsible Content */}
      <MDBox sx={{
        maxHeight: isDescriptionExpanded ? '500px' : '0px',
        opacity: isDescriptionExpanded ? 1 : 0,
        overflow: 'hidden',
        transition: 'all 0.5s ease-in-out',
        px: 1.5,
        pb: isDescriptionExpanded ? 1.5 : 0
      }}>
        {/* Insights Type Toggle */}
        <MDBox sx={{ mb: 2 }}>
          <MDTypography variant="subtitle2" fontWeight="700" sx={{ mb: 2, color: '#1f2937', fontSize: '12px' }}>
            📊 Insights Format
          </MDTypography>
          <MDBox sx={{ display: 'flex', gap: 0.5 }}>
            <Button
              variant={insightsType === 'cards' ? 'contained' : 'outlined'}
              size="small"
              onClick={() => setInsightsType('cards')}
              sx={{
                borderRadius: '4px',
                textTransform: 'none',
                fontWeight: 500,
                px: 1.5,
                py: 0.5,
                fontSize: '11px',
                minWidth: '80px',
                height: '24px',
                color: insightsType === 'cards' ? '#ffffff' : '#1f2937',
                borderColor: insightsType === 'cards' ? 'transparent' : '#d1d5db',
                ...(insightsType === 'cards' && {
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)'
                  }
                }),
                '&:hover': {
                  borderColor: insightsType === 'cards' ? 'transparent' : '#9ca3af'
                }
              }}
            >
              Cards
            </Button>
            <Button
              variant={insightsType === 'markdown' ? 'contained' : 'outlined'}
              size="small"
              onClick={() => setInsightsType('markdown')}
              sx={{
                borderRadius: '4px',
                textTransform: 'none',
                fontWeight: 500,
                px: 1.5,
                py: 0.5,
                fontSize: '11px',
                minWidth: '80px',
                height: '24px',
                color: insightsType === 'markdown' ? '#ffffff' : '#1f2937',
                borderColor: insightsType === 'markdown' ? 'transparent' : '#d1d5db',
                ...(insightsType === 'markdown' && {
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)'
                  }
                }),
                '&:hover': {
                  borderColor: insightsType === 'markdown' ? 'transparent' : '#9ca3af'
                }
              }}
            >
              Markdown
            </Button>
          </MDBox>
        </MDBox>

        {/* Divider */}
        <MDBox sx={{ height: '1px', backgroundColor: '#e5e7eb', mb: 2 }} />

        {/* Component Description Section */}
        <MDBox sx={{ mb: 2 }}>
          <MDTypography variant="subtitle2" fontWeight="700" sx={{ mb: 2, color: '#1f2937', fontSize: '12px' }}>
            📝 Component Description
          </MDTypography>
          <TextField
            fullWidth
            multiline
            rows={5}
            placeholder="Enter your custom description for this component..."
            value={customDescription}
            onChange={(e) => setCustomDescription(e.target.value)}
            variant="outlined"
            size="small"
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '8px',
                backgroundColor: '#ffffff',
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#d1d5db'
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#667eea'
                }
              }
            }}
          />
        </MDBox>

        {/* Divider */}
        <MDBox sx={{ height: '1px', backgroundColor: '#e5e7eb', mb: 2 }} />

        {/* Recipes Section */}
        <MDBox sx={{ mb: 2 }}>
          <MDTypography variant="subtitle2" fontWeight="700" sx={{ mb: 2, color: '#1f2937', fontSize: '12px' }}>
            🍳 Recipe Prompts
          </MDTypography>

          {/* Add Recipe Button */}
          <Button
            variant="text"
            startIcon={<Add />}
            onClick={addRecipe}
            sx={{ mb: 2, textTransform: 'none', fontWeight: 600, color: '#667eea' }}
          >
            Add Recipe Description
          </Button>

          {/* Recipe Boxes */}
          <MDBox sx={{ display: 'flex', flexDirection: 'column', gap: 1.5, mb: 2 }}>
            {recipes.map((recipe, index) => (
              <MDBox key={index} sx={{ display: 'flex', gap: 1, alignItems: 'flex-start' }}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  placeholder={`Recipe #${index + 1}`}
                  value={recipe}
                  onChange={(e) => updateRecipe(index, e.target.value)}
                  variant="outlined"
                  size="small"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '8px',
                      backgroundColor: '#ffffff'
                    }
                  }}
                />
                <IconButton
                  onClick={() => removeRecipe(index)}
                  size="small"
                  sx={{ mt: 0.5 }}
                  aria-label={`Remove recipe ${index + 1}`}
                >
                  <DeleteOutline />
                </IconButton>
              </MDBox>
            ))}
          </MDBox>
        </MDBox>

        {/* Generate/Rerun Button */}
        <MDButton
          variant="gradient"
          color="info"
          onClick={handleRerun}
          disabled={loading}
          sx={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: '8px',
            textTransform: 'none',
            fontWeight: 600,
            color: 'white',
            px: 3,
            py: 1.5,
            minWidth: '120px',
            '&:hover': {
              background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
              transform: 'translateY(-1px)',
              boxShadow: '0 4px 12px rgba(102, 126, 234, 0.4)'
            },
            '&:disabled': {
              background: '#9ca3af',
              transform: 'none',
              boxShadow: 'none'
            },
            transition: 'all 0.2s ease-in-out'
          }}
        >
          Re-run Insights
        </MDButton>
      </MDBox>
    </MDBox>
  );

  if (!open) return null;

  return (
    <MDBox
      sx={{
        width: '100%',
        height: '96vh',
        border: '1px solid #e5e7eb',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
        borderRadius: '12px',
        ...sx
      }}
    >
      {/* Header */}
      <MDBox
        sx={{
          p: 1,
          borderBottom: '1px solid #e5e7eb',
          background: 'linear-gradient(135deg,rgb(170, 179, 222) 0%,rgb(193, 176, 210) 100%)',
          color: 'white',
          borderRadius: '12px 12px 0 0'
        }}
      >
        <MDBox display="flex" alignItems="center" justifyContent="space-between" p={1}>
          <MDBox display="flex" alignItems="center" gap={2}>
            <MDBox
              sx={{
                width: 24,
                height: 24,
                borderRadius: '12px',
                background: 'rgba(255, 255, 255, 0.2)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backdropFilter: 'blur(10px)'
              }}
            >
              <Psychology sx={{ color: 'white', fontSize: 24 }} />
            </MDBox>
            <MDBox>
              <MDTypography variant="h6" fontWeight="700" sx={{ color: 'white' }}>
                AI Insights
              </MDTypography>
            </MDBox>
          </MDBox>
          <MDBox display="flex" alignItems="center" gap={1}>
            {servingFromCache && <IconButton 
              onClick={() => fetchInsights(true)} // Pass true to skip cache
              size="small"
              disabled={loading}
              sx={{ 
                color: 'white',
                '&:hover': { 
                  background: 'rgba(255, 255, 255, 0.1)' 
                },
                '&:disabled': {
                  color: 'rgba(255, 255, 255, 0.5)',
                  background: 'rgba(255, 255, 255, 0.05)'
                }
              }}
            >
              <Refresh />
            </IconButton>}
            <IconButton 
              onClick={onClose} 
              size="small"
              sx={{ 
                color: 'white',
                '&:hover': { 
                  background: 'rgba(255, 255, 255, 0.1)' 
                } 
              }}
            >
              <Close />
            </IconButton>
          </MDBox>
        </MDBox>
      </MDBox>

      {/* Content */}
      <MDBox sx={{ 
        flex: 1, 
        overflowY: 'auto',
        background: 'linear-gradient(180deg, #f8fafc 0%, #ffffff 100%)',
        borderRadius: '0 0 0 12px'
      }}>
        <MDBox sx={{ p: 1.5 }}>
          {error && (
            <Alert 
              severity="error" 
              sx={{ 
                mb: 2,
                borderRadius: '12px',
                '& .MuiAlert-icon': { alignItems: 'center' }
              }}
            >
              {error}
            </Alert>
          )}
          
          {renderCustomizationSection()}

          {loading && (
            <MDBox sx={{ 
              textAlign: 'center', 
              py: 12,
              background: 'linear-gradient(90deg, #f8fafc 0%, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%, #f8fafc 100%)',
              backgroundSize: '200% 100%',
              borderRadius: '16px',
              border: '1px solid #e5e7eb',
              animation: 'shimmer 2s ease-in-out infinite',
              '@keyframes shimmer': {
                '0%': { backgroundPosition: '200% 0' },
                '100%': { backgroundPosition: '-200% 0' }
              }
            }}>
              <MDTypography variant="h6" fontWeight="600" sx={{ mb: 1, color: '#1f2937', fontSize: '14px', padding: '0 10px' }}>
                Generating insights
              </MDTypography>
              <MDTypography variant="body2" color="text.secondary" sx={{ fontSize: '14px', padding: '0 10px' }}>
                {currentStep || 'Analyzing your data'}
              </MDTypography>
            </MDBox>
          )}
          
          {!loading && ((insights && insights.length) || finalAnswer) && renderInsights()}
        </MDBox>
      </MDBox>
    </MDBox>
  );
};

export default AIInsightsSidebar;
