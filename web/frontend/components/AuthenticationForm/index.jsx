import React from "react";
import CircularProgress from "@mui/material/CircularProgress";
import Icon from "@mui/material/Icon";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDInput from "@/components/MDInput";
import MDButton from "@/components/MDButton";

/**
 * Reusable authentication form component
 * Can be used in both modal and page contexts
 * 
 * @param {Object} props - Component props
 * @param {Object} props.auth - Authentication state and handlers from useAuthentication hook
 * @param {string} props.emailPlaceholder - Placeholder text for email input
 * @param {boolean} props.showEmailEditIcon - Whether to show the edit email icon
 * @returns {JSX.Element} Authentication form
 */
const AuthenticationForm = ({ 
  auth,
  emailPlaceholder = "<EMAIL>",
  showEmailEditIcon = true
}) => {
  const {
    email,
    password,
    confirmPassword,
    flowType,
    emailSubmitStart,
    passwordSubmitStart,
    setEmail,
    setPassword,
    setConfirmPassword,
    handleEmailSubmit,
    handlePasswordSubmit,
    handleEditEmail,
    isEmailValid,
    isPasswordValid,
    isConfirmPasswordValid
  } = auth;

  return (
    <MDBox component="form" role="form">
      {/* Email Identification Step */}
      {!flowType && (
        <MDBox mb={2}>
          <MDInput
            type="email"
            label="Email"
            onChange={(e) => setEmail(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter" && isEmailValid) {
                e.preventDefault();
                handleEmailSubmit();
              }
            }}
            value={email}
            variant="standard"
            fullWidth
            placeholder={emailPlaceholder}
            InputLabelProps={{ shrink: true }}
          />
          <MDBox mt={3} mb={1}>
            <MDButton
              variant="gradient"
              color={isEmailValid ? "info" : "light"}
              fullWidth
              fontWeight="bold"
              onClick={handleEmailSubmit}
              disabled={!isEmailValid}
              sx={{
                "&:disabled": {
                  color: "grey",
                },
              }}
            >
              {emailSubmitStart ? (
                <CircularProgress size={20} color="white" />
              ) : (
                "Continue"
              )}
            </MDButton>
          </MDBox>
        </MDBox>
      )}

      {/* Password Step */}
      {email && flowType && (flowType.includes("register") || flowType.includes("login")) && (
        <MDBox mb={2}>
          {/* Email Display with Edit Option */}
          <MDTypography
            variant="body2"
            color="secondary"
            sx={{
              fontSize: "0.75rem",
              fontWeight: 400,
              lineHeight: 1.25,
              letterSpacing: "0.00938em",
              marginBottom: "1em",
            }}
          >
            Email: {email}
            {showEmailEditIcon && (
              <>
                &nbsp;
                <Icon
                  sx={{
                    fontSize: "0.75rem",
                    cursor: "pointer",
                    verticalAlign: "top",
                    "&:hover": {
                      opacity: 0.7,
                    },
                  }}
                  onClick={handleEditEmail}
                >
                  edit_outlined
                </Icon>
              </>
            )}
          </MDTypography>

          {/* Password Input */}
          <MDBox mb={2}>
            <MDInput
              type="password"
              label={flowType.includes("register") ? "New Password" : "Password"}
              onChange={(e) => setPassword(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter" && isPasswordValid && isConfirmPasswordValid) {
                  e.preventDefault();
                  handlePasswordSubmit(e);
                }
              }}
              value={password}
              variant="standard"
              fullWidth
              placeholder="************"
              InputLabelProps={{ shrink: true }}
            />
          </MDBox>

          {/* Confirm Password Input (Registration only) */}
          {flowType.includes("register") && (
            <MDBox mb={2}>
              <MDInput
                type="password"
                label="Confirm Password"
                onChange={(e) => setConfirmPassword(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter" && isPasswordValid && isConfirmPasswordValid) {
                    e.preventDefault();
                    handlePasswordSubmit(e);
                  }
                }}
                value={confirmPassword}
                variant="standard"
                fullWidth
                placeholder="************"
                InputLabelProps={{ shrink: true }}
              />
            </MDBox>
          )}

          {/* Submit Button */}
          <MDBox mt={3} mb={1}>
            <MDButton
              variant="gradient"
              color={isPasswordValid && isConfirmPasswordValid ? "info" : "light"}
              fullWidth
              fontWeight="bold"
              onClick={handlePasswordSubmit}
              disabled={!isPasswordValid || !isConfirmPasswordValid}
              sx={{
                "&:disabled": {
                  color: "grey",
                },
              }}
            >
              {passwordSubmitStart ? (
                <CircularProgress size={20} color="white" />
              ) : flowType.includes("register") ? (
                "Register"
              ) : (
                "Login"
              )}
            </MDButton>
          </MDBox>
        </MDBox>
      )}
    </MDBox>
  );
};

export default AuthenticationForm;
