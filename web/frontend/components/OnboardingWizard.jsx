import React, { useState } from "react";
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDButton from "@/components/MDButton";
// Add other MD components as needed

// Stepper data
const steps = [
  { key: "welcome", label: "Welcome", completed: false },
  { key: "profile", label: "Update your Account Details", completed: false },
  { key: "invite", label: "Invite your team", completed: false, optional: true },
  { key: "integrations", label: "Connect multiple datasources", completed: false, optional: true },
  { key: "call", label: "Schedule an onboarding call", completed: false, optional: true },
  { key: "ai", label: "Ask AI analyst demo", completed: false, optional: true },
];

function OnboardingWizard() {
  // Wizard step state
  const [currentStep, setCurrentStep] = useState(0);
  const [stepStatus, setStepStatus] = useState(steps.map(s => s.completed));
  const dataSyncProgress = 20; // Example value

  // Profile step state
  const [profileForm, setProfileForm] = useState({
    name: "",
    email: "",
    phone: "",
    industry: "",
    userType: "",
    role: "",
    focusAreas: [],
  });
  const [profileErrors, setProfileErrors] = useState({});
  const industryOptions = [
    "Art & Photography", "Animals & Pet Supplies", "Baby & Toddler", "Clothing & Fashion", "Jewelry & Accessories", "Electronics", "Food & Drink", "Home & Garden", "Furniture", "Hardware", "Health & Beauty", "Sports & Recreation", "Toys & Games", "Stationary", "Other"
  ];
  const userTypeOptions = [
    { value: "owner", label: "Shopify Merchant" },
    { value: "agency", label: "Agency" },
    { value: "other", label: "Other" }
  ];
  const roleOptions = [
    "Brand Manager", "Marketer", "Ad Agency", "Finance", "Supply Chain", "Operations"
  ];
  const focusAreaOptions = [
    "Acquisition", "Retention", "Product Performance", "Web Analytics", "Others"
  ];

  // Invite Team step state
  const [teamEmail, setTeamEmail] = useState("");
  const [teamRole, setTeamRole] = useState("Member");
  const [teamList, setTeamList] = useState([]);
  const [teamError, setTeamError] = useState("");
  const teamRoles = ["Member", "Admin", "Owner"];

  // Integrations step state
  const [connectedSources, setConnectedSources] = useState([]);
  const sources = [
    { key: "shopify", label: "Shopify", color: "success.main", description: "Connect your Shopify store" },
    { key: "googleads", label: "Google Ads", color: "info.main", description: "Import advertising data" },
    { key: "facebookads", label: "Facebook Ads", color: "primary.main", description: "Track Meta advertising" },
    { key: "klaviyo", label: "Klaviyo", color: "warning.main", description: "Email marketing analytics" },
    { key: "ga4", label: "Google Analytics", color: "error.main", description: "Web analytics integration" },
    { key: "tiktokads", label: "TikTok Ads", color: "secondary.main", description: "TikTok advertising data" },
  ];

  // Call step state
  const [selectedDate, setSelectedDate] = useState("");
  const [selectedTime, setSelectedTime] = useState("");
  const dates = ["Today", "Tomorrow", "Wednesday", "Thursday"];
  const times = ["9:00 AM", "10:00 AM", "11:00 AM", "1:00 PM", "2:00 PM", "3:00 PM", "4:00 PM"];

  // AI Demo step state
  const [selectedInsight, setSelectedInsight] = useState("");
  const demoOptions = [
    "Revenue Optimization",
    "Customer Segmentation",
    "Ad Performance",
    "Product Analysis"
  ];

  // --- HANDLERS ---

  const handleProfileChange = (field, value) => {
    setProfileForm({ ...profileForm, [field]: value });
  };
  const handleProfileFocusAreaToggle = (area) => {
    setProfileForm({
      ...profileForm,
      focusAreas: profileForm.focusAreas.includes(area)
        ? profileForm.focusAreas.filter(a => a !== area)
        : [...profileForm.focusAreas, area]
    });
  };
  const handleProfileRoleSelect = (role) => {
    setProfileForm({ ...profileForm, role });
  };
  const validateProfile = () => {
    const newErrors = {};
    if (!profileForm.name) newErrors.name = "Name is required.";
    if (!profileForm.email || !/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(profileForm.email)) newErrors.email = "Valid email required.";
    if (!profileForm.phone || !/^\+?[0-9\s-]{7,}$/.test(profileForm.phone)) newErrors.phone = "Valid phone required.";
    if (!profileForm.industry) newErrors.industry = "Industry required.";
    if (!profileForm.userType) newErrors.userType = "User type required.";
    return newErrors;
  };

  // Step completion and navigation
  const handleStepComplete = (stepIdx) => {
    const updatedStatus = [...stepStatus];
    updatedStatus[stepIdx] = true;
    setStepStatus(updatedStatus);
    if (stepIdx < steps.length - 1) {
        setCurrentStep(stepIdx + 1);
    }
  };

  // Event handlers for each step's primary action
  const handleProfileSubmit = (e) => {
    e.preventDefault();
    const newErrors = validateProfile();
    setProfileErrors(newErrors);
    if (Object.keys(newErrors).length === 0) {
      console.log("Profile Submitted:", profileForm);
      // TODO: API call to save onboarding details
      handleStepComplete(currentStep);
    }
  };

  const handleAddTeam = () => {
    if (!teamEmail || !/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(teamEmail)) {
      setTeamError("Enter a valid email address.");
      return;
    }
    setTeamList([...teamList, { email: teamEmail, role: teamRole }]);
    setTeamEmail("");
    setTeamRole("Member");
    setTeamError("");
  };

  const handleInviteContinue = () => {
    console.log("Invites Sent:", teamList);
    // TODO: API call to invite team members
    handleStepComplete(currentStep);
  };

  const handleIntegrationsConnect = (key) => {
    if (!connectedSources.includes(key)) {
      setConnectedSources([...connectedSources, key]);
    }
  };

  const handleIntegrationsContinue = () => {
    console.log("Integrations Saved:", connectedSources);
    // TODO: API call to save integrations
    handleStepComplete(currentStep);
  };

  const handleCallContinue = () => {
    console.log("Call Scheduled:", { date: selectedDate, time: selectedTime });
    // TODO: API call to book call
    handleStepComplete(currentStep);
  };

  const handleAIDemoContinue = () => {
    console.log("AI Demo Requested:", selectedInsight);
    // TODO: API call to request demo
    handleStepComplete(currentStep);
  };

  // --- RENDER FUNCTIONS ---

  const renderStepper = () => (
    <MDBox width={260} p={2} style={{ minHeight: "100vh", borderRight: "1px solid #eee" }}>
      <MDTypography variant="h6" mb={2}>Datadrew AI<br />Setup Guide</MDTypography>
      <MDTypography variant="caption" color="text" mb={2}>Avg. time to complete: 5 min</MDTypography>
      {steps.map((step, idx) => (
        <MDBox key={step.key} display="flex" alignItems="center" mb={2}>
          <MDButton
            variant={currentStep === idx ? "contained" : "outlined"}
            color={stepStatus[idx] ? "success" : "info"}
            size="small"
            onClick={() => setCurrentStep(idx)}
            style={{ minWidth: 32, minHeight: 32, borderRadius: "50%", marginRight: 12 }}
          >
            {stepStatus[idx] ? "✓" : idx + 1}
          </MDButton>
          <MDTypography variant="body2" color={currentStep === idx ? "primary" : "text"}>
            {step.label}
            {step.optional && <MDTypography variant="caption" color="text"> (Optional)</MDTypography>}
          </MDTypography>
        </MDBox>
      ))}
    </MDBox>
  );

  const renderSidebar = () => (
    <MDBox width={260} p={2} style={{ minHeight: "100vh", borderLeft: "1px solid #eee" }}>
      <MDBox mb={4}>
        <MDTypography variant="subtitle2">Data Sync</MDTypography>
        <MDTypography variant="caption" color="text">Initializing your workspace...</MDTypography>
        <MDBox mt={1} width="100%" height={8} bgcolor="#eee" borderRadius={4}>
          <MDBox width={`${dataSyncProgress}%`} height={8} bgcolor="info.main" borderRadius={4} />
        </MDBox>
        <MDTypography variant="caption" color="text">{dataSyncProgress}% complete</MDTypography>
      </MDBox>
      <MDBox>
        <MDTypography variant="subtitle2">Need Help?</MDTypography>
        <MDTypography variant="caption" color="text">To better use your 7 day trial, here are some helpful resources to get you started.</MDTypography>
        <MDBox mt={2}>
          <MDTypography variant="body2">• Documentation</MDTypography>
          <MDTypography variant="body2">• Schedule a demo</MDTypography>
          <MDTypography variant="body2">• Live Chat</MDTypography>
          <MDTypography variant="body2">• Videos</MDTypography>
        </MDBox>
      </MDBox>
    </MDBox>
  );

  const renderStepContent = () => {
    switch (steps[currentStep].key) {
      case "welcome":
        return (
          <MDBox display="flex" flexDirection="column" alignItems="center" justifyContent="center" height="100%" mt={10}>
            <MDBox mb={3}>
              <MDBox
                display="flex"
                alignItems="center"
                justifyContent="center"
                width={80}
                height={80}
                bgcolor="info.main"
                borderRadius={16}
                boxShadow={2}
                mb={2}
              >
                <MDTypography variant="h3" color="white" fontWeight="bold">D</MDTypography>
              </MDBox>
            </MDBox>
            <MDTypography variant="h2" fontWeight="bold" mb={2} color="text.primary">
              Welcome to Datadrew AI!
            </MDTypography>
            <MDTypography variant="body1" mb={4} color="text.secondary" maxWidth={480} textAlign="center">
              You're just minutes away from transforming your data into profitable growth.<br />
              There are a few things that need to be set up first. Ready to get started?
            </MDTypography>
            <MDButton
              variant="contained"
              color="info"
              size="large"
              onClick={() => handleStepComplete(currentStep)}
              sx={{ mt: 2, px: 4, borderRadius: 2 }}
            >
              Get started&nbsp;→
            </MDButton>
          </MDBox>
        );

      case "profile":
        return (
          <MDBox maxWidth={700} mx="auto" mt={6}>
            <MDTypography variant="h3" fontWeight="bold" mb={2}>
              Complete Your Profile
            </MDTypography>
            <MDTypography variant="body1" color="text.secondary" mb={4}>
              Tell us more about your role and interests.
            </MDTypography>
            <MDBox component="form" display="flex" flexDirection="column" gap={3} onSubmit={handleProfileSubmit}>
              <MDBox display="flex" gap={2}>
                <MDBox flex={1}>
                  <MDTypography variant="subtitle2" mb={1}>Full Name</MDTypography>
                  <MDBox component="input" type="text" value={profileForm.name} onChange={e => handleProfileChange("name", e.target.value)} placeholder="Enter your full name" style={{width: '100%', padding: '12px', borderRadius: '8px', border: '1px solid #ddd'}} />
                  {profileErrors.name && <MDTypography variant="caption" color="error">{profileErrors.name}</MDTypography>}
                </MDBox>
                <MDBox flex={1}>
                  <MDTypography variant="subtitle2" mb={1}>Work Email</MDTypography>
                  <MDBox component="input" type="email" value={profileForm.email} onChange={e => handleProfileChange("email", e.target.value)} placeholder="Enter your work email" style={{width: '100%', padding: '12px', borderRadius: '8px', border: '1px solid #ddd'}} />
                  {profileErrors.email && <MDTypography variant="caption" color="error">{profileErrors.email}</MDTypography>}
                </MDBox>
              </MDBox>
              <MDBox display="flex" gap={2}>
                <MDBox flex={1}>
                  <MDTypography variant="subtitle2" mb={1}>Phone Number</MDTypography>
                  <MDBox component="input" type="tel" value={profileForm.phone} onChange={e => handleProfileChange("phone", e.target.value)} placeholder="Enter your phone number" style={{width: '100%', padding: '12px', borderRadius: '8px', border: '1px solid #ddd'}} />
                  {profileErrors.phone && <MDTypography variant="caption" color="error">{profileErrors.phone}</MDTypography>}
                </MDBox>
                <MDBox flex={1}>
                  <MDTypography variant="subtitle2" mb={1}>Industry</MDTypography>
                  <MDBox component="select" value={profileForm.industry} onChange={e => handleProfileChange("industry", e.target.value)} style={{width: '100%', padding: '12px', borderRadius: '8px', border: '1px solid #ddd', appearance: 'none', background: 'white'}}>
                    <option value="" disabled>Select your industry</option>
                    {industryOptions.map(opt => <option key={opt} value={opt}>{opt}</option>)}
                  </MDBox>
                  {profileErrors.industry && <MDTypography variant="caption" color="error">{profileErrors.industry}</MDTypography>}
                </MDBox>
              </MDBox>
              <MDBox>
                <MDTypography variant="subtitle2" mb={1}>User Type</MDTypography>
                <MDBox component="select" value={profileForm.userType} onChange={e => handleProfileChange("userType", e.target.value)} style={{width: '100%', padding: '12px', borderRadius: '8px', border: '1px solid #ddd', appearance: 'none', background: 'white'}}>
                  <option value="" disabled>Select user type</option>
                  {userTypeOptions.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
                </MDBox>
                {profileErrors.userType && <MDTypography variant="caption" color="error">{profileErrors.userType}</MDTypography>}
              </MDBox>
              <MDBox>
                <MDTypography variant="subtitle2" mb={1}>What describes you best?</MDTypography>
                <MDBox display="flex" flexWrap="wrap" gap={2}>
                  {roleOptions.map(role => (
                    <MDButton key={role} variant={profileForm.role === role ? "contained" : "outlined"} color="info" sx={{minWidth: 160, mb:1}} onClick={() => handleProfileRoleSelect(role)}>{role}</MDButton>
                  ))}
                </MDBox>
              </MDBox>
              <MDBox>
                <MDTypography variant="subtitle2" mb={1}>What are your major focus areas? (Select multiple)</MDTypography>
                <MDBox display="flex" flexWrap="wrap" gap={2}>
                  {focusAreaOptions.map(area => (
                    <MDButton key={area} variant={profileForm.focusAreas.includes(area) ? "contained" : "outlined"} color="info" sx={{minWidth: 160, mb:1}} onClick={() => handleProfileFocusAreaToggle(area)}>{area}</MDButton>
                  ))}
                </MDBox>
              </MDBox>
              <MDBox display="flex" justifyContent="flex-end" gap={2} mt={4}>
                <MDButton variant="contained" color="info" type="submit">
                  Continue&nbsp;→
                </MDButton>
              </MDBox>
            </MDBox>
          </MDBox>
        );

      case "invite":
        return (
          <MDBox maxWidth={700} mx="auto" mt={6}>
            <MDTypography variant="h3" fontWeight="bold" mb={2}>
              Invite Your Team
            </MDTypography>
            <MDTypography variant="body1" color="text.secondary" mb={4}>
              Share Datadrew with teammates, advisors, agencies and investors
            </MDTypography>
            <MDBox display="flex" flexDirection="column" gap={3}>
              <MDBox display="flex" gap={2} alignItems="flex-end">
                <MDBox flex={2}>
                  <MDTypography variant="subtitle2" mb={1}>Add Team Member</MDTypography>
                  <MDBox component="input" type="email" value={teamEmail} onChange={e => setTeamEmail(e.target.value)} placeholder="Enter email address" style={{width: '100%', padding: '12px', borderRadius: '8px', border: '1px solid #ddd'}} />
                  {teamError && <MDTypography variant="caption" color="error">{teamError}</MDTypography>}
                </MDBox>
                <MDBox flex={1}>
                  <MDBox component="select" value={teamRole} onChange={e => setTeamRole(e.target.value)} style={{width: '100%', padding: '12px', borderRadius: '8px', border: '1px solid #ddd'}}>
                    {teamRoles.map(role => <option key={role} value={role}>{role}</option>)}
                  </MDBox>
                </MDBox>
                <MDButton variant="contained" color="info" onClick={handleAddTeam} sx={{height: 48, minWidth: 80}}>
                  + Add
                </MDButton>
              </MDBox>
              {teamList.length > 0 && (
                <MDBox mt={2} p={2} border="1px solid #eee" borderRadius={2}>
                  <MDTypography variant="subtitle2" mb={1}>Team Members to Invite</MDTypography>
                  {teamList.map((member, idx) => (
                    <MDBox key={idx} display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                      <MDTypography variant="body2">{member.email}</MDTypography>
                      <MDTypography variant="caption" color="text.secondary" sx={{border: '1px solid #ddd', px: 1, borderRadius: 1}}>{member.role}</MDTypography>
                    </MDBox>
                  ))}
                </MDBox>
              )}
              <MDBox display="flex" justifyContent="space-between" gap={2} mt={4}>
                <MDButton variant="text" color="info" onClick={handleInviteContinue}>
                  Skip for now
                </MDButton>
                <MDButton variant="contained" color="info" onClick={handleInviteContinue}>
                  Continue&nbsp;→
                </MDButton>
              </MDBox>
            </MDBox>
          </MDBox>
        );

      case "integrations":
        return (
          <MDBox maxWidth={900} mx="auto" mt={6}>
            <MDTypography variant="h3" fontWeight="bold" mb={2}>
              Connect Multiple Datasources
            </MDTypography>
            <MDTypography variant="body1" color="text.secondary" mb={4}>
              We're currently connected to {connectedSources.length} data source{connectedSources.length !== 1 ? "s" : ""} <MDTypography variant="caption" color="text">(Optional)</MDTypography>
            </MDTypography>
            <MDBox display="flex" flexWrap="wrap" gap={3}>
              {sources.map(source => (
                <MDBox key={source.key} width={"calc(50% - 12px)"} minWidth={320} p={3} bgcolor="background.paper" borderRadius={4} boxShadow={1} display="flex" flexDirection="column" alignItems="flex-start" mb={2}>
                  <MDBox display="flex" alignItems="center" gap={2} mb={1}>
                    <MDBox width={36} height={36} bgcolor={source.color} borderRadius={2} display="flex" alignItems="center" justifyContent="center">
                      <MDTypography variant="h5" color="white" fontWeight="bold">{source.label[0]}</MDTypography>
                    </MDBox>
                    <MDTypography variant="h5" fontWeight="medium">{source.label}</MDTypography>
                  </MDBox>
                  <MDTypography variant="body2" color="text.secondary" mb={2}>{source.description}</MDTypography>
                  <MDButton
                    variant={connectedSources.includes(source.key) ? "contained" : "outlined"}
                    color={connectedSources.includes(source.key) ? "success" : "info"}
                    onClick={() => handleIntegrationsConnect(source.key)}
                    sx={{ minWidth: 120, mt: 'auto' }}
                  >
                    {connectedSources.includes(source.key) ? "✓ Connected" : "Connect"}
                  </MDButton>
                </MDBox>
              ))}
            </MDBox>
            <MDBox display="flex" justifyContent="space-between" gap={2} mt={4}>
              <MDButton variant="text" color="info" onClick={handleIntegrationsContinue}>
                Skip for now
              </MDButton>
              <MDButton variant="contained" color="info" onClick={handleIntegrationsContinue}>
                Continue&nbsp;→
              </MDButton>
            </MDBox>
          </MDBox>
        );

      case "call":
        return (
          <MDBox maxWidth={700} mx="auto" mt={6}>
            <MDTypography variant="h3" fontWeight="bold" mb={2}>
              Schedule an Onboarding Call
            </MDTypography>
            <MDTypography variant="body1" color="text.secondary" mb={4}>
              Book a call with our onboarding specialist to get the most out of Datadrew AI.
            </MDTypography>
            <MDBox display="flex" gap={2} mb={3}>
              <MDBox flex={1}>
                <MDTypography variant="subtitle2" mb={1}>Select Date</MDTypography>
                <MDBox component="select" value={selectedDate} onChange={e => setSelectedDate(e.target.value)} style={{width: '100%', padding: '12px', borderRadius: '8px', border: '1px solid #ddd'}}>
                  <option value="" disabled>Select date</option>
                  {dates.map(date => <option key={date} value={date}>{date}</option>)}
                </MDBox>
              </MDBox>
              <MDBox flex={1}>
                <MDTypography variant="subtitle2" mb={1}>Select Time</MDTypography>
                <MDBox component="select" value={selectedTime} onChange={e => setSelectedTime(e.target.value)} style={{width: '100%', padding: '12px', borderRadius: '8px', border: '1px solid #ddd'}}>
                  <option value="" disabled>Select time</option>
                  {times.map(time => <option key={time} value={time}>{time}</option>)}
                </MDBox>
              </MDBox>
            </MDBox>
            <MDBox display="flex" justifyContent="space-between" gap={2} mt={4}>
              <MDButton variant="text" color="info" onClick={handleCallContinue}>
                Skip for now
              </MDButton>
              <MDButton variant="contained" color="info" onClick={handleCallContinue} disabled={!selectedDate || !selectedTime}>
                Continue&nbsp;→
              </MDButton>
            </MDBox>
          </MDBox>
        );

      case "ai":
        return (
          <MDBox maxWidth={700} mx="auto" mt={6}>
            <MDTypography variant="h3" fontWeight="bold" mb={2}>
              Ask AI Analyst Demo
            </MDTypography>
            <MDTypography variant="body1" color="text.secondary" mb={4}>
              Try out Datadrew's AI-powered analytics. Select a demo insight below.
            </MDTypography>
            <MDBox display="flex" flexDirection="column" gap={3}>
              <MDBox>
                <MDTypography variant="subtitle2" mb={1}>Select Demo Insight</MDTypography>
                <MDBox display="flex" flexWrap="wrap" gap={2}>
                  {demoOptions.map(opt => (
                    <MDButton key={opt} variant={selectedInsight === opt ? "contained" : "outlined"} color="info" onClick={() => setSelectedInsight(opt)}>{opt}</MDButton>
                  ))}
                </MDBox>
              </MDBox>
              <MDBox display="flex" justifyContent="space-between" gap={2} mt={4}>
                <MDButton variant="text" color="info" onClick={handleAIDemoContinue}>
                  Skip for now
                </MDButton>
                <MDButton variant="contained" color="info" onClick={handleAIDemoContinue} disabled={!selectedInsight}>
                  Continue&nbsp;→
                </MDButton>
              </MDBox>
            </MDBox>
          </MDBox>
        );

      default:
        return (
          <MDBox textAlign="center" mt={10}>
            <MDTypography variant="h4">All Done!</MDTypography>
            <MDTypography variant="body2" color="text" mt={2}>You have completed the setup guide.</MDTypography>
          </MDBox>
        );
    }
  };

  return (
    <MDBox display="flex" justifyContent="space-between">
      {renderStepper()}
      <MDBox flex={1} p={3}>
        {renderStepContent()}
      </MDBox>
      {renderSidebar()}
    </MDBox>
  );
}

export default OnboardingWizard;