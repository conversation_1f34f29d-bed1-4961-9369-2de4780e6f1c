import React, { useState, useEffect } from "react";
import MDBox from "@/components/MDBox";
import TableV2 from "@/components/Report/views/TableV2";
import Plotly<PERSON>hart from "@/components/Report/views/PlotlyChart";
import PillBar from '@/components/PillBar';
import Divider from "@mui/material/Divider";
import MDTypography from "@/components/MDTypography";
import { useTranslation } from "react-i18next";
import Grid from "@mui/material/Grid";
import Icon from "@mui/material/Icon";
import MDBadge from "@/components/MDBadge";
import DatasetIcon from '@mui/icons-material/Dataset';
import BarChartIcon from '@mui/icons-material/BarChart';
import MDButton from "@/components/MDButton";
import MDTooltip from "@/components/MDTooltip";

import {TableIcon, ChartIcon} from "@/examples/Icons";

import logoShopify from "@/assets/images/shopify-logo.svg";
import logoFacebook from "@/assets/images/facebook-logo.png";
import logoGoogleAds from "@/assets/images/google-ads-logo.png";
import logoGoogleAnalytics from "@/assets/images/google-analytics-logo.png";
import logoDatadrew from "@/assets/images/brand-short-3.png";
import {SOURCE_FB, SOURCE_SHOPIFY, SOURCE_GOOGLE_ADS, SOURCE_GOOGLE_ANALYTICS} from "@/layouts/dashboards/metrics/metadata"

const sourceInfo = (source) => {
  switch (source) {
    case SOURCE_GOOGLE_ADS:
      return {logo: logoGoogleAds, title: "google-ads"};
    case SOURCE_GOOGLE_ANALYTICS:
      return {logo: logoGoogleAnalytics, title: "integration.ga4"};
    case SOURCE_FB:
      return {logo: logoFacebook, title: "facebook-ads"};
    case SOURCE_SHOPIFY:
      return {logo: logoShopify, title: "shopify"};
    case "":
    case null:
    case undefined:
      return {logo: logoShopify, title: "shopify"};
    default:
      return {
        logo: logoDatadrew,
        title: source.split("-").join(" ")
      };
  }
};
// View component registry - maps viewType to component
const VIEW_COMPONENTS = {
  table: TableV2,
  chart: PlotlyChart
};

/**
 * Resolves variables starting with $ in the provided config object
 * @param {Object} config - The configuration object
 * @param {Array} variables - Array of variable strings to resolve
 * @returns {Object} - Object with resolved variable mappings
 */
function resolve(config, variables = []) {
  if (!variables || !Array.isArray(variables)) {
    return {};
  }

  // Start with existing variable_map if available
  const variable_map = { ...(config.variable_map || {}) };

  // Process each variable
  variables.forEach((variable) => {
    // Skip if not a string or doesn't start with $
    if (typeof variable !== 'string' || !variable.startsWith('$')) {
      return;
    }

    // Skip if already resolved in the variable_map
    if (variable_map[variable] !== undefined) {
      return;
    }

    // Parse the path parts
    const parts = variable.substring(1).split('.');
    const source = parts[0]; // config, global 

    // Handle different sources
    if (source === 'config') {
      // Navigate through the config object
      let value = config;
      for (let i = 1; i < parts.length; i++) {
        if (value === undefined || value === null) {
          break;
        }
        value = value[parts[i]];
      }
      variable_map[variable] = value;
    }
  });

  return variable_map;
}

// Render action buttons if provided in config
const renderActionButtons = (actions) => {
  if (!actions || !Array.isArray(actions) || actions.length === 0) {
    return null;
  }

  return (
    <MDBox display="flex" justifyContent="flex-end" alignItems="center" gap={1} my={1} px={1.5}>
      {actions.map((action, index) => {
        const button = (
          <MDButton
            key={index}
            variant="text"
            size="small"
            onClick={action.onClick}
            color="secondary"
            startIcon={action.icon}
            sx={{ 
              fontSize: "0.7rem",
              padding: "4px 8px",
              minWidth: "auto",
              '&:hover': { 
                backgroundColor: 'rgba(0,0,0,0.04)',
                borderColor: "rgba(0, 0, 0, 0.2)"
              } 
            }}
          >
            {action.label}
          </MDButton>
        );

        return action.tooltip ? (
          <MDTooltip key={index} title={action.tooltip} placement="top">
            {button}
          </MDTooltip>
        ) : button;
      })}
    </MDBox>
  );
};

export default function Report({ config }) {
  const [activeViewId, setActiveViewId] = useState("");
  const { t } = useTranslation();

  if (!config.views || config.views.length === 0) {
    return null;
  }

  useEffect(() => {
    if (config.views.length > 0) {
      // Find chart view if available
      const chartView = config.views.find(view => view.viewType === "chart");
      // Set chart view as active if available, otherwise use first view
      setActiveViewId(chartView ? chartView.id : config.views[0].id);
    }
  }, [config.views]);

  let viewOptions = config.views.map((view) => ({
    label: (
      <MDTooltip placement="top" title={
        <MDTypography ml={0.5} variant="caption" fontWeight="regular" sx={{"fontSize": "0.75rem", textTransform: "capitalize"}}>{view.title || view.viewType}</MDTypography>
      }>
      <MDBox display="flex" direction="row" alignItems="center" verticalAlign="middle" justifyContent="center">
        {view.viewType === "table" 
          ? <TableIcon/> 
          : <ChartIcon/>}
      </MDBox>
      </MDTooltip>
    ),
    value: view.id
  }));

  // Determine if we should show a footer with actions
  const shouldShowFooter = config.actions && Array.isArray(config.actions) && config.actions.length > 0;

  return (
    <MDBox
      mb={3}
      sx={{
        borderRadius: '12px',
        backgroundColor: 'rgba(0, 0, 0, 0.02)', // Soft background tint
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.03)', // Subtle shadow
        maxWidth: '100%',
        overflow: 'hidden'
      }}
    >
      <MDBox p={2} pb={1.5}>
        <Grid container spacing={2} alignItems="center" justifyContent="space-between">
          <Grid item xs={12} sm={config.views.length > 1 ? 7 : 12} md={config.views.length > 1 ? 8 : 12}>
            <MDTypography 
              variant="h6" 
              color="text.primary" 
              fontWeight="semiBold" 
              className="card-title-default" 
              sx={{ 
                overflow: "hidden", 
                textOverflow: "ellipsis",
                whiteSpace: "normal",
                display: "-webkit-box",
                WebkitLineClamp: 2,
                WebkitBoxOrient: "vertical",
                fontSize: "1.05rem",
                width: "100%",
                lineHeight: 1.3,
                pr: config.views.length > 1 ? 1 : 0 // Add padding-right when switcher is present
              }}
            >
              {t(config.title ?? "report")}
            </MDTypography>
          </Grid>
          
          {config.views.length > 1 && 
            <Grid item xs={12} sm={5} md={4}>
              <MDBox sx={{ 
                display: "flex", 
                justifyContent: { xs: "flex-start", sm: "flex-end" },
                mt: { xs: 1.5, sm: 0 },
                pl: { sm: 1 } // Add padding-left for better spacing
              }}>
                <PillBar
                  name="type"
                  options={viewOptions}
                  value={activeViewId}
                  onChange={(value) => setActiveViewId(value)}
                />
              </MDBox>
            </Grid>
          }
        </Grid>
      </MDBox>
      
      {(config.title || config.views.length > 1) && <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%", borderColor: "rgba(0, 0, 0, 0.04)"}} />}
      
      {config.tags && 
        <MDBox 
          display="flex" 
          flexWrap="wrap" 
          alignItems="center" 
          justifyContent="left" 
          p={2} 
          pt={1.5}
          pb={1.5}
          sx={{ gap: 0.5 }}
        >
          {config.source && 
            <MDBox key="source" mr={0.5}>
              <MDBadge badgeContent={
                <MDBox display="flex" direction="row" alignItems="center">
                  <MDBox component="img" src={sourceInfo(config.source).logo} alt={sourceInfo(config.source).title} width="1rem" mx={0.1} /> &nbsp;
                  <MDTypography variant="caption" sx={{ fontSize: "0.80rem", fontWeight: "regular", textTransform: "capitalize" }}>
                    {t(sourceInfo(config.source).title)}
                  </MDTypography>
                </MDBox>
              } color="light" size="xs" variant="contained" container sx={{
                '& .MuiBadge-badge': {
                  background: "transparent"
                }
              }}/>
            </MDBox>
          }
          {config.tags.map((tag, index) => {
            return (
              <MDBox key={index} mb={0.25}>
                <MDBadge 
                  badgeContent={
                    <MDTypography variant="caption" sx={{ fontSize: "0.80rem", fontWeight: "regular" }}>
                      {tag.value}
                    </MDTypography>
                  } 
                  color={["info", "success", "warning"][index % 3]} 
                  size="sm" 
                  variant={"contained"} 
                  container
                  sx={{
                    '& .MuiBadge-badge': {
                      // color: false ? "white" : "text.disabled",
                      // backgroundColor: false ? "info.main" : "rgba(0, 0, 0, 0.03)",
                      // borderColor: false ? "info.main" : "rgba(0, 0, 0, 0.1)",
                      fontSize: "0.65rem",
                      // fontWeight: false ? "medium" : "regular",
                      padding: "4px 8px",
                      height: "auto",
                      minWidth: "auto"
                    }
                  }}
                />
              </MDBox>
            );
          })}
        </MDBox>
      }
      
      {config.tags && <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%", borderColor: "rgba(0, 0, 0, 0.04)"}} />}
      
      <MDBox
        sx={{
          overflowX: 'auto', // Enable horizontal scrolling
          width: '100%',
          WebkitOverflowScrolling: 'touch', // Smooth scrolling on iOS
          scrollbarWidth: 'thin', // Firefox
          msOverflowStyle: 'none', // IE/Edge
          '&::-webkit-scrollbar': {
            height: '8px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: 'rgba(0,0,0,0.05)',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'rgba(0,0,0,0.2)',
            borderRadius: '4px',
            '&:hover': {
              backgroundColor: 'rgba(0,0,0,0.3)',
            },
          },
        }}
      >
        <Grid container spacing={1} p={1.2} pb={1.5} sx={{ minWidth: 'min-content' }}>
          {config.views.filter((view) => view.id === activeViewId).map((view) => {
              const ViewComponent = VIEW_COMPONENTS[view.viewType];
              if (!ViewComponent) {
                return null;
              }
              return (
                <ViewComponent
                  key={view.id}
                  view_config={{
                    ...view,
                    variable_map: resolve(config, view.variables)
                  }}
                />
              );
          })}
        </Grid>
      </MDBox>
      
      {shouldShowFooter && (
        <>
          {renderActionButtons(config.actions)}
        </>
      )}
    </MDBox>
  );
}