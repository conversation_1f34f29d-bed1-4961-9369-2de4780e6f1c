import React from "react";
import { Paper, Table, TableBody, TableRow, TableCell, TableContainer, TablePagination } from "@mui/material";
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import { formatCellValue } from "@/util";

// [1 Aug'25] Note: Legacy prashna table that is no longer used. Kept here for reference, can remove in some time.
function ViewTable({ view_config }) {

    const resolveVariable = (variable) => {
        if (typeof variable === 'string' && variable.startsWith('$') && view_config.variable_map) {
            return view_config.variable_map[variable] || variable;
        }
        return variable;
    }

    const { columns = [], data: rows = [] } = resolveVariable(view_config.dataReference);
  
    const formatColumnName = (columnName) => {
        if (typeof columnName !== 'string') return columnName; // Return as is if not a string
        return columnName
            .split('_')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    };

    // Safe wrapper for formatCellValue with additional error handling
    const safeFormatCellValue = (value, columnName) => {
        try {
            return formatCellValue(value, columnName, 0);
        } catch (error) {
            console.error('Error formatting cell value:', error, { value, columnName });
            return '[Display Error]';
        }
    };

    const [page, setPage] = React.useState(0);
    const [rowsPerPage, setRowsPerPage] = React.useState(5);

    const handleChangePage = (event, newPage) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    // Determine if pagination should be shown
    const showPagination = rows.length > rowsPerPage;

    // Calculate minimum width based on number of columns
    const calculateMinWidth = () => {
      // Start with a base width
      const baseWidth = 250;
      // Add width for each additional column beyond the first
      const additionalColumnWidth = 150;
      return baseWidth + (columns.length - 1) * additionalColumnWidth;
    };

    // Get cell styling based on column index
    const getCellStyling = (index) => {
      const baseStyling = {
        minWidth: '120px',
        maxWidth: '250px',
        whiteSpace: 'normal',
        wordBreak: 'break-word',
        overflow: 'hidden',
        textOverflow: 'ellipsis'
      };
      
      // First column might need more space
      if (index === 0) {
        return {
          ...baseStyling,
          minWidth: '180px',
        };
      }
      
      return baseStyling;
    };

    return (
      <MDBox sx={{width: "100%"}}>
          <TableContainer 
            component={Paper} 
            sx={{
              borderRadius: "0px", 
              boxShadow: "none", 
              border: "0px", 
              p: 0,
              width: "100%",
              overflowX: "auto"
            }}
          >
            <Table 
              sx={{ 
                minWidth: calculateMinWidth(),
                width: "100%",
                tableLayout: "fixed"
              }} 
              size="small"
            >
              <TableBody>
                <TableRow>
                  {columns.map((column, index) => (
                    <TableCell 
                      key={index} 
                      align={index === 0 ? "left" : "right"}
                      sx={getCellStyling(index)}
                    >
                      <MDTypography 
                        variant="button" 
                        fontWeight="medium"
                        sx={{
                          display: 'block',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis'
                        }}
                      >
                        {formatColumnName(column)}
                      </MDTypography>
                    </TableCell>
                  ))}
                </TableRow>
              </TableBody>
              <TableBody>
                {rows.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((row, rowIndex) => (
                  <TableRow key={rowIndex} className="last:!border-0">
                    {columns.map((column, colIndex) => (
                      <TableCell 
                        key={colIndex} 
                        align={colIndex === 0 ? "left" : "right"}
                        sx={getCellStyling(colIndex)}
                      >
                        <MDTypography 
                          variant="button" 
                          fontWeight="regular"
                          sx={{
                            display: 'block',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis'
                          }}
                        >
                          {safeFormatCellValue(row[column], column)}
                        </MDTypography>
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            {showPagination && (
              <TablePagination
                rowsPerPageOptions={[5]}
                component="div"
                count={rows.length}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
              />
            )}
          </TableContainer>
      </MDBox>
    );
}

export default ViewTable;