import React from "react";
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDTooltip from "@/components/MDTooltip";
import { formatCellValue } from "@/util";
import DataTable from '@/examples/Tables/DataTable';

function ViewTableV2({ view_config }) {

  const resolveVariable = (variable) => {
    if (typeof variable === 'string' && variable.startsWith('$') && view_config.variable_map) {
      return view_config.variable_map[variable] || variable;
    }
    return variable;
  }

  // Handle different possible data structures
  let columns = [];
  let rows = [];

  if (view_config.dataReference) {
    const resolvedData = resolveVariable(view_config.dataReference);

    if (resolvedData && typeof resolvedData === 'object') {
      columns = resolvedData.columns || [];
      rows = resolvedData.data || resolvedData.rows || [];
    }
  } else if (view_config.columns && view_config.data) {
    // Direct structure
    columns = view_config.columns;
    rows = view_config.data;
  } else if (view_config.data) {
    // Try to infer columns from data
    if (Array.isArray(view_config.data) && view_config.data.length > 0) {
      columns = Object.keys(view_config.data[0]);
      rows = view_config.data;
    }
  }

  // Early return if no data
  if (!columns || columns.length === 0 || !rows || rows.length === 0) {
    return (
      <MDBox sx={{ width: "100%", p: 2, textAlign: "center" }}>
        <MDTypography variant="body2" color="secondary">
          No data available
        </MDTypography>
      </MDBox>
    );
  }

  const formatColumnName = React.useCallback((columnName) => {
    if (typeof columnName !== 'string') return columnName;
    return columnName
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }, []);

  // Safe wrapper for formatCellValue with additional error handling
  const safeFormatCellValue = React.useCallback((value, columnName) => {
    try {
      // Handle string numbers with many decimal places
      if (typeof value === 'string' && /^\d+\.\d+$/.test(value)) {
        const numValue = parseFloat(value);
        if (!isNaN(numValue)) {
          return numValue.toFixed(2);
        }
      }

      // Handle scientific notation strings
      if (typeof value === 'string' && /^\d+\.\d+E-\d+$/.test(value)) {
        const numValue = parseFloat(value);
        if (!isNaN(numValue)) {
          return numValue.toFixed(2);
        }
      }

      return formatCellValue(value, columnName, 0);
    } catch (error) {
      console.error('Error formatting cell value:', error, { value, columnName });
      return '[Display Error]';
    }
  }, []);

  // Determine if pagination should be shown
  const showPagination = rows.length > 5;

  // Transform data for DataTable component
  const tableData = React.useMemo(() => {
    try {
      // Create columns configuration for DataTable
      const tableColumns = columns.map((column, index) => {
        const isFirstColumn = index === 0;
        const isNumeric = column.toLowerCase().includes('count') ||
          column.toLowerCase().includes('amount') ||
          column.toLowerCase().includes('price') ||
          column.toLowerCase().includes('spend') ||
          column.toLowerCase().includes('purchase') ||
          column.toLowerCase().includes('customer');

        return {
          Header: (
            <MDTypography
              variant="button"
              fontWeight="bold"
              sx={{
                fontSize: '0.875rem',
                color: 'black',
              }}
            >
              {formatColumnName(column)}
            </MDTypography>
          ),
          accessor: column,
          align: isFirstColumn ? "-webkit-left" : "-webkit-right",
          width: isFirstColumn ? '25%' : isNumeric ? '15%' : '20%',
          Cell: ({ value }) => {
            const cellValue = safeFormatCellValue(value, column);
            const displayValue = typeof cellValue === 'string' && cellValue.length > 30
              ? cellValue.substring(0, 30) + '...'
              : cellValue;

            return (
              <MDTooltip
                title={cellValue}
                placement="top"
                arrow
                disableHoverListener={typeof cellValue === 'string' && cellValue.length <= 30}
              >
                <MDTypography
                  variant="button"
                  fontWeight="regular"
                  color="text"
                  sx={{
                    fontSize: '0.875rem',
                    cursor: typeof cellValue === 'string' && cellValue.length > 30 ? 'help' : 'default'
                  }}
                >
                  {displayValue}
                </MDTypography>
              </MDTooltip>
            );
          }
        };
      });

      // Transform rows data
      const tableRows = rows.map(row => {
        const transformedRow = {};
        columns.forEach(column => {
          transformedRow[column] = row[column] !== undefined ? row[column] : '';
        });
        return transformedRow;
      });

      const result = {
        columns: tableColumns,
        rows: tableRows
      };

      return result;
    } catch (error) {
      console.error("TableV2: Error creating table data:", error);
      throw error;
    }
  }, [columns, rows, formatColumnName, safeFormatCellValue]);

  try {
    return (
      <MDBox sx={{ width: "100%" }}>
        <DataTable
          table={tableData}
          entriesPerPage={{ defaultValue: 5, entries: [5] }}
          canSearch={true}
          showTotalEntries={false}
          pagination={showPagination ? { variant: "gradient", color: "info" } : false}
          isSorted={true}
          noEndBorder={false}
          isPrashna={true}
        />
      </MDBox>
    );
  } catch (error) {
    console.error("TableV2: Error rendering DataTable:", error);
    return (
      <MDBox sx={{ width: "100%", p: 2, textAlign: "center" }}>
        <MDTypography variant="body2" color="error">
          Error rendering table: {error.message}
        </MDTypography>
      </MDBox>
    );
  }
}

export default ViewTableV2;