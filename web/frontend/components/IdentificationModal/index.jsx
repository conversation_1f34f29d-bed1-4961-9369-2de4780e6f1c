import React, { useEffect } from "react";
import { Link } from "react-router-dom";
import { useMaterialUIController, setSelectedShop, tracker } from "@/context";
import { useTranslation } from "react-i18next";
import NiceModal, { useModal } from '@ebay/nice-modal-react';

// @mui material components
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import CircularProgress from '@mui/material/CircularProgress';

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDInput from "@/components/MDInput";
import MDButton from "@/components/MDButton";

// Shared authentication components
import { useAuthentication } from "@/hooks/useAuthentication";
import AuthenticationForm from "@/components/AuthenticationForm";
import { getMessageForFirebaseError } from "@/layouts/authentication/util";
import { toast } from "react-toastify";
import { getAuth, sendPasswordResetEmail } from 'firebase/auth';
import { app } from '@/firebase-config';

const IdentificationModal = NiceModal.create(({ onDismiss }) => {
  const modal = useModal();
  const [controller, dispatch] = useMaterialUIController();
  const { loginConfig, selectedShop, shopConfig } = controller;
  const loginShop = loginConfig?.shop;
  const { t } = useTranslation();
  const customerEmail = shopConfig?.shop?.onboard_email || shopConfig?.shop?.customer_email;

  const handlePasswordReset = async () => {
    if (!auth.email || !auth.email.includes('@')) {
      toast.error(t("please-enter-email-first"));
      return;
    }
    
    try {
      const authInstance = getAuth(app);
      await sendPasswordResetEmail(authInstance, auth.email);
      toast.success(t("password-reset-email-sent"));
    } catch (error) {
      toast.error(getMessageForFirebaseError(error.code || error.message));
    }
  };

    // Initialize authentication hook
  const auth = useAuthentication({
    onSuccess: () => {
      // Track successful identification before closing modal
      tracker.event("Workspace Identification Completed", { 
        shop: selectedShop || "unknown",
        flow_type: auth.flowType === "login" ? "existing_user" : auth.flowType === "register" ? "new_user" : "identifying_user",
        source: "identification_modal"
      });
      modal.hide();
      window.location.href = '/';
    },
    enableTracking: true,
    trackEvent: tracker.event,
    showSuccessToast: true
  });

  // Set email once if available from shopConfig
  if (customerEmail && !auth.email) {
    auth.setEmail(customerEmail);
  }

  // Track modal open
  useEffect(() => {
    tracker.event("Identification Modal Opened", { 
      shop: selectedShop || "unknown",
    });
  }, [selectedShop]);

  // Set selected shop when email is identified
  useEffect(() => {
    if (auth.flowType && loginShop?.myshopify_domain) {
      setSelectedShop(dispatch, loginShop.myshopify_domain);
    }
  }, [auth.flowType, loginShop, dispatch]);

  const handleClose = () => {
    tracker.event("Identification Modal Closed", { 
      shop: selectedShop || "unknown",
      flow_type: auth.flowType === "login" ? "existing_user" : auth.flowType === "register" ? "new_user" : "identifying_user"
    });
    if (onDismiss) {
      onDismiss();
    }
    modal.hide();
  };

  return (
    <Dialog
      open={modal.visible}
      onClose={null} // Disable clicking outside to close
      maxWidth="sm"
      fullWidth
      TransitionProps={{
        onExited: () => modal.remove(),
      }}
      PaperProps={{
        sx: {
          borderRadius: 3,
          maxHeight: '90vh'
        }
      }}
    >
      <MDBox display="flex" justifyContent="flex-end" alignItems="center" p={0.5}>    
        <IconButton onClick={handleClose} size="small">
          <CloseIcon />
        </IconButton>
      </MDBox>
      
      <DialogContent sx={{ pt: 0, pb: 3 }}>
        <MDBox p={3}>
          {/* Header */}
          <MDBox textAlign="center" mb={4}>
            <MDTypography variant="h4" fontWeight="bold" mb={1}>
              {t("finish-setting-up-workspace")}
            </MDTypography>
            <MDTypography variant="body2" color="text" mb={1}>
              {t("connect-email-invite-team-description")}
            </MDTypography>
          </MDBox>

          {/* Content based on identification status */}
          {!auth.flowType ? (
            <MDBox>
              <MDBox mb={2}>
                <MDInput
                  type="email"
                  label={t("email")}
                  value={auth.email}
                  onChange={(e) => auth.setEmail(e.target.value)}
                  fullWidth
                  placeholder={t("email-placeholder")}
                />
              </MDBox>
              
              <MDBox mt={3}>
                <MDButton 
                  variant="gradient" 
                  color="info" 
                  fullWidth 
                  onClick={auth.handleEmailSubmit}
                  disabled={auth.emailSubmitStart}
                >
                  {auth.emailSubmitStart ? (
                    <CircularProgress size={20} color="inherit" />
                  ) : (
                    t("continue")
                  )}
                </MDButton>
              </MDBox>
            </MDBox>
          ) : (
            <MDBox>
              <AuthenticationForm
                auth={auth}
                emailPlaceholder="<EMAIL>"
                showEmailEditIcon={true}
              />
              
              {auth.flowType === 'login' && (
                <MDBox mt={2} textAlign="center">
                  <Link 
                    onClick={handlePasswordReset}
                    style={{ 
                      cursor: 'pointer', 
                      textDecoration: 'underline',
                      color: 'inherit'
                    }}
                  >
                    <MDTypography variant="caption" color="text">
                      {t("forgot-password")}
                    </MDTypography>
                  </Link>
                </MDBox>
              )}
            </MDBox>
          )}
        </MDBox>
      </DialogContent>
    </Dialog>
  );
});

export default IdentificationModal;
