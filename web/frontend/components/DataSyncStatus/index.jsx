import React, { useEffect, useMemo } from "react";
import {Link} from "react-router-dom";

// antd imports
import {Progress} from "antd";
import BannerImage from "./BannerImage";

import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";

// Authentication layout components
import Grid from "@mui/material/Grid";
import Card from "@mui/material/Card";
import MDButton from "@/components/MDButton";

// Material Dashboard 2 PRO React examples
import DefaultNavbar from "@/examples/Navbars/DefaultNavbar";
import PageLayout from "@/examples/LayoutContainers/PageLayout";

// Material Dashboard 2 PRO React page layout routes
import pageRoutes from "@/page.routes";
import { useTranslation } from "react-i18next";

// Authentication pages components
import { setShopConfig, useMaterialUIController, useCancellableAxios } from "@/context";
import { ShopOptionsDropdown } from "@/layouts/pages/profile/current";
import {DashboardLoader} from "@/components/AppLoader";

const DataSyncStatus = () => {
    const {t} = useTranslation();
    const [controller, dispatch] = useMaterialUIController();
    const {shopConfig, loginConfig, selectedShop} = controller;
    const pollIntervalRef = React.useRef(null);
    const isUserFlow = loginConfig.user && loginConfig.user.email;

    let reqData = {};
    if (!!selectedShop) {
        reqData.selectedShop = selectedShop;
    }

    const axiosInstance = useCancellableAxios();
    
    let pollStatus = () => {
        axiosInstance.post("/api/shop/config?poll=1", reqData)
        .then((response) => {
            if (response.data && response.data.syncDone) {
                if (pollIntervalRef.current) {
                    clearInterval(pollIntervalRef.current);
                }
                setShopConfig(dispatch, { ...response.data })
            } else if (response.data && response.data.syncPercentage != shopConfig.syncPercentage) {
                setShopConfig(dispatch, { ...response.data })
            }
        })
        .catch((err) => {
            console.log("error", err);
        });
    };

    useEffect(() => {
        axiosInstance.post("/api/shop/config", reqData)
        .then(function (response) {
            if (response.data && !response.data.syncDone) {
                pollIntervalRef.current = setInterval(pollStatus, 30000); // every minute
                setTimeout(() => {
                    clearInterval(pollIntervalRef.current);
                }, 3600000); //stop polling after 1 hour
            }

            setShopConfig(dispatch, { ...response.data})
        })
        .catch((err) => {
            console.log("error", err);
        });

        return () => {
            if (pollIntervalRef.current) {
                clearInterval(pollIntervalRef.current);
            }
        };
    }, [selectedShop]);

    const bannerImage = useMemo(() => <BannerImage />, []);

    return (
        <PageLayout>
            <DefaultNavbar
                darkLogo={true}
                routes={pageRoutes}
                // action={{
                //   type: "external",
                //   route: "https://creative-tim.com/product/material-dashboard-pro-react",
                //   label: "buy now",
                // }}
                transparent
            />
            <MDBox px={1} width="100%" height="90vh" mx="auto" bgColor="light">
            {shopConfig.loading && <MDBox mt={12} mb={9}>
                <DashboardLoader />
            </MDBox>}
            {!shopConfig.loading && <MDBox mt={12} mb={9}>
                <Grid container justifyContent="center">
                <Grid item xs={11} lg={8}>
                    <MDBox mt={isUserFlow ? 5 : 10} mb={3} textAlign="center">
                    <MDBox mb={1}>
                        <MDTypography variant="h3" fontWeight="medium">
                            {t("welcome-aboard")}
                        </MDTypography>
                    </MDBox>
                    <MDTypography variant="h5" fontWeight="regular" color="secondary">
                        {t("data-sync-status-subtitle")}
                    </MDTypography>
                    </MDBox>
                    <Card>
                    <Grid container spacing={3} p={3}>
                        <Grid item xs={12} sm={6} container justifyContent="center">
                            <MDBox position="relative" mx="auto" alignItems="center" display="flex" flexDirection="column" justifyContent="middle" height="100%">
                                {bannerImage}
                            </MDBox>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <MDBox
                                alignItems="flex-start"
                                display="flex"
                                flexDirection="column"
                                justifyContent="center" height="100%">
                                <MDTypography variant="h5" fontWeight="regular" mb={1}>
                                    {t("generating-insights")}
                                </MDTypography>
                                <MDBox sx={{border: "1px dashed #ccc", width:"100%"}} p={1}>
                                <Progress
                                    width={"100%"}
                                    trailColor="#ffffff"
                                    strokeWidth={15}
                                    strokeColor={{
                                        "0%": "#108ee9",
                                        "100%": "#87d068",
                                    }}
                                    percent={shopConfig.syncPercentage}
                                />
                                </MDBox>
                                <MDTypography variant="button" color="dark" mb={1} mt={1}>
                                    {t("data-sync-status-desc-1")}
                                </MDTypography>
                                <MDTypography variant="button" color="secondary" fontWeight="regular">
                                    {t("data-sync-status-desc-2")} <MDTypography variant="button" component="a" color="secondary" href="mailto:<EMAIL>" fontWeight="regular"><EMAIL></MDTypography>.
                                </MDTypography>
                            </MDBox>
                        </Grid>
                    </Grid>
                    </Card>
                </Grid>

                {isUserFlow && (
                    <Grid item xs={11} lg={8} mt={3}>
                        <Card>
                        <MDBox display="flex" justifyContent="space-between" alignItems="center" p={2}>
                            <MDTypography variant="button" fontWeight="regular" color="dark" width="70%">
                                {t("switch-or-add-store")}
                            </MDTypography>
                            <ShopOptionsDropdown />
                        </MDBox>
                        </Card>
                    </Grid>
                )}

                <Grid item xs={11} lg={8} mt={3}>
                    <Card>
                        <Grid container spacing={1} p={2} pt={2}>
                            <Grid item xs={12} sm={12} container spacing={3} justifyContent="center" p={0} m={0}>
                                <Grid item xs={4} p={0.6}>
                                    <MDButton p={0} target="_blank" component={Link} to={"http://help.datadrew.io/en/articles/5239600-cohort-analysis"} variant="outlined" fullWidth sx={{p: "0 !important"}}>
                                    <Card className="embedded-card" p={1}>
                                    <MDBox mb={0.5} lineHeight={1} display="flex">
                                        <MDTypography variant="button" fontWeight="regular" color="dark" p={1} className="embedded-card-header">
                                            {t("cohort-analysis-blog-title")}
                                        </MDTypography>
                                    </MDBox>
                                    <MDTypography variant="caption" color="info" fontWeight="regular" p={1}>
                                        {t("cohort-analysis")}
                                    </MDTypography>
                                    </Card>
                                    </MDButton>
                                </Grid>

                                <Grid item xs={4} p={0.6}>
                                    <MDButton p={0} target="_blank" component={Link} to={"http://help.datadrew.io/en/articles/5096099-product-repurchase-analysis"} variant="outlined" fullWidth sx={{p: "0 !important"}}>
                                    <Card className="embedded-card" p={1}>
                                    <MDBox mb={0.5} lineHeight={1} display="flex">
                                        <MDTypography variant="button" fontWeight="regular" color="dark" p={1} className="embedded-card-header">
                                            {t("product-repurchase-blog-title")}
                                        </MDTypography>
                                    </MDBox>
                                    <MDTypography variant="caption" color="info" fontWeight="regular" p={1}>
                                        {t("p-product-repurchase")}
                                    </MDTypography>
                                    </Card>
                                    </MDButton>
                                </Grid>

                                <Grid item xs={4} p={0.6}>
                                    <MDButton p={0} target="_blank" component={Link} to={"http://help.datadrew.io/en/articles/8340868-rfm-analysis"} variant="outlined" fullWidth sx={{p: "0 !important"}}>
                                    <Card className="embedded-card" p={1}>
                                    <MDBox mb={0.5} lineHeight={1} display="flex">
                                        <MDTypography variant="button" fontWeight="regular" color="dark" p={1} className="embedded-card-header">
                                            {t("rfm-analysis-blog-title")}
                                        </MDTypography>
                                    </MDBox>
                                    <MDTypography variant="caption" color="info" fontWeight="regular" p={1}>
                                        {t("rfm-analysis")}
                                    </MDTypography>
                                    </Card>
                                    </MDButton>
                                </Grid>
                            </Grid>
                        </Grid>
                    </Card>
                </Grid>
                </Grid>
            </MDBox>}
            </MDBox>
        </PageLayout>
    );
};


export default DataSyncStatus;