import React, { useState, useEffect } from "react";
import Autocomplete from "@mui/material/Autocomplete";
import TextField from "@mui/material/TextField";
import Checkbox from "@mui/material/Checkbox";
import Chip from "@mui/material/Chip";
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import Icon from "@mui/material/Icon";
import { useTranslation } from "react-i18next";
import { getMetricBySlug } from "@/layouts/dashboards/metrics/metadata";
import logoFacebook from "@/assets/images/facebook-logo.png"; // legacy fallback (may be removed later)
import GoogleAdsIconAsset from '@/assets/images/google-ads-logo.png'; // legacy fallback
import ShopifyLogo from '@/assets/images/shopify-logo.svg'; // legacy fallback
import { sourceLogo } from "@/layouts/dashboards/metrics/MetricTitle";

export default function ColumnVisibilitySelector({ 
  columns = [], 
  visibleColumns = {}, 
  onVisibilityChange = () => {},
  alwaysVisibleColumns = [] // Columns that cannot be hidden
}) {
  const { t } = useTranslation();

  // Create a mapping from accessor to metric slug (same as the table uses)
  const accessorToMetricMap = {
    // Shopify metrics
    'order_count': 'shopify:order_count',
    'customer_count': 'shopify:cust_count', 
    'units_sold': 'shopify:units_sold',
    'net_revenue': 'shopify:total_price',
    'avg_selling_price': 'shopify:avg_price',
    // Meta/Facebook metrics
    'meta_ads_spend': 'facebook-marketing:spend',
    'meta_ads_cpc': 'facebook-marketing:cpc',
    'meta_ads_cpm': 'facebook-marketing:cpm',
    'meta_ads_ctr': 'facebook-marketing:ctr',
    'meta_ads_clicks': 'facebook-marketing:clicks',
    'meta_ads_impressions': 'facebook-marketing:impressions',
    // Google Ads metrics
    'gads_spend': 'google-ads:cost',
    'gads_cpc': 'google-ads:cpc',
    'gads_cpm': 'google-ads:cpm',
    'gads_ctr': 'google-ads:ctr',
    'gads_clicks': 'google-ads:clicks',
    'gads_impressions': 'google-ads:impressions',
    'gads_conversions': 'google-ads:conversions',
    'gads_conversions_value': 'google-ads:conversions_value',
    // Blended metrics
    'blended_catalog_spend': 'blended:catalog-spend',
    'blended_catalog_roas': 'blended:catalog-roas'
  };

  // Get proper column label using the same method as the table
  const getColumnLabel = (accessor) => {
    const metricSlug = accessorToMetricMap[accessor];
    if (!metricSlug) {
      const fallbackText = accessor === 'product_cell' ? t('product-information') : accessor;
      return { text: fallbackText, icon: null, label: fallbackText };
    }
    const metric = getMetricBySlug(metricSlug);
    const baseTitle = metric?.title ? t(metric.title) : accessor.replace(/_/g, ' ');
    const sources = metric?.sources || [];
    const icons = sources.map((s, i) => {
      const logo = sourceLogo(s);
      if (!logo) return null;
      return <img key={i} src={logo} alt={s} style={{ width: 14, height: 14 }} />;
    }).filter(Boolean);
    return {
      text: baseTitle,
      icon: icons.length ? (
        <MDBox sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          {icons}
        </MDBox>
      ) : null,
      label: `${baseTitle} ${sources.join(' ')}`.trim()
    };
  };

  // Define column groups using the same categories as the table
  const columnGroups = [
    {
      id: 'product',
      title: t('product-information'),
      columns: ['product_cell']
    },
    {
      id: 'revenue',
      title: 'Shopify',
      columns: ['order_count', 'customer_count', 'units_sold', 'net_revenue', 'avg_selling_price']
    },
    {
      id: 'spend',
      title: t('ad-spend'),
      columns: ['blended_catalog_spend', 'meta_ads_spend', 'gads_spend']
    },
    {
      id: 'roas',
      title: 'ROAS',
      columns: ['blended_catalog_roas']
    },
    {
      id: 'meta',
      title: t('meta-ads-metrics'),
      columns: ['meta_ads_cpc', 'meta_ads_cpm', 'meta_ads_ctr', 'meta_ads_clicks', 'meta_ads_impressions']
    },
    {
      id: 'google',
      title: t('google-ads-metrics'),
      columns: ['gads_cpc', 'gads_cpm', 'gads_ctr', 'gads_clicks', 'gads_impressions', 'gads_conversions', 'gads_conversions_value']
    }
  ];

  // Create grouped options for Autocomplete
  const groupedOptions = columnGroups.map(group => {
    const groupColumns = columns.filter(col => group.columns.includes(col.accessor));
    return {
      group: group.title,
      options: groupColumns.map(col => {
        const labelInfo = getColumnLabel(col.accessor);
        const metricSlug = accessorToMetricMap[col.accessor];
        const description = metricSlug ? getMetricBySlug(metricSlug).description : '';
        
        return {
          accessor: col.accessor,
          labelInfo: labelInfo,
          label: labelInfo.label, // For search compatibility
          description: description ? t(description) : '',
          isAlwaysVisible: alwaysVisibleColumns.includes(col.accessor)
        };
      })
    };
  }).filter(group => group.options.length > 0);

  // Flatten options for Autocomplete
  const allOptions = groupedOptions.reduce((acc, group) => [...acc, ...group.options], []);

  // Get currently selected options
  const selectedOptions = allOptions.filter(option => visibleColumns[option.accessor] !== false);

  const handleChange = (event, newValue) => {
    const newVisibility = {};
    
    // Initialize all columns as false
    columns.forEach(col => {
      newVisibility[col.accessor] = false;
    });

    // Set selected columns as true
    newValue.forEach(option => {
      newVisibility[option.accessor] = true;
    });

    // Always keep always visible columns as true
    alwaysVisibleColumns.forEach(accessor => {
      newVisibility[accessor] = true;
    });

    onVisibilityChange(newVisibility);
  };

  // Count visible columns
  const visibleCount = Object.values(visibleColumns).filter(Boolean).length;
  const totalColumns = columns.length;

  return (
    <MDBox sx={{ minWidth: 300, maxWidth: 400 }}>
      <Autocomplete
          multiple
          disableCloseOnSelect
          limitTags={3}
          getLimitTagsText={(more) => `+${more} more`}
          options={allOptions}
          filterOptions={(options, { inputValue }) => {
            if (!inputValue) return options;
            const searchTerm = inputValue.toLowerCase();
            return options.filter(option => {
              const group = groupedOptions.find(g => 
                g.options.some(opt => opt.accessor === option.accessor)
              );
              const groupName = group ? group.group.toLowerCase() : '';
              const optionLabel = option.label.toLowerCase();
              const optionDescription = option.description ? option.description.toLowerCase() : '';
              return optionLabel.includes(searchTerm) || 
                     groupName.includes(searchTerm) || 
                     optionDescription.includes(searchTerm);
            });
          }}
          groupBy={(option) => {
            const group = groupedOptions.find(g => 
              g.options.some(opt => opt.accessor === option.accessor)
            );
            return group ? group.group : '';
          }}
          getOptionLabel={(option) => option.label}
          value={selectedOptions}
          onChange={handleChange}
          isOptionEqualToValue={(option, value) => option.accessor === value.accessor}
          getOptionDisabled={(option) => option.isAlwaysVisible}
          renderInput={(params) => (
            <TextField
              {...params}
              placeholder={t("search-columns")}
              size="small"
              sx={{
                '& .MuiOutlinedInput-root': {
                  minHeight: '40px',
                  '& .MuiAutocomplete-input': {
                    padding: '4px 4px 4px 6px !important',
                  }
                }
              }}
            />
          )}
          renderOption={(props, option, { selected }) => (
            <li {...props}>
              <Checkbox
                checked={selected}
                disabled={option.isAlwaysVisible}
                size="small"
                sx={{
                  mr: 1,
                  color: option.isAlwaysVisible ? 'text.disabled' : 'grey.500',
                  '&.Mui-checked': {
                    color: 'info.main',
                  },
                }}
              />
              <MDBox sx={{ flexGrow: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                {option.labelInfo.icon && (
                  <MDBox sx={{ display: 'flex', alignItems: 'center' }}>
                    {option.labelInfo.icon}
                  </MDBox>
                )}
                <MDTypography 
                  variant="body2" 
                  sx={{ 
                    fontSize: "12px",
                    color: option.isAlwaysVisible ? 'text.disabled' : 'text.primary',
                    fontWeight: selected ? "500" : "400",
                    lineHeight: 1.4
                  }}
                >
                  {option.labelInfo.text}
                  {option.isAlwaysVisible && (
                    <MDTypography 
                      component="span" 
                      variant="caption" 
                      sx={{ 
                        fontSize: "9px", 
                        color: 'warning.main',
                        ml: 1,
                        fontStyle: 'italic',
                        fontWeight: "600"
                      }}
                    >
                      ({t('required')})
                    </MDTypography>
                  )}
                </MDTypography>
              </MDBox>
            </li>
          )}
          renderTags={(value, getTagProps) =>
            value.map((option, index) => (
              <Chip
                {...getTagProps({ index })}
                key={option.accessor}
                label={
                  <MDBox sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    {option.labelInfo.icon && (
                      <MDBox sx={{ display: 'flex', alignItems: 'center' }}>
                        {React.cloneElement(option.labelInfo.icon, { 
                          style: { width: 12, height: 12 },
                          sx: { fontSize: 12, color: '#0088FF' }
                        })}
                      </MDBox>
                    )}
                    <span>{option.labelInfo.text}</span>
                  </MDBox>
                }
                size="small"
                disabled={option.isAlwaysVisible}
                sx={{
                  height: 24,
                  fontSize: "11px",
                  '& .MuiChip-label': {
                    px: 1,
                    display: 'flex',
                    alignItems: 'center'
                  },
                  '& .MuiChip-deleteIcon': {
                    fontSize: "14px"
                  }
                }}
              />
            ))
          }
          sx={{
            '& .MuiAutocomplete-popupIndicator': {
              color: 'text.secondary'
            },
            '& .MuiAutocomplete-clearIndicator': {
              color: 'text.secondary'
            }
          }}
          ChipProps={{
            color: "info",
            variant: "outlined"
          }}
          ListboxProps={{
            sx: {
              backgroundColor: 'white !important',
              '& .MuiAutocomplete-groupLabel': {
                backgroundColor: 'white !important',
                color: 'text.primary',
                fontSize: '11px',
                fontWeight: '600',
                padding: '6px 16px',
                borderBottom: '1px solid',
                borderColor: '#f0f0f0',
                letterSpacing: '0.02em'
              },
              '& .MuiAutocomplete-groupUl': {
                paddingBottom: '6px',
                backgroundColor: 'white !important',
              },
              '& .MuiAutocomplete-option': {
                padding: '6px 16px',
                minHeight: 'auto',
                backgroundColor: 'white !important',
                '&:hover': {
                  backgroundColor: 'white !important',  
                },
                '&.Mui-focused': {
                  backgroundColor: 'white !important',
                },
                '&[aria-selected="true"]': {
                  backgroundColor: 'white !important',
                }
              }
            }
          }}
        />
    </MDBox>
  );
}
