import React, { useState, useEffect } from 'react';
import { Popover, Box, Select, MenuItem, TextField, Button, IconButton, Typography } from '@mui/material';
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import { useTranslation } from 'react-i18next';

const numericFilterOperators = [
    { label: 'op_is_greater_than', value: 'gt' },
    { label: 'op_is_less_than', value: 'lt' },
    { label: 'op_is_equal_to', value: 'equals' },
    { label: 'op_is_not_equal_to', value: 'notEquals' },
    { label: 'op_is_greater_than_equal', value: 'gte' },
    { label: 'op_is_less_than_equal', value: 'lte' },
    { label: 'op_is_between', value: 'range' },
];

const ColumnFilter = ({ header, columnId, onFilterApply, appliedValue }) => {
    const { t } = useTranslation();
    const [anchorEl, setAnchorEl] = useState(null);
    const [operator, setOperator] = useState(numericFilterOperators[0].value);
    const [value, setValue] = useState('');
    const [minValue, setMinValue] = useState('');
    const [maxValue, setMaxValue] = useState('');

    useEffect(() => {
        if (appliedValue) {
            setOperator(appliedValue.operator || numericFilterOperators[0].value);
            if (appliedValue.operator === 'range') {
                setMinValue(appliedValue.minValue || '');
                setMaxValue(appliedValue.maxValue || '');
                setValue('');
            } else {
                setValue(appliedValue.value || '');
                setMinValue('');
                setMaxValue('');
            }
        } else {
            setOperator(numericFilterOperators[0].value);
            setValue('');
            setMinValue('');
            setMaxValue('');
        }
    }, [appliedValue]);

    const handleClick = (event) => {
        event.stopPropagation(); // Prevent triggering column sort
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleApply = () => {
        if (operator === 'range') {
            if (minValue !== '' && maxValue !== '') {
                onFilterApply(columnId, { operator, minValue, maxValue });
            } else {
                onFilterApply(columnId, null); // Clear filter if range values are empty
            }
        } else {
            if (value !== '') {
                onFilterApply(columnId, { operator, value });
            } else {
                onFilterApply(columnId, null); // Clear filter if value is empty
            }
        }
        handleClose();
    };

    const handleClear = () => {
        onFilterApply(columnId, null);
        setValue('');
        setMinValue('');
        setMaxValue('');
        handleClose();
    };

    const open = Boolean(anchorEl);
    const id = open ? `filter-popover-${columnId}` : undefined;
    const isFilterActive = appliedValue && (
        (appliedValue.operator === 'range' && appliedValue.minValue !== '' && appliedValue.maxValue !== '') ||
        (appliedValue.operator !== 'range' && appliedValue.value !== '')
    );

    return (
        <Box 
            sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}
            onClick={(e) => e.stopPropagation()} // Prevent any clicks on the header from propagating
        >
            {header}
            <IconButton 
                size="small" 
                onClick={handleClick} 
                sx={{ 
                    ml: 0.5,
                    p: 0.5,
                    backgroundColor: isFilterActive ? '#e3f2fd' : 'transparent',
                    color: isFilterActive ? '#1976d2' : '#666',
                    '&:hover': {
                        backgroundColor: isFilterActive ? '#bbdefb' : '#f5f5f5',
                    }
                }} 
                onMouseDown={(e) => e.stopPropagation()} // Also prevent mousedown events
            >
                <FilterAltIcon fontSize="small" />
            </IconButton>
            <Popover
                id={id}
                open={open}
                anchorEl={anchorEl}
                onClose={handleClose}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'center',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'center',
                }}
                PaperProps={{
                    sx: {
                        backgroundColor: 'white !important',
                        border: '1px solid #e0e0e0',
                        boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.15)',
                        borderRadius: '8px',
                        overflow: 'hidden',
                    },
                }}
            >
                <Box sx={{ 
                    p: 2, 
                    display: 'flex', 
                    flexDirection: 'column', 
                    gap: 1.5, 
                    width: 240,
                    backgroundColor: 'white !important'
                }}>
                    <Box sx={{ mb: 0.5 }}>
                        <Typography variant="caption" sx={{ 
                            fontSize: '11px', 
                            fontWeight: 600, 
                            color: '#666',
                            textTransform: 'uppercase',
                            letterSpacing: '0.5px'
                        }}>
                            {t('filters-column-filter-condition', 'Filter Condition')}
                        </Typography>
                    </Box>
                    <Select
                        value={operator}
                        onChange={(e) => setOperator(e.target.value)}
                        size="medium"
                        variant="outlined"
                        sx={{
                            backgroundColor: 'white !important',
                            minHeight: '40px',
                            '& .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#e0e0e0',
                            },
                            '& .MuiSelect-select': {
                                backgroundColor: 'white !important',
                                padding: '8px 14px',
                            },
                            '&:hover .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1976d2',
                            },
                            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1976d2',
                            }
                        }}
                    >
                        {numericFilterOperators.map(op => (
                            <MenuItem key={op.value} value={op.value}>{t(op.label)}</MenuItem>
                        ))}
                    </Select>
                    
                    <Box sx={{ mt: 0.5, mb: 0.5 }}>
                        <Typography variant="caption" sx={{ 
                            fontSize: '11px', 
                            fontWeight: 600, 
                            color: '#666',
                            textTransform: 'uppercase',
                            letterSpacing: '0.5px'
                        }}>
                            {operator === 'range' ? t('filters-column-range', 'Range') : t('filters-column-value', 'Value')}
                        </Typography>
                    </Box>
                    
                    {operator === 'range' ? (
                        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                            <TextField
                                type="number"
                                placeholder="Min"
                                value={minValue}
                                onChange={(e) => setMinValue(e.target.value)}
                                size="small"
                                variant="outlined"
                                sx={{
                                    flex: 1,
                                    backgroundColor: 'white',
                                    '& .MuiOutlinedInput-root': {
                                        backgroundColor: 'white',
                                        '& fieldset': {
                                            borderColor: '#e0e0e0',
                                        },
                                        '&:hover fieldset': {
                                            borderColor: '#1976d2',
                                        },
                                        '&.Mui-focused fieldset': {
                                            borderColor: '#1976d2',
                                        }
                                    }
                                }}
                            />
                            <Typography variant="body2" sx={{ color: '#666', fontSize: '14px', px: 0.5 }}>
                                to
                            </Typography>
                            <TextField
                                type="number"
                                placeholder="Max"
                                value={maxValue}
                                onChange={(e) => setMaxValue(e.target.value)}
                                size="small"
                                variant="outlined"
                                sx={{
                                    flex: 1,
                                    backgroundColor: 'white',
                                    '& .MuiOutlinedInput-root': {
                                        backgroundColor: 'white',
                                        '& fieldset': {
                                            borderColor: '#e0e0e0',
                                        },
                                        '&:hover fieldset': {
                                            borderColor: '#1976d2',
                                        },
                                        '&.Mui-focused fieldset': {
                                            borderColor: '#1976d2',
                                        }
                                    }
                                }}
                            />
                        </Box>
                    ) : (
                        <TextField
                            type="number"
                            placeholder="Enter value"
                            value={value}
                            onChange={(e) => setValue(e.target.value)}
                            size="small"
                            variant="outlined"
                            sx={{
                                backgroundColor: 'white',
                                '& .MuiOutlinedInput-root': {
                                    backgroundColor: 'white',
                                    '& fieldset': {
                                        borderColor: '#e0e0e0',
                                    },
                                    '&:hover fieldset': {
                                        borderColor: '#1976d2',
                                    },
                                    '&.Mui-focused fieldset': {
                                        borderColor: '#1976d2',
                                    }
                                }
                            }}
                        />
                    )}
                    <Box sx={{ 
                        display: 'flex', 
                        justifyContent: 'space-between', 
                        mt: 1.5,
                        gap: 1
                    }}>
                        <Button 
                            onClick={handleClear} 
                            size="small"
                            variant="outlined"
                            sx={{
                                flex: 1,
                                borderColor: '#e0e0e0',
                                color: '#666',
                                textTransform: 'none',
                                fontSize: '12px',
                                '&:hover': {
                                    borderColor: '#ccc',
                                    backgroundColor: '#f9f9f9'
                                }
                            }}
                        >
                            {t('clear')}
                        </Button>
                        <Button 
                            onClick={handleApply} 
                            variant="contained" 
                            size="small"
                            sx={{
                                flex: 1,
                                backgroundColor: '#1976d2',
                                color: 'white !important',
                                textTransform: 'none',
                                fontSize: '12px',
                                boxShadow: 'none',
                                '&:hover': {
                                    backgroundColor: '#1565c0',
                                    boxShadow: 'none',
                                    color: 'white !important'
                                }
                            }}
                        >
                            {t('apply')}
                        </Button>
                    </Box>
                </Box>
            </Popover>
        </Box>
    );
};

export default ColumnFilter;
