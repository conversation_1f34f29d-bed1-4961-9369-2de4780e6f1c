// @mui material components
import MDBadge from "@/components/MDBadge";
import { Link } from "react-router-dom";
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import Icon from "@mui/material/Icon";
import { useTranslation } from "react-i18next";
// Images
import premiumTag from "@/assets/images/premium-tag.png";
import { useMaterialUIController } from "@/context";

function SubscriptionPlanBadge({ routeToPricing = true, onlyShowFree = false }) {
  const [controller] = useMaterialUIController();
  const { shopConfig } = controller;

  const {t} = useTranslation();
  let isFreePlan = shopConfig.planDetails?.planType == "free";
  
  let planName = shopConfig.planDetails?.planName;
  if (shopConfig.planDetails?.render?.display_name) {
    planName = shopConfig.planDetails.render.display_name;
  }

  if (!shopConfig.subscription_enabled) {
    return null;
  }

  if (isFreePlan) {
    const badgeContent = (
        <MDBox
            bgColor="light"
            width="max-content"
            px={2}
            py={0.25}
            borderRadius="section"
            lineHeight={1}
            letterSpacing={0.8}
            sx={{
                border: "1px solid",
                borderColor: "dark",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                minHeight: "1.25rem"
            }}
        >
            <MDBox
                display="flex"
                alignItems="center"
                justifyContent="center"
                gap={0.5}
                sx={{ height: "100%", width: "100%" }}
            >
                <MDTypography
                    variant="caption"
                    textTransform="uppercase"
                    fontWeight="medium"
                    color="dark"
                    sx={{
                        whiteSpace: "nowrap",
                        display: "flex",
                        alignItems: "center",
                        lineHeight: 1.4,
                        fontSize: "10px"
                    }}
                >
                    {t("free")} {t("plan")}
                </MDTypography>
                {routeToPricing && <Icon fontSize="small" sx={{ fontSize: "8px", color: "dark" }}>
                    arrow_outward
                </Icon>}
            </MDBox>
        </MDBox>
    );

    if (routeToPricing) {
        return (
            <Link to="/pricing" style={{ textDecoration: 'none', display: 'inline-flex', alignItems: 'center' }}>
                {badgeContent}
            </Link>
        );
    }

    return badgeContent;
  }

  if (!planName) {
    return null;
  }

  if (onlyShowFree) {
    return null;
  }

  return (
    <MDBadge 
      badgeContent={
        <>
          {planName}
          <img src={premiumTag} alt="premium" style={{width: "15px", height: "15px", marginLeft:"5px"}}/>
        </>
      } 
      size="lg" 
      p={0}
      variant="gradient"
      color="dark"
      sx={{color:"white", p: 0}}
    />
  );
}

export default SubscriptionPlanBadge;
