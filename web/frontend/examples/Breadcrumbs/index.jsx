// react-router-dom components
import { Link } from "react-router-dom";

// prop-types is a library for typechecking of props.
import PropTypes from "prop-types";

// @mui material components
import { Breadcrumbs as MuiBreadcrumbs, Tooltip, IconButton } from "@mui/material";
import Icon from "@mui/material/Icon";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
function Breadcrumbs({ icon, title, route, light, description }) {
  const routes = route.slice(0, -1);

  return (
    <MDBox mr={{ xs: 0, xl: 8 }}>
      <MuiBreadcrumbs
        sx={{
          "& .MuiBreadcrumbs-separator": {
            color: ({ palette: { white, grey } }) =>
              light ? white.main : grey[600],
          },
        }}
      >
        <Link to="/">
          <MDTypography
            component="span"
            variant="body2"
            color={light ? "white" : "dark"}
            opacity={light ? 0.8 : 0.5}
            sx={{ lineHeight: 0, userSelect: "none" }}
          >
            <Icon>{icon}</Icon>
          </MDTypography>
        </Link>
        {routes.map((el) => {
          // All other breadcrumb segments are non-clickable text
          return (
            <MDTypography
              key={el}
              component="span"
              variant="button"
              fontWeight="regular"
              textTransform="capitalize"
              color={light ? "white" : "secondary"}
              sx={{ lineHeight: 0, userSelect: "none" }}
            >
              {el.replace("-", " ")}
            </MDTypography>
          );
        })}
        <MDBox display="flex" alignItems="center" gap={0.5}>
          <MDTypography
            variant="button"
            fontWeight="medium"
            textTransform="uppercase"
            color={light ? "white" : "dark"}
            sx={{ lineHeight: 0, userSelect: "none" }}
          >
            {title.replace("-", " ")}
          </MDTypography>
          {description && (
            <Tooltip
              title={description}
              placement="bottom"
              arrow
              sx={{
                '& .MuiTooltip-tooltip': {
                  maxWidth: 400,
                  fontSize: '0.875rem',
                  whiteSpace: 'pre-line',
                  lineHeight: 1.4,
                }
              }}
            >
              <IconButton
                size="small"
                sx={{
                  padding: 0.25,
                  color: light ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
                  '&:hover': {
                    color: light ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.8)',
                    backgroundColor: 'transparent',
                  },
                }}
              >
                <Icon sx={{ fontSize: '1rem' }}>info_outlined</Icon>
              </IconButton>
            </Tooltip>
          )}
        </MDBox>
      </MuiBreadcrumbs>
    </MDBox>
  );
}

// Setting default values for the props of Breadcrumbs
Breadcrumbs.defaultProps = {
  light: false,
  description: null,
};

// Typechecking props for the Breadcrumbs
Breadcrumbs.propTypes = {
  icon: PropTypes.node.isRequired,
  title: PropTypes.string.isRequired,
  route: PropTypes.oneOfType([PropTypes.string, PropTypes.array]).isRequired,
  light: PropTypes.bool,
  description: PropTypes.string,
};

export default Breadcrumbs;
