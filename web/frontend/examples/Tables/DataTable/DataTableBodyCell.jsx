// prop-types is a library for typechecking of props
import PropTypes from "prop-types";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";

function DataTableBodyCell({ noBorder, align, children, cell }) {
  return (
    <MDBox
      component="td"
      className={`${cell.column.className ?? ""} td`}
      textAlign={align}
      py={1.5}
      px={3}
      sx={({ palette: { light }, typography: { size }, borders: { borderWidth } }) => ({
        fontSize: size.sm,
        borderBottom: noBorder ? "none" : `${borderWidth[1]} solid ${light.main}`,
        verticalAlign: 'middle', // Ensure consistent vertical alignment
      })}

      style={{
        background: cell.isGrouped
          ? '#f0f2f5'
          : cell.isAggregated
          ? 'white'
          : cell.isPlaceholder
              ? 'rgba(0, 0, 0, 0.001)'// '#ff000042'
              : 'rgba(0, 0, 0, 0.001)',
      }}
    >
      <MDBox
        width="max-content"
        color="text"
        sx={{ 
          display: "flex",
          alignItems: "center",
          height: "100%"
        }}
      >
        {children}
      </MDBox>
    </MDBox>
  );
}

// Setting default values for the props of DataTableBodyCell
DataTableBodyCell.defaultProps = {
  noBorder: false,
  align: "left",
};

// Typechecking props for the DataTableBodyCell
DataTableBodyCell.propTypes = {
  children: PropTypes.node.isRequired,
  noBorder: PropTypes.bool,
  align: PropTypes.oneOf(["left", "right", "center"]),
};

export default DataTableBodyCell;
