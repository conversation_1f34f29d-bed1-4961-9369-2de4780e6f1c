import React, { useState, useEffect, useLayoutEffect, useCallback, Fragment, useRef } from "react";
import { useLocation, NavLink, useNavigate } from "react-router-dom";

// @mui material components
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import IconButton from "@mui/material/IconButton";
import Avatar from "@mui/material/Avatar";
import Popover from "@mui/material/Popover";
import Divider from "@mui/material/Divider";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";
import InputAdornment from "@mui/material/InputAdornment";
import SubscriptionPlanBadge from "@/components/SubscriptionPlanBadge";

// Material Dashboard 2 PRO React context
import { useMaterialUIController, setSelectedShop, tracker, setActiveCategory } from "@/context";
import MDBox from "@/components/MDBox";
import { useTranslation } from "react-i18next";

// Import routes for category structure
import routes from "@/router";

// Dialogs and constants
import { AddStoreDialog } from '@/layouts/authentication/add-store';
import IdentificationModal from '@/components/IdentificationModal';
import PaywallDialog from '@/components/Paywall';
import NiceModal from '@ebay/nice-modal-react';
import { WORKSPACE_ROLE_ADMIN, WORKSPACE_ROLE_OWNER } from "@/constants";

// Import logos for light and dark sidebars
import brandDark from "@/assets/images/brand-short-3.png";
import brandLight from "@/assets/images/brand-short-2.png";
import premiumTag from "@/assets/images/premium-tag.png";
import MDButton from "@/components/MDButton";

// Sidebar utilities
import { getActiveCategoryFromRoute, SIDEBAR_WIDTH, EXPANDED_PANEL_WIDTH } from "@/utils/sidebarUtils";


// @phosphor-icons/react imports (kept for NewSidenav UI elements)

import { 
  MagnifyingGlassIcon,
  HouseIcon,
  XIcon,
  PlusIcon,
  SquaresFourIcon,
  CaretRightIcon,
  PushPinIcon,
  DotsSixVerticalIcon,
  PuzzlePieceIcon,
  RobotIcon
} from '@phosphor-icons/react';

// Helper function to extract categories from routes
const getCategoriesFromRoutes = (allRoutes) => {
  const categories = {};
  
  allRoutes.forEach(route => {
    if (route.type === "category") {
      categories[route.categoryKey] = {
        name: route.name,
        icon: route.icon,
        items: route.items || []
      };
    }
  });
  
  return categories;
};

/**
 * NavigationItem - Reusable component that preserves original styling exactly
 * Used by pinned items, main items, and sub-items to ensure consistency
 */
const NavigationItem = React.memo(({ 
  item, 
  isActive, 
  isDarkMode, 
  safeTranslate,
  // Pin functionality
  togglePinItem,
  pinnedItems,
  showPinButton = true,
  // Drag and drop (for pinned items)
  isDraggable = false,
  dragProps = {},
  // Chevron (for expandable items)
  hasChevron = false,
  chevronProps = {},
  // Styling variants
  variant = 'main', // 'main', 'sub', 'pinned'
  // Click handlers
  onClick,
  // Additional content (like New badge)
  additionalContent
}) => {
  const isPinned = pinnedItems?.some(p => p.route === item.route);
  
  // Get the exact original className based on variant
  const getClassName = () => {
    const baseClasses = `
      block transition-all duration-150 no-underline
      relative group rounded-md
    `;
    
    switch (variant) {
      case 'sub':
        return `${baseClasses} pr-0.5 pl-2 py-2.5 min-h-[35px] ${
          isActive(item.route, false)
            ? isDarkMode
              ? 'bg-gray-700 text-white shadow-sm font-medium'
              : 'bg-blue-100 text-gray-800 shadow-sm font-medium'
            : isDarkMode
              ? 'text-white hover:bg-gray-700 hover:text-white'
              : 'text-gray-600 hover:bg-gray-100 hover:text-gray-800'
        }`;
      
      case 'pinned':
        return `${baseClasses} px-0.5 py-2.5 min-h-[35px] ${
          dragProps.isDragging ? 'opacity-50 scale-95' : 'opacity-100 scale-100'
        } ${
          isActive(item.route, false)
            ? isDarkMode
              ? 'bg-gray-700 text-white shadow-sm'
              : 'bg-blue-100 text-gray-800 shadow-sm'
            : isDarkMode
              ? 'text-white hover:text-white hover:bg-gray-700'
              : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
        }`;
      
      default: // main
        return `${baseClasses} px-0.5 pl-2 py-2.5 min-h-[35px] ${
          item.route && isActive(item.route, false)
            ? isDarkMode
              ? 'bg-gray-700 text-white shadow-sm'
              : 'bg-blue-100 text-gray-800 shadow-sm'
            : item.hasSubItems
              ? isDarkMode
                ? 'cursor-pointer text-white hover:text-white hover:bg-gray-700'
                : 'cursor-pointer text-gray-600 hover:text-gray-800 hover:bg-gray-100'
              : isDarkMode
                ? 'text-white hover:text-white hover:bg-gray-700'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
        }`;
    }
  };

  // Get exact original sx styles
  const getSxStyles = () => {
    const baseStyles = {
      minHeight: '35px',
      transition: 'background-color 0.15s cubic-bezier(0.4, 0, 0.2, 1)',
      userSelect: 'none', // Prevent text selection on entire item
      WebkitUserSelect: 'none', // Safari
      MozUserSelect: 'none', // Firefox
      msUserSelect: 'none', // IE/Edge
      '&:hover': {
        backgroundColor: isDarkMode ? 'rgba(55, 65, 81, 0.5)' : 'rgba(243, 244, 246, 0.8)'
      }
    };

    if (variant === 'pinned') {
      return {
        ...baseStyles,
        transform: dragProps.isDragging ? 'scale(0.95)' : 'scale(1)',
        boxShadow: dragProps.isDragging ? '0 8px 25px rgba(0,0,0,0.15)' : 'none',
      };
    }

    return baseStyles;
  };

  // Get active indicator styles (exact original)
  const getActiveIndicatorClass = () => {
    if (variant === 'sub') {
      return `absolute left-0 top-1.5 bottom-1.5 w-0.5 rounded-full ${
        isDarkMode ? 'bg-gray-500' : 'bg-blue-500'
      }`;
    }
    return `absolute left-0 top-2 bottom-2 w-1 rounded-full ${
      isDarkMode ? 'bg-gray-500' : 'bg-blue-500'
    }`;
  };

  // Get icon container style (exact original)
  const getIconContainerStyle = () => {
    if (variant === 'sub') {
      return { 
        width: '28px', 
        display: 'flex', 
        alignItems: 'center' 
      }; // Sub-item indent with proper alignment
    }
    return { 
      width: '20px', 
      display: 'flex', 
      justifyContent: 'flex-start', 
      alignItems: 'center',
      marginRight: '6px' 
    };
  };

  // Get typography styles with consistent colors
  const getTypographyStyles = () => {
    const baseStyles = {
      fontSize: '0.85rem',
      fontWeight: 400,
      lineHeight: variant === 'main' ? 1.4 : 1.4,
      letterSpacing: '0.15px',
      color: variant === 'pinned' 
        ? (isDarkMode ? '#FFFFFF !important' : '#374151 !important') // Force explicit color for pinned items
        : 'inherit',
      whiteSpace: 'nowrap',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      flex: 1,
      userSelect: 'none', // Prevent text selection
      WebkitUserSelect: 'none', // Safari
      MozUserSelect: 'none', // Firefox
      msUserSelect: 'none' // IE/Edge
    };

    if (variant === 'sub') {
      return {
        ...baseStyles,
        fontSize: '0.85rem' // Match original sub-item font size
      };
    }

    return baseStyles;
  };

  // Get typography className (exact original)
  const getTypographyClassName = () => {
    if (variant === 'main') {
      return `transition-all duration-150 ${
        item.route && isActive(item.route, false)
          ? 'font-medium' 
          : item.hasSubItems
            ? 'tracking-wide font-medium'
            : 'font-medium'
      }`;
    }
    return 'transition-all duration-150';
  };

  return (
    <Box
      component={item.route ? NavLink : "div"}
      to={item.route || undefined}
      onClick={onClick}
      className={getClassName()}
      sx={getSxStyles()}
      {...(isDraggable ? {
        draggable: true,
        onDragStart: dragProps.onDragStart,
        onDragOver: dragProps.onDragOver,
        onDragLeave: dragProps.onDragLeave,
        onDrop: dragProps.onDrop,
        onDragEnd: dragProps.onDragEnd
      } : {})}
    >

      <Box className={`flex items-center ${variant === 'pinned' ? 'justify-between cursor-move flex-1' : 'justify-between'}`}>
        <Box className="flex items-center" style={{ minWidth: 0, flex: 1 }}>
          {/* Icon container - exact original */}
          <Box style={getIconContainerStyle()}>
            {/* Drag handle for pinned items - exact original */}
            {variant === 'pinned' && (
              <DotsSixVerticalIcon color={isDarkMode ? '#6B7280' : '#9CA3AF'} size={16} style={{ 
                opacity: dragProps.isDragging ? 1 : 0,
                '.group:hover &': { opacity: 0.5 },
                cursor: dragProps.isDragging ? 'grabbing' : 'grab',
                transition: 'opacity 200ms ease',
                position: 'absolute',
                zIndex: 1
              }} />
            )}
            
            {/* Item icon - reduced size */}
            {variant !== 'sub' && item.icon && (
              <item.icon size={20} />
            )}
          </Box>
          
          {/* Text - exact original */}
          <Typography 
            variant="caption" 
            className={getTypographyClassName()}
            sx={getTypographyStyles()}
          >
            {safeTranslate(item.name)}
          </Typography>
          
          {/* Additional content - exact original */}
          {additionalContent}
        </Box>
        
        {/* Right side actions - reduced width for more text space */}
        <Box style={{ width: '36px', display: 'flex', alignItems: 'center', justifyContent: 'flex-end', gap: '2px' }}>
          {/* Pin button - exact original */}
          {showPinButton && item.route && (
            <IconButton
              size="small"
              onClick={(e) => {
                e.preventDefault();
                togglePinItem(item);
              }}
              sx={{ 
                p: 0.5,
                opacity: variant === 'pinned' ? 0.7 : (isPinned ? 0.7 : 0),
                '.group:hover &': { opacity: 1 }
              }}
            >
              <PushPinIcon color={isPinned
                  ? isDarkMode ? '#F59E0B' : '#D97706'
                  : isDarkMode ? '#6B7280' : '#9CA3AF'} size={16} />
            </IconButton>
          )}
          
          {/* Chevron for expandable items - exact original */}
          {hasChevron && (
            <Box className={`transition-all duration-150 ${
              chevronProps.isExpanded ? 'rotate-90' : ''
            }`} sx={{ marginRight: '10px' }}>
              <CaretRightIcon size={14} color={isDarkMode ? '#9CA3AF' : '#6B7280'} />
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  );
});

/**
 * NewSidenav - Modern sidebar navigation component with expandable panels
 * Features:
 * - Collapsible navigation with category-based organization
 * - Expandable secondary panels for detailed navigation
 * - Store selection and management
 * - Route-based active state management
 * - Responsive design with proper accessibility
 */
function NewSidenav() {
  const [controller, dispatch] = useMaterialUIController();
  const { selectedShop, loginConfig, selectedWorkspaceId, shopConfig, activeCategory } = controller;
  const location = useLocation();
  const { pathname } = location;
  const { t } = useTranslation();
  const navigate = useNavigate();
  
  // Extract categories from routes
  const categories = getCategoriesFromRoutes(routes);

  // Function to render sidebar content dynamically from routes
  const renderSidebarFromRoutes = () => {
    const topRoutes = routes.filter(route => route.stick === "top");
    const bottomRoutes = routes.filter(route => route.stick === "bottom");

    return (
      <>
        {/* Top Section - All top elements including categories */}
        <Box className="flex-1 px-2 py-3 space-y-4">
          {topRoutes.map(route => renderRouteElement(route))}
        </Box>

        {/* Bottom Section - All bottom elements including categories */}
        <Box className="px-2 py-3 space-y-4">
          {bottomRoutes.map(route => renderRouteElement(route))}
        </Box>
      </>
    );
  };

  // Function to render individual route elements
  const renderRouteElement = (route) => {
    if (!route || !route.type) return null;

    // Handle feature gating
    if (route.feature && !shopConfig.planDetails?.features?.[route.feature]) {
      return null;
    }

    switch (route.type) {
      case 'divider':
        return (
          <Box 
            key={route.key}
            className={`mx-2 border-t ${
              isDarkMode ? 'border-gray-700' : 'border-gray-200'
            }`} 
          />
        );

      case 'store-section':
        return (
          <Box key={route.key} className="relative">
            {renderStoreSection()}
          </Box>
        );

      case 'category':
        return (
          <CategoryButton
            key={route.categoryKey}
            categoryKey={route.categoryKey}
            category={{
              name: route.name,
              icon: route.icon,
              items: route.items || [],
              route: route.route // Pass the category route if it exists
            }}
          />
        );

      default:
        return null;
    }
  };

  // Extract store section rendering to separate function
  const renderStoreSection = () => (
    <>
      {/* Active Store Display with Click Popup */}
      <Box className="flex flex-col items-center space-y-1">
        <Box
          onClick={handleStorePopupOpen}
          className={`
            size-8 mx-auto rounded-full cursor-pointer
            flex items-center justify-center transition-colors duration-200
            relative group
            ${isStorePopupOpen ? 'ring-2 ring-blue-400' : 'hover:ring-2 hover:ring-gray-300'}
          `}
          sx={{
            backgroundColor: getStoreAvatarColor(activeShop)
          }}
          role="button"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleStorePopupOpen(e);
            }
          }}
        >
          {/* Store initials in circular avatar */}
          <Typography
            variant="body2"
            className="font-bold text-white"
            sx={{ fontSize: '0.75rem' }}
          >
            {getStoreInitials(activeShop)}
          </Typography>
        </Box>
        
        {/* Store name below avatar */}
        <Typography 
          variant="caption" 
          className={`text-center font-medium ${
            isDarkMode ? 'text-white' : 'text-gray-700'
          }`}
          onClick={handleStorePopupOpen}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleStorePopupOpen(e);
            }
          }}
          role="button"
          tabIndex={0}
          sx={{ 
            fontSize: '0.7rem',
            lineHeight: 1.2,
            letterSpacing: '0.15px',
            wordBreak: 'break-word',
            hyphens: 'auto',
            fontWeight: 400,
            marginTop: '0px',
            userSelect: 'none',
            WebkitUserSelect: 'none',
            MozUserSelect: 'none',
            msUserSelect: 'none'
          }}
        >
          {t("store")}
        </Typography>
      </Box>
    </>
  );
  
  // Helper function to safely translate text, fallback to original if translation doesn't exist
  const safeTranslate = (key) => {
    try {
      const translated = t(key);
      // If the translation is the same as the key, it means no translation exists
      return translated === key ? key : translated;
    } catch (error) {
      return key;
    }
  };
  
  // Determine if sidebar should be dark mode
  const isDarkMode = true;
  
  const [isAnimating, setIsAnimating] = useState(false);
  const [storePopupAnchor, setStorePopupAnchor] = useState(null);
  const [storeSearchQuery, setStoreSearchQuery] = useState("");
  
  // New state for improved sidebar features
  const [collapsedSections, setCollapsedSections] = useState(new Set());
  const [pinnedSectionCollapsed, setPinnedSectionCollapsed] = useState(() => {
    // Load pinned section collapsed state from localStorage
    const saved = localStorage.getItem('dashr_pinned_section_collapsed');
    return saved ? JSON.parse(saved) : false;
  });
  const [pinnedItems, setPinnedItems] = useState(() => {
    // Load pinned items from localStorage
    const saved = localStorage.getItem('dashr_pinned_items');
    return saved ? JSON.parse(saved) : [];
  });

  
  // Drag and drop state
  const [draggedIndex, setDraggedIndex] = useState(null);
  const [dragOverIndex, setDragOverIndex] = useState(null);
  
  // Set active category based on current route
  useEffect(() => {
    // TODO - remove this hardcoded pathname check
    if (pathname === '/dashboard' || pathname === '/integrations') {
      setActiveCategory(dispatch, null);
      return;
    }
    const routeCategory = getActiveCategoryFromRoute(pathname);
    // Only update activeCategory if we have a route match
    // Don't close the panel for routes that don't have a category mapping
    if (routeCategory) {
      setActiveCategory(dispatch, routeCategory);
    }
  }, [pathname]);

  // Calculate total sidebar width for layout based on actual state
  const totalSidebarWidth = activeCategory
    ? SIDEBAR_WIDTH + EXPANDED_PANEL_WIDTH  // Primary + Secondary
    : SIDEBAR_WIDTH; // Primary only

  // Keep latest width in a ref so listeners don't need to be re-registered
  const widthRef = useRef(totalSidebarWidth);
  useEffect(() => {
    widthRef.current = totalSidebarWidth;
  }, [totalSidebarWidth]);

  // Emit sidebar width changes to parent - dispatch immediately on every render to prevent layout shift
  useLayoutEffect(() => {
    const event = new CustomEvent('sidebarWidthChange', {
      detail: { width: totalSidebarWidth }
    });
    window.dispatchEvent(event);
  }); // No dependency array - run on every render to ensure width is always correct

  // Store switching logic from old sidebar
  const handleSelectedShopChange = (selectedShop) => {
    if (!selectedShop) {
      return;
    }

    tracker.event("Switch Shop", { shop: selectedShop });
    
    if (selectedShop === 'add-new-store') {
      // show a dialog to add a new store
      NiceModal.show(AddStoreDialog, {});
      handleStorePopupClose();
      return;
    }

    if (selectedShop === 'multi-store-sign-up') {
      // show a dialog to multi store login
      NiceModal.show(IdentificationModal, {});
      handleStorePopupClose();
      return;
    }

    if (selectedShop === 'multi-store-sign-in') {
      // show a dialog to multi store login
      NiceModal.show(IdentificationModal, { preferSignIn: true });
      handleStorePopupClose();
      return;
    }

    setSelectedShop(dispatch, selectedShop);
    handleStorePopupClose();
  };

  // Handle navigation to workspace settings
  const handleManageWorkspaces = () => {
    tracker.event("Navigate to Workspace Settings", { from: "new_sidenav" });
    handleStorePopupClose();
    navigate("/workspace-settings");
  };

  const handleAddNewStore = () => {
    if (isShopFlow) {
      // In shop flow, encourage login instead of directly adding store
      NiceModal.show(IdentificationModal, {});
    } else {
      // In user flow, directly show add store dialog
      NiceModal.show(AddStoreDialog, {});
    }
    handleStorePopupClose();
  };

  // Handle store popup with MUI Popover
  const handleStorePopupOpen = (event) => {
    setStorePopupAnchor(event.currentTarget);
  };

  const handleStorePopupClose = () => {
    setStorePopupAnchor(null);
    setStoreSearchQuery(""); // Reset search when closing
  };

  const isStorePopupOpen = Boolean(storePopupAnchor);

  // Determine if this is shop flow or user flow
  const isShopFlow = !loginConfig.userData || !loginConfig.userData.email;
  const activeWorkspace = (loginConfig?.workspaces ?? []).find((w) => w.workspace_id === selectedWorkspaceId);

  // Get shop options based on flow
  let shopOptions = [];
  if (isShopFlow) {
    shopOptions = loginConfig.shopOptions ?? [];
  } else {
    shopOptions = activeWorkspace?.shops ?? [];
  }

  // Find active shop
  let activeShop = shopOptions.find((shop) => shop.myshopify_domain === selectedShop);
  if (loginConfig.admin && !activeShop) {
    activeShop = (loginConfig.adminShopOptions ?? []).find((shop) => shop.myshopify_domain === selectedShop);
  }

  // Get store initials for display
  const getStoreInitials = (shop) => {
    if (!shop) return "S";
    const name = shop.name || shop.myshopify_domain || "";
    return name.split(' ').map(word => word[0]).join('').toUpperCase().slice(0, 2) || "S";
  };

  // Generate consistent color for store avatar based on shop domain
  const getStoreAvatarColor = (shop) => {
    if (!shop || !shop.myshopify_domain) return isDarkMode ? '#64748b' : '#3b82f6';
    
    // Muted colors for dark mode, bright colors for light mode
    const lightColors = [
      '#ef4444', '#f97316', '#f59e0b', '#eab308', '#84cc16',
      '#22c55e', '#10b981', '#14b8a6', '#06b6d4', '#0ea5e9',
      '#3b82f6', '#6366f1', '#8b5cf6', '#a855f7', '#d946ef',
      '#ec4899', '#f43f5e'
    ];
    
    const darkColors = [
      '#7f1d1d', '#9a3412', '#92400e', '#a16207', '#365314',
      '#14532d', '#064e3b', '#134e4a', '#164e63', '#0c4a6e',
      '#1e3a8a', '#312e81', '#581c87', '#701a75', '#86198f',
      '#9d174d', '#be123c'
    ];
    
    const colors = isDarkMode ? darkColors : lightColors;
    let hash = 0;
    for (let i = 0; i < shop.myshopify_domain.length; i++) {
      hash = shop.myshopify_domain.charCodeAt(i) + ((hash << 5) - hash);
    }
    return colors[Math.abs(hash) % colors.length];
  };

  // Filter shop options based on search query
  const filteredShopOptions = shopOptions.filter(shop => {
    if (!storeSearchQuery) return true;
    const searchLower = storeSearchQuery.toLowerCase();
    const name = (shop.name || "").toLowerCase();
    const domain = (shop.myshopify_domain || "").toLowerCase();
    return name.includes(searchLower) || domain.includes(searchLower);
  });

  // Secondary sidebar remains open by default - removed click-outside behavior



  // Listen once for requests to update sidebar width
  useEffect(() => {
    const handleSidebarWidthRequest = () => {
      const event = new CustomEvent('sidebarWidthChange', {
        detail: { width: widthRef.current }
      });
      window.dispatchEvent(event);
    };

    window.addEventListener('requestSidebarWidth', handleSidebarWidthRequest);
    
    return () => {
      window.removeEventListener('requestSidebarWidth', handleSidebarWidthRequest);
    };
  }, []);

  const handleCategoryClick = useCallback((categoryKey) => {
    if (isAnimating) return; // Prevent clicks during animation
    
    setIsAnimating(true);
    
    // Get the category data to check if it has items
    const category = categories[categoryKey];
    const hasItems = category && category.items && category.items.length > 0;
    
    // Toggle the category: if it's already active, close it; otherwise, open it only if it has items
    if (activeCategory === categoryKey) {
      setActiveCategory(dispatch, null);
    } else if (hasItems) {
      setActiveCategory(dispatch, categoryKey);
    }
    // If category has no items, don't open the expanded panel
    
    // Reset animation flag after transition completes
    setTimeout(() => setIsAnimating(false), 300);
  }, [isAnimating, activeCategory, categories]);

  // Toggle section collapse state
  const toggleSectionCollapse = useCallback((sectionKey) => {
    setCollapsedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sectionKey)) {
        newSet.delete(sectionKey);
      } else {
        newSet.add(sectionKey);
      }
      return newSet;
    });
  }, []);

  // Toggle pinned section collapse
  const togglePinnedSection = useCallback(() => {
    setPinnedSectionCollapsed(prev => {
      const newState = !prev;
      localStorage.setItem('dashr_pinned_section_collapsed', JSON.stringify(newState));
      return newState;
    });
  }, []);



  // Toggle item pinning
  const togglePinItem = useCallback((item) => {
    setPinnedItems(prev => {
      const isPinned = prev.some(p => p.route === item.route);
      // Only save serializable data to localStorage
      const itemToSave = {
        route: item.route,
        name: item.name,
        key: item.key
      };
      const newPinned = isPinned 
        ? prev.filter(p => p.route !== item.route)
        : [...prev, itemToSave]; // No limit on pinned items
      
      // Save to localStorage
      localStorage.setItem('dashr_pinned_items', JSON.stringify(newPinned));
      return newPinned;
    });
  }, []);

  // Drag and drop for pinned items
  const handleDragStart = useCallback((e, index) => {
    setDraggedIndex(index);
    e.dataTransfer.setData('text/plain', index.toString());
    e.dataTransfer.effectAllowed = 'move';
    // Add some visual feedback
    e.dataTransfer.setDragImage(e.currentTarget, 0, 0);
  }, []);

  const handleDragOver = useCallback((e, index) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverIndex(index);
  }, []);

  const handleDragLeave = useCallback(() => {
    setDragOverIndex(null);
  }, []);

  const handleDrop = useCallback((e, targetIndex) => {
    e.preventDefault();
    const sourceIndex = parseInt(e.dataTransfer.getData('text/plain'), 10);
    
    // Reset drag states
    setDraggedIndex(null);
    setDragOverIndex(null);
    
    if (sourceIndex !== targetIndex) {
      setPinnedItems(prev => {
        const newPinned = [...prev];
        const [movedItem] = newPinned.splice(sourceIndex, 1);
        newPinned.splice(targetIndex, 0, movedItem);
        
        // Save to localStorage
        localStorage.setItem('dashr_pinned_items', JSON.stringify(newPinned));
        return newPinned;
      });
    }
  }, []);

  const handleDragEnd = useCallback(() => {
    // Reset all drag states when drag ends
    setDraggedIndex(null);
    setDragOverIndex(null);
  }, []);

  const isActive = useCallback((route, isCategoryRoute = false) => {
    if (pathname === route) return true;
    
    // For category routes, allow hierarchical matching
    if (isCategoryRoute) {
      const routePattern = new RegExp(`^${route.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}([/?]|$)`);
      return routePattern.test(pathname);
    }
    
    // For item routes, only exact matches
    return false;
  }, [pathname]);

  // Check if a category has any active routes
  const hasActiveRoute = useCallback((categoryItems) => {
    return categoryItems.some(item => {
      if (item.route) {
        return isActive(item.route, false); // Item routes should only match exactly
      }
      if (item.subItems) {
        return item.subItems.some(subItem => isActive(subItem.route, false));
      }
      return false;
    });
  }, [isActive]);

  const CategoryButton = ({ categoryKey, category }) => {
    const isSelected = activeCategory === categoryKey; // Currently exploring this category
    const hasActiveRoutes = hasActiveRoute(category.items); // Has active page/route
    
    // Check if current route matches the category route
    const isCategoryRouteActive = category.route && isActive(category.route, true);
    const hasActiveItems = hasActiveRoutes || isCategoryRouteActive;
    
    const handleClick = () => {
      // If category has a route, navigate to it
      if (category.route) {
        navigate(category.route);
      }
      
      // Toggle the expanded panel for this category
      handleCategoryClick(categoryKey);
    };
    
    const buttonContent = (
      <Box 
        className="flex flex-col items-center space-y-1 cursor-pointer group"
        onClick={handleClick}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleClick();
          }
        }}
        sx={{
          userSelect: 'none',
          WebkitUserSelect: 'none', 
          MozUserSelect: 'none',
          msUserSelect: 'none'
        }}
      >
        <Box
          className={`
            w-10 h-10 mx-auto rounded-lg
            flex items-center justify-center transition-all duration-200 motion-reduce:transition-none
            relative
            focus:outline-none focus:ring-2 focus:ring-offset-2
            ${isDarkMode
              ? hasActiveItems
                ? 'bg-gray-700 shadow-sm  focus:ring-gray-400' // Active: same as hover, no blue
                : isSelected
                  ? 'bg-gray-700 shadow-sm focus:ring-gray-400' // Exploring - darker gray
                  : 'group-hover:bg-gray-700 group-hover:shadow-sm focus:ring-gray-400'
              : hasActiveItems
                ? 'bg-blue-100 shadow-lg shadow-blue-500/10 focus:ring-blue-400' // Enhanced shadow for active state  
                : isSelected
                  ? 'bg-gray-200 shadow-sm focus:ring-gray-400' // Exploring - darker gray
                  : 'group-hover:bg-gray-100 group-hover:shadow-sm focus:ring-gray-400'
            }
          `}
        >
          {/* Active indicator line - only show for active routes, not just exploring */}
          {hasActiveItems && (
            <Box className={`absolute left-0 top-0 bottom-0 w-1 rounded-l-lg ${
              isDarkMode ? 'bg-blue-500 shadow-sm' : 'bg-blue-500 shadow-sm'
            }`} />
          )}
          
          <Box className={`
            text-xl transition-all duration-200 flex items-center justify-center
            ${isDarkMode
              ? hasActiveItems
                ? 'text-blue-200' // Active: neutral tone
                : isSelected
                  ? 'text-gray-200' // Exploring - darker gray
                  : 'text-gray-400 group-hover:text-blue-400'
              : hasActiveItems
                ? 'text-blue-700' // Higher contrast for active state
                : isSelected
                  ? 'text-gray-500' // Exploring - darker gray
                  : 'text-gray-500 group-hover:text-blue-500'
            }
          `}>
            <category.icon />
          </Box>
        </Box>
        
        {/* Category name below icon - improved typography */}
        <Typography 
          variant="caption" 
          className={`text-center font-medium ${
            isDarkMode ? 'text-white' : 'text-gray-700'
          }`}
          sx={{ 
            fontSize: '0.7rem',
            lineHeight: 1.2,
            letterSpacing: '0.15px',
            whiteSpace: 'nowrap',

            fontWeight: 400,
            marginTop: '0px',
            userSelect: 'none',
            WebkitUserSelect: 'none',
            MozUserSelect: 'none',
            msUserSelect: 'none',
            maxWidth: '60px'
          }}
        >
          {safeTranslate(category.name)}
        </Typography>
      </Box>
    );

    return buttonContent;
  };



  return (
    <>
      {/* Main Thin Sidebar */}
      <Box
        className={`
          fixed top-0 left-0 h-screen
          border-r 
          flex flex-col
          ${isDarkMode 
            ? 'bg-gray-900 border-gray-700' 
            : 'bg-gray-50 border-gray-200'
          }
        `}
        style={{ 
          width: SIDEBAR_WIDTH,
          zIndex: 1300, // Higher than navbar and content
          backgroundColor: '#171d26'
        }}
      >
        {/* Logo Section */}
        <Box className={`flex items-center justify-center h-16 border-b ${
          isDarkMode ? 'border-gray-700' : 'border-gray-200'
        }`}>
          <Box
            component={NavLink}
            to="/dashboard"
            className="cursor-pointer flex items-center justify-center"
          >
            <img
              src={isDarkMode ? brandLight : brandDark}
              alt="DataDrew"
              className="w-8 h-8 hover:opacity-80 transition-opacity duration-200"
            />
          </Box>
        </Box>

        {/* Dynamic Sidebar Content from Routes */}
        {renderSidebarFromRoutes()}
        

      </Box>

      {/* Store Management Popover */}
      <Popover
        open={isStorePopupOpen}
        anchorEl={storePopupAnchor}
        onClose={handleStorePopupClose}
        anchorOrigin={{
          vertical: 'center',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'center',
          horizontal: 'left',
        }}
        slotProps={{
          paper: {
            sx: {
              backgroundColor: isDarkMode ? 'rgba(23, 29, 38, 0.98)' : 'rgba(255, 255, 255, 0.98)',
              backdropFilter: 'blur(10px)',
              border: `1px solid ${isDarkMode ? 'rgba(148, 163, 184, 0.2)' : 'rgba(229, 231, 235, 0.6)'}`,
              borderRadius: '10px',
              boxShadow: isDarkMode ? '0 12px 30px rgba(0,0,0,0.45)' : '0 10px 25px rgba(0, 0, 0, 0.15)',
              width: '320px',
              height: isShopFlow ? 'auto' : '450px',
              overflow: 'hidden',
              marginLeft: '30px', // Add more space to open further to the right
            }
          }
        }}
      >
        <Box sx={{ p: 2, height: isShopFlow ? 'auto' : '100%', overflow: 'hidden', display: 'flex', flexDirection: 'column', color: isDarkMode ? '#E5E7EB' : undefined }}>
          {/* Store Info Header */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, mb: 2, flexShrink: 0 }}>
            <Avatar
              sx={{
                backgroundColor: getStoreAvatarColor(activeShop),
                width: 36,
                height: 36,
                fontSize: '0.75rem',
                fontWeight: 'bold',
                textTransform: "uppercase",
                color: 'white',
                flexShrink: 0,
              }}
            >
              {getStoreInitials(activeShop)}
            </Avatar>
            <Box sx={{ flex: 1, minWidth: 0 }}>
              <Typography 
                variant="body2" 
                sx={{ 
                  lineHeight: 1.3, 
                  color: isDarkMode ? '#E5E7EB' : '#1F2937', 
                  fontWeight: 600,
                  fontSize: '0.875rem',
                  mb: 0.25,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}
              >
                {activeShop?.name || activeShop?.myshopify_domain || t("no-shop-selected")}
              </Typography>
              <SubscriptionPlanBadge routeToPricing={false} onlyShowFree={true}/>
              {!isShopFlow && (
                <Typography 
                  variant="caption" 
                  sx={{ 
                    lineHeight: 1.2, 
                    color: isDarkMode ? '#9CA3AF' : '#6B7280',
                    fontSize: '0.75rem',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    display: 'block'
                  }}
                >
                  {activeWorkspace?.workspace_name || activeWorkspace?.name || t("my-workspace")}
                </Typography>
              )}
            </Box>
          </Box>
          
          {/* STORES Header - Only show in user flow */}
          {!isShopFlow && (
            <>
              <Box sx={{ mb: 1, flexShrink: 0 }}>
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: "0.7rem",
                    fontWeight: "normal",
                    textTransform: "uppercase",
                    letterSpacing: "1px",
                    color: isDarkMode ? '#9CA3AF' : '#6b7280'
                  }}
                >
                  {t("stores-in-your-workspace").toUpperCase()}
                </Typography>
              </Box>
              
              {/* Search bar - only show if more than one store */}
              {shopOptions.length > 1 && (
                <Box sx={{ mb: 2, flexShrink: 0 }}>
                  <TextField
                    fullWidth
                    size="small"
                    placeholder={t("sidenav.search-stores")}
                    value={storeSearchQuery}
                    onChange={(e) => setStoreSearchQuery(e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <MagnifyingGlassIcon color={isDarkMode ? '#9CA3AF' : '#6b7280'} size={14} />
                        </InputAdornment>
                      ),
                      sx: {
                        backgroundColor: isDarkMode ? 'rgba(2,6,23,0.35)' : 'rgba(243, 244, 246, 0.8)',
                        borderRadius: '6px',
                        fontSize: '0.875rem',
                        '& .MuiOutlinedInput-notchedOutline': {
                          border: `1px solid ${isDarkMode ? 'rgba(148,163,184,0.25)' : 'rgba(209, 213, 219, 0.5)'}`,
                        },
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: isDarkMode ? 'rgba(148,163,184,0.45)' : 'rgba(156, 163, 175, 0.7)',
                        },
                        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                          borderColor: isDarkMode ? '#64748b' : '#3b82f6',
                        },
                        '& input': {
                          color: isDarkMode ? '#E5E7EB' : '#374151',
                          fontSize: '0.875rem',
                          '&::placeholder': {
                            color: isDarkMode ? '#94a3b8' : '#6b7280',
                            opacity: 1,
                          },
                        },
                      },
                    }}
                  />
                </Box>
              )}
              
              {/* Stores List - with fixed height */}
              <Box 
                className="flex-1 overflow-y-auto"
                sx={{ 
                  mb: 2,
                  minHeight: 0,
                }}
              >
                {filteredShopOptions.length === 0 ? (
                  <Box sx={{ py: 2, textAlign: 'center' }}>
                    <Typography
                      variant="body2"
                      sx={{ color: '#6b7280', fontSize: '0.875rem' }}
                    >
                      {t("sidenav.no-stores-found")}
                    </Typography>
                  </Box>
                ) : (
                  filteredShopOptions.map((shop) => {
                    let renderName = !!shop.nameKey ? t(shop.nameKey) : shop.name;
                    let isActiveShop = shop.myshopify_domain === selectedShop;
                    let isSpecialOption = ["multi-store-sign-in", "multi-store-sign-up", "add-new-store"].includes(shop.myshopify_domain);
                    
                    return (
                      <Box
                        key={shop.myshopify_domain}
                        onClick={() => {
                          if (shop.myshopify_domain !== selectedShop) {
                            handleSelectedShopChange(shop.myshopify_domain);
                          }
                        }}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          p: 1,
                          borderRadius: 1,
                          cursor: 'pointer',
                          backgroundColor: isActiveShop ? (isDarkMode ? 'rgba(148,163,184,0.12)' : 'rgba(59, 130, 246, 0.1)') : 'transparent',
                          '&:hover': {
                            backgroundColor: isActiveShop ? (isDarkMode ? 'rgba(148,163,184,0.18)' : 'rgba(59, 130, 246, 0.15)') : (isDarkMode ? 'rgba(148,163,184,0.08)' : 'rgba(243, 244, 246, 0.8)'),
                          },
                          mb: 0.5
                        }}
                      >
                        <Box sx={{ mr: 1.5 }}>
                          {isSpecialOption ? (
                            <SquaresFourIcon color={isDarkMode ? '#94a3b8' : '#3b82f6'} size={20} />
                          ) : (
                            <Box
                              sx={{
                                width: '24px',
                                height: '24px',
                                borderRadius: '50%',
                                backgroundColor: getStoreAvatarColor(shop),
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                fontSize: '10px',
                                fontWeight: 'bold',
                                color: 'white !important', // Force white text always
                              }}
                            >
                              {getStoreInitials(shop)}
                            </Box>
                          )}
                        </Box>
                        <Box sx={{ flex: 1, minWidth: 0 }}>
                          <Typography
                            variant="body2"
                            sx={{
                              color: isDarkMode ? '#E5E7EB' : '#374151',
                              fontWeight: isActiveShop ? "medium" : "regular",
                              fontSize: '0.875rem',
                              lineHeight: 1.2,
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap'
                            }}
                          >
                            {renderName}
                          </Typography>
                          {!isSpecialOption && shop.myshopify_domain && (
                            <Typography
                              variant="caption"
                              sx={{
                                color: isDarkMode ? '#9CA3AF' : '#6b7280',
                                fontSize: '0.75rem',
                                lineHeight: 1.1,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap'
                              }}
                            >
                              {shop.myshopify_domain}
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    );
                  })
                )}
              </Box>
              
              {/* Action Buttons Container */}
              <Box sx={{ flexShrink: 0 }}>
                {/* Add Store Button - Only show for admins/owners */}
                {[WORKSPACE_ROLE_ADMIN, WORKSPACE_ROLE_OWNER].includes(loginConfig.userData?.role) && (
                  <Box sx={{ mb: 2 }}>
                    <Button
                      variant="outlined"
                      size="small"
                      fullWidth
                      onClick={handleAddNewStore}
                      startIcon={<PlusIcon size={16} />}
                      sx={{
                        color: isDarkMode ? '#E5E7EB' : '#374151',
                        borderColor: isDarkMode ? 'rgba(148,163,184,0.35)' : 'rgba(209, 213, 219, 0.6)',
                        fontSize: "0.75rem",
                        py: 0.75,
                        fontWeight: "medium",
                        textTransform: "uppercase",
                        "&:hover": {
                          borderColor: isDarkMode ? 'rgba(148,163,184,0.55)' : '#3b82f6',
                          backgroundColor: isDarkMode ? 'rgba(148,163,184,0.08)' : 'rgba(59, 130, 246, 0.05)'
                        }
                      }}
                    >
                      {t("add-new-store")}
                    </Button>
                  </Box>
                )}
                
                {/* Manage Workspaces Button */}
                <Box>
                  <Button
                    variant="outlined"
                    size="small"
                    fullWidth
                    onClick={handleManageWorkspaces}
                    startIcon={<SquaresFourIcon size={16} />}
                    sx={{
                      color: isDarkMode ? '#E5E7EB' : '#374151',
                      borderColor: isDarkMode ? 'rgba(148,163,184,0.35)' : 'rgba(209, 213, 219, 0.6)',
                      fontSize: "0.75rem",
                      py: 0.75,
                      fontWeight: "medium",
                      textTransform: "uppercase",
                      "&:hover": {
                        borderColor: isDarkMode ? 'rgba(148,163,184,0.55)' : '#3b82f6',
                        backgroundColor: isDarkMode ? 'rgba(148,163,184,0.08)' : 'rgba(59, 130, 246, 0.05)'
                      }
                    }}
                  >
                    {t("manage-workspaces")}
                  </Button>
                </Box>
              </Box>
            </>
          )}
          
          {/* Shop Flow - Add Store Button */}
          {isShopFlow && (
            <Box sx={{ flexShrink: 0 }}>
              <Divider sx={{ borderColor: 'rgba(209, 213, 219, 0.6)', my: 1 }} />
              
              {/* Add Store Button */}
              <Box>
                <Button
                  variant="outlined"
                  size="small"
                  fullWidth
                  onClick={handleAddNewStore}
                  startIcon={<PlusIcon size={16} />}
                  sx={{
                    color: isDarkMode ? '#E5E7EB' : '#374151',
                    borderColor: isDarkMode ? 'rgba(148,163,184,0.35)' : 'rgba(209, 213, 219, 0.6)',
                    fontSize: "0.75rem",
                    py: 0.75,
                    fontWeight: "medium",
                    textTransform: "uppercase",
                    "&:hover": {
                      borderColor: isDarkMode ? 'rgba(148,163,184,0.55)' : '#3b82f6',
                      backgroundColor: isDarkMode ? 'rgba(148,163,184,0.08)' : 'rgba(59, 130, 246, 0.05)'
                    }
                  }}
                >
                  {t("add-new-store")}
                </Button>
              </Box>
            </Box>
          )}
        </Box>
      </Popover>

      {/* Expanded Panel */}
      <Box
        className={`
          fixed top-0 h-screen
          border-r shadow-xl transition-all duration-300 ease-out
          ${activeCategory ? 'translate-x-0 opacity-100' : '-translate-x-full opacity-0 pointer-events-none'}
          flex flex-col
          ${isDarkMode 
            ? 'bg-gray-800 border-gray-600' 
            : 'bg-gray-50 border-gray-200'
          }
          motion-reduce:transition-none
        `}
        style={{ 
          left: SIDEBAR_WIDTH, 
          width: EXPANDED_PANEL_WIDTH,
          zIndex: 1250, // Between main sidebar and navbar
          backgroundColor: '#1f2631'
        }}
      >
        {/* Panel Header */}
        <Box className={`h-16 px-4 border-b ${
          isDarkMode ? 'border-gray-600' : 'border-gray-200'
        }`}>
          <Box className="flex items-center justify-between h-full">
            <Box className="flex items-center space-x-3">
              <Box className={`text-lg ${
                isDarkMode ? 'text-slate-300' : 'text-blue-500'
              }`}>
                {activeCategory && (() => {
                  const IconComponent = categories[activeCategory]?.icon;
                  return IconComponent ? <IconComponent /> : null;
                })()}
              </Box>
              <Typography 
                variant="h6" 
                className={`font-medium text-base ${
                  isDarkMode ? 'text-white' : 'text-gray-800'
                }`}
                sx={{ fontWeight: 500, fontSize: '1rem', letterSpacing: '0.15px', color: isDarkMode ? '#E5E7EB' : '#1F2937'}}
              >
                {safeTranslate(categories[activeCategory]?.name)}
              </Typography>
            </Box>
            <IconButton
              onClick={() => {
                setActiveCategory(dispatch, null);
              }}
              size="small"
              sx={{ 
                color: isDarkMode ? '#d1d5db !important' : '#374151 !important',
                '&:hover': {
                  color: isDarkMode ? '#f3f4f6 !important' : '#1f2937 !important',
                  backgroundColor: isDarkMode ? 'rgba(75, 85, 99, 0.1)' : 'rgba(156, 163, 175, 0.1)'
                }
              }}
            >
              <XIcon size={16} />
            </IconButton>
          </Box>
        </Box>

        {/* Panel Content */}
        <Box className="flex-1 overflow-y-auto">
          {/* Pinned Items Section */}
          {pinnedItems.length > 0 && activeCategory && activeCategory !== 'settings' && (
            <Box className="px-4 pt-3 pb-2">
              <Box 
                onClick={togglePinnedSection}
                className="cursor-pointer rounded-md px-1 py-1.5"
                sx={{
                  userSelect: 'none',
                  WebkitUserSelect: 'none',
                  MozUserSelect: 'none',
                  msUserSelect: 'none'
                }}
              >
                <Box className="flex items-center justify-between">
                  <Box className="flex items-center" style={{ minWidth: 0, flex: 1 }}>
                    {/* Icon container - fixed width to align with main items */}
                    <Box style={{ width: '20px', display: 'flex', justifyContent: 'flex-start', marginRight: '8px' }}>
                      <PushPinIcon color={isDarkMode ? '#9CA3AF' : '#6B7280'} size={16} />
                    </Box>
                    
                    {/* Section name - starts at same position as main item text */}
                    <Typography 
                      variant="caption" 
                      className="tracking-wide"
                      sx={{ 
                        fontSize: '0.85rem',
                        fontWeight: 400,
                        letterSpacing: '0.15px',
                        color: isDarkMode ? '#9CA3AF' : '#6B7280',
                        flex: 1,
                        userSelect: 'none',
                        WebkitUserSelect: 'none',
                        MozUserSelect: 'none',
                        msUserSelect: 'none'
                      }}
                    >
                      {t("sidenav.pinned")} ({pinnedItems.length})
                    </Typography>
                  </Box>
                  
                  {/* Chevron for collapsible pinned section - aligned with other chevrons */}
                  <Box style={{ width: '48px', display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                    <Box className={`transition-transform duration-200 ${
                      pinnedSectionCollapsed ? '' : 'rotate-90'
                    }`} style={{ marginRight: '10px' }}>
                      <CaretRightIcon color={isDarkMode ? '#9CA3AF' : '#6B7280'} size={14} />
                    </Box>
                  </Box>
                </Box>
              </Box>
                            {!pinnedSectionCollapsed && (
                <Box className="mt-1 space-y-0.5">
                {pinnedItems.map((pinnedItem, index) => {
                  // Find the actual item from categories to get the icon
                  let fullItem = null;
                  const categoryItems = categories[activeCategory]?.items || [];
                  
                  // Look for the item in direct routes
                  fullItem = categoryItems.find(item => item.route === pinnedItem.route);
                  
                  // If not found, look in sub-items
                  if (!fullItem) {
                    for (const item of categoryItems) {
                      if (item.subItems) {
                        fullItem = item.subItems.find(sub => sub.route === pinnedItem.route);
                        if (fullItem) break;
                      }
                    }
                  }
                  
                  // If still not found, use the pinned item data
                  if (!fullItem) {
                    fullItem = pinnedItem;
                  }

                  const isDragging = draggedIndex === index;
                  const isDropTarget = dragOverIndex === index && draggedIndex !== null && draggedIndex !== index;
                  
                                      return (
                      <Fragment key={pinnedItem.route}>
                      {/* Drop zone indicator above this item */}
                      {isDropTarget && draggedIndex !== null && draggedIndex > index && (
                        <Box
                          sx={{
                            height: '2px',
                            backgroundColor: isDarkMode ? '#3B82F6' : '#3B82F6',
                            borderRadius: '1px',
                            mx: 2,
                            my: 0.5,
                            opacity: 0.8
                          }}
                        />
                      )}
                      
                      <NavigationItem
                        item={fullItem}
                        isActive={isActive}
                        isDarkMode={isDarkMode}
                        safeTranslate={safeTranslate}
                        togglePinItem={togglePinItem}
                        pinnedItems={pinnedItems}
                        variant="pinned"
                        isDraggable={true}
                        dragProps={{
                          isDragging,
                          onDragStart: (e) => handleDragStart(e, index),
                          onDragOver: (e) => handleDragOver(e, index),
                          onDragLeave: handleDragLeave,
                          onDrop: (e) => handleDrop(e, index),
                          onDragEnd: handleDragEnd
                        }}
                      />

                      {/* Drop zone indicator below this item */}
                      {isDropTarget && draggedIndex !== null && draggedIndex < index && (
                        <Box
                          sx={{
                            height: '2px',
                            backgroundColor: isDarkMode ? '#3B82F6' : '#3B82F6',
                            borderRadius: '1px',
                            mx: 2,
                            my: 0.5,
                            opacity: 0.8
                          }}
                        />
                                             )}
                     </Fragment>
                    );
                  })}
                </Box>
              )}
            </Box>
          )}

          {/* Main Navigation Items */}
          <Box className="px-4 py-3 space-y-2">
            {activeCategory && categories[activeCategory]?.items
              .filter((item) => {
                // If item has a feature key, check if it's enabled in planDetails
                if (item.feature) {
                  return shopConfig.planDetails?.features?.[item.feature] === true;
                }
                // If no feature key, always show the item
                return true;
              })
              .map((item, index) => (
                <Box key={item.key}>
                  
                  {/* Level 2 Item - Section Headers */}
                  <NavigationItem
                    item={item}
                    isActive={isActive}
                    isDarkMode={isDarkMode}
                    safeTranslate={safeTranslate}
                    togglePinItem={togglePinItem}
                    pinnedItems={pinnedItems}
                    showPinButton={false && item.route && activeCategory !== 'settings'}
                    variant="main"
                    hasChevron={item.hasSubItems}
                    chevronProps={{ isExpanded: !collapsedSections.has(item.key) }}
                    onClick={(e) => {
                      if (item.hasSubItems && !item.route) {
                        e.preventDefault();
                        toggleSectionCollapse(item.key);
                      }
                    }}
                  />

                {/* Level 3 Sub-items - Collapsible */}
                {item.hasSubItems && item.subItems && !collapsedSections.has(item.key) && (
                  <Box className="mt-1 space-y-0.5">
                    {item.subItems
                      .filter((subItem) => {
                        // If subitem has a feature key, check if it's enabled in planDetails
                        if (subItem.feature) {
                          return shopConfig.planDetails?.features?.[subItem.feature] === true;
                        }
                        // If no feature key, always show the item
                        return true;
                      })
                      .map((subItem) => (
                        <NavigationItem
                          key={subItem.key}
                          item={subItem}
                          isActive={isActive}
                          isDarkMode={isDarkMode}
                          safeTranslate={safeTranslate}
                          togglePinItem={togglePinItem}
                          pinnedItems={pinnedItems}
                          showPinButton={false && activeCategory !== 'settings'}
                          variant="sub"
                          additionalContent={
                            subItem.isNew && (
                              <Box
                                sx={{
                                  px: 0.75,
                                  py: 0.25,
                                  borderRadius: '9999px',
                                  backgroundColor: isDarkMode ? 'rgba(156, 163, 175, 0.2)' : 'rgba(243, 244, 246, 0.8)',
                                  color: isDarkMode ? '#10B981' : '#059669',
                                  fontSize: '0.6rem',
                                  fontWeight: 600,
                                  textTransform: 'uppercase',
                                  letterSpacing: '0.05em',
                                  border: `1px solid ${isDarkMode ? 'rgba(16, 185, 129, 0.3)' : 'rgba(5, 150, 105, 0.2)'}`
                                }}
                              >
                                {t("sidenav.new")}
                              </Box>
                            )
                          }
                        />
                      ))}
                  </Box>
                )}
              </Box>
            ))}
          </Box>
        </Box>

        {/* Free Trial Button - moved from main sidebar */}
        {shopConfig?.subscription_enabled && shopConfig?.planDetails?.planType === "free" && (
          <Box className="px-4 py-3" sx={{ 
            flexShrink: 0
          }}>
            <MDButton
              variant="outlined"
              fullWidth
              className="rotate-tag-parent"
              startIcon={
                <MDBox className="rotate-tag" component="img" src={premiumTag} alt="Premium" width="16px" />
              }
              onClick={() => {
                tracker.event("Sidebar Premium Button Clicked", { page: location.pathname });
                NiceModal.show(PaywallDialog, { feature: "" });
              }}
              sx={{
                justifyContent: 'center',
                padding: '10px 16px',
                borderRadius: '8px',
                // backgroundColor: 'transparent',
                backgroundColor: isDarkMode ? 'rgba(251, 191, 36, 0.06)' : 'rgba(251, 191, 36, 0.03)',
                borderColor: isDarkMode ? 'rgba(245, 158, 11, 0.4)' : 'rgba(217, 119, 6, 0.3)',
                color: isDarkMode ? '#F59E0B' : '#D97706',
                fontSize: '0.85rem',
                fontWeight: 450,
                lineHeight: 1.4,
                letterSpacing: '0.15px',
                textTransform: 'none',
                '&:hover': {
                  // backgroundColor: 'transparent',
                  backgroundColor: isDarkMode ? 'rgba(251, 191, 36, 0.1)' : 'rgba(251, 191, 36, 0.06)',
                  borderColor: isDarkMode ? 'rgba(245, 158, 11, 0.6)' : 'rgba(217, 119, 6, 0.5)',
                  color: isDarkMode ? '#FBBF24' : '#92400E'
                  // boxShadow: isDarkMode 
                  //   ? '0 2px 8px rgba(245, 158, 11, 0.15)' 
                  //   : '0 2px 8px rgba(217, 119, 6, 0.1)',
                },
                '& .MuiButton-startIcon': {
                  marginRight: '8px',
                  marginLeft: 0
                },
                transition: 'all 0.2s ease'
              }}
            >
              {t("sidenav.start-free-trial")}
            </MDButton>
          </Box>
        )}
      </Box>
    </>
  );
}

export default NewSidenav;
