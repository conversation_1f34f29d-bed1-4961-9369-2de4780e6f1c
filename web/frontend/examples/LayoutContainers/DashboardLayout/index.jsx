import { useEffect, useState } from "react";

// react-router-dom components
import { useLocation } from "react-router-dom";

// prop-types is a library for typechecking of props.
import PropTypes from "prop-types";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import { Grid } from "@mui/material";

// Material Dashboard 2 PRO React context
import { useMaterialUIController, setLayout, setOpenAIInsights } from "@/context";

// Sidebar utilities
import { calculateSidebarWidth } from "@/utils/sidebarUtils";
import { useSidebarResize, getResizeHandleProps } from "@/utils/sidebarResizeUtils";

// AI Insights Sidebar
import AIInsightsSidebar from '@/components/AIInsightsSidebar';
import { getRouteKeyFromPathname } from "@/router/constants";
import dayjs from 'dayjs';

// Toggle to enable/disable AI Insights sidebar resize functionality
const ENABLE_AI_SIDEBAR_RESIZE = true;

function DashboardLayout({ children }) {
  const [controller, dispatch] = useMaterialUIController();
  const { miniSidenav, shopConfig, openAIInsights, selectedFilters = {} } = controller;
  const { pathname } = useLocation();
  const { start_date, end_date } = selectedFilters;
  
  // Calculate initial sidebar width based on current route to prevent layout shift
  const initialSidebarWidth = calculateSidebarWidth(pathname);
  const [sidebarWidth, setSidebarWidth] = useState(initialSidebarWidth);
  const [isInitialized, setIsInitialized] = useState(false);

  // AI Insights sidebar resize functionality
  const aiSidebarResize = useSidebarResize({
    enabled: ENABLE_AI_SIDEBAR_RESIZE,
    defaultWidth: 400,
    minWidth: 300,
    maxWidth: 800,
    rightMargin: 24,
    storageKey: 'aiSidebarWidth'
  });

  let showSubscriptionBanner = false && shopConfig.subscription_enabled
    && shopConfig.planDetails?.planType == "free";

  const currentComponentId = getRouteKeyFromPathname(pathname);

  useEffect(() => {
    setLayout(dispatch, "dashboard");
    
    // Update sidebar width when route changes to prevent layout shift
    const newSidebarWidth = calculateSidebarWidth(pathname);
    setSidebarWidth(newSidebarWidth);
    
    // Request sidebar width update on route change to sync with actual sidebar state
    const requestSidebarWidthUpdate = () => {
      window.dispatchEvent(new CustomEvent('requestSidebarWidth'));
    };
    
    // Small delay to ensure sidebar is ready
    setTimeout(requestSidebarWidthUpdate, 50);
  }, [pathname, dispatch]);

  // Listen for sidebar width changes via custom events
  useEffect(() => {
    const handleSidebarWidthChange = (event) => {
      setSidebarWidth(event.detail.width);
      setIsInitialized(true);
    };

    window.addEventListener('sidebarWidthChange', handleSidebarWidthChange);
    
    // Initial request for sidebar width
    window.dispatchEvent(new CustomEvent('requestSidebarWidth'));
    
    return () => {
      window.removeEventListener('sidebarWidthChange', handleSidebarWidthChange);
    };
  }, []);

  return (
    <MDBox
      sx={({ breakpoints, transitions, functions: { pxToRem } }) => ({
        p: 3,
        top: (showSubscriptionBanner ? "2rem" : "0"),
        position: "relative",
        marginLeft: pxToRem(sidebarWidth),
        transition: transitions.create(["margin-left"], {
          easing: transitions.easing.easeInOut,
          duration: 300,
        }),
        zIndex: { xs: 2200, md: 1 }, // Ensure AI overlay sits above left sidebar on mobile
        minHeight: "calc(100vh - 6rem)",
      })}
    >
      <Grid container spacing={2} alignItems="flex-start" wrap="nowrap" sx={{ overflow: 'hidden' }}>
        {/* Main content */}
        <Grid item sx={{ flex: { xs: '1 1 100%', md: openAIInsights && currentComponentId ? `0 0 calc(100% - ${aiSidebarResize.sidebarWidth}px)` : '1 1 auto' }, minWidth: 0 }}>
          {children}
        </Grid>

        {/* AI Insights Sidebar - positioned to the right */}
        {openAIInsights && currentComponentId && (
          <Grid
            item
            sx={{
              flex: { xs: '0 0 100%', md: `0 0 ${aiSidebarResize.sidebarWidth}px` },
              minWidth: 0,
              maxWidth: { xs: '100%', md: `${aiSidebarResize.sidebarWidth}px` },
              position: { xs: 'fixed', md: 'fixed' },
              top: { xs: 0, md: 0 },
              right: { xs: 0, md: 24 },
              bottom: { xs: 0, md: 0 },
              left: { xs: 0, md: 'auto' },
              width: { xs: '100vw', md: `${aiSidebarResize.sidebarWidth}px` },
              height: { xs: '100vh', md: '100vh' },
              zIndex: { xs: 2000, md: 2000 },
              overflow: { xs: 'auto', md: 'visible' },
            }}
          >
            {/* Resize handle - only show if resize is enabled */}
            {ENABLE_AI_SIDEBAR_RESIZE && (
              <MDBox {...getResizeHandleProps(aiSidebarResize)} />
            )}
            <AIInsightsSidebar
              key={currentComponentId}
              open={openAIInsights}
              onClose={() => setOpenAIInsights(dispatch, false)}
              filters={{
                start_date: start_date ? dayjs(start_date).format("YYYY-MM-DD") : undefined,
                end_date: end_date ? dayjs(end_date).format("YYYY-MM-DD") : undefined
              }}
            />
          </Grid>
        )}
      </Grid>
    </MDBox>
  );
}

// Typechecking props for the DashboardLayout
DashboardLayout.propTypes = {
  children: PropTypes.node.isRequired,
};

export default DashboardLayout;
