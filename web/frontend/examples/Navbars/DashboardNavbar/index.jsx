import { useState, useEffect } from "react";

// react-router components
import { useLocation, Link, useNavigate } from "react-router-dom";
import { tracker } from "@/context";
import { signOutUser } from "@/utils/auth";
// prop-types is a library for typechecking of props.
import PropTypes from "prop-types";
import SectionNavigation from "@/examples/Navbars/SectionNavigation";

// User Settings Modal
import UserSettingsModal from "@/layouts/pages/account/settings/UserSettingsModal";

// @phosphor-icons/react imports
import { 
  UserIcon,
  GearIcon,
  StorefrontIcon,
  CalendarCheckIcon,
  QuestionIcon,
  TranslateIcon,
  SignOutIcon,
  LightbulbIcon,
  SquaresFourIcon,
  CaretRightIcon
} from '@phosphor-icons/react';

// @material-ui core components
import AppBar from "@mui/material/AppBar";
import Toolbar from "@mui/material/Toolbar";
import IconButton from "@mui/material/IconButton";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import IdentificationModal from '@/components/IdentificationModal';
import NiceModal from '@ebay/nice-modal-react';
import MUILink from "@mui/material/Link";
import Avatar from "@mui/material/Avatar";
import ListItemIcon from "@mui/material/ListItemIcon";
import MDTypography from "@/components/MDTypography";
import Divider from "@mui/material/Divider";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";

// Material Dashboard 2 PRO React examples
import Breadcrumbs from "@/examples/Breadcrumbs";
import { getRouteStaticDataFromPathname, getRouteKeyFromPathname } from "@/router/constants";

// Custom styles for DashboardNavbar
import {
  navbar,
  navbarContainer,
  navbarIconButton,
} from "@/examples/Navbars/DashboardNavbar/styles";

// Material Dashboard 2 PRO React context
import {
  useMaterialUIController,
  setTransparentNavbar,
  setMiniSidenav,
  setOpenConfigurator,
  setOpenAIInsights,
} from "@/context";

import { useTranslation } from 'react-i18next';
import MDButton from "@/components/MDButton";

function DashboardNavbar({ absolute, light, isMini, sections }) {
  const [navbarType, setNavbarType] = useState();
  const [controller, dispatch] = useMaterialUIController();
  const { miniSidenav, transparentNavbar, fixedNavbar, openConfigurator, openAIInsights, darkMode, loginConfig, shopConfig, selectedShop } = controller;
  const [userMenuAnchor, setUserMenuAnchor] = useState(null);
  const [langMenuAnchor, setLangMenuAnchor] = useState(null);
  const route = useLocation().pathname.split("/").slice(1);
  const { i18n, t } = useTranslation();
  const { pathname } = useLocation();

  const routeStaticData = getRouteStaticDataFromPathname(pathname);

  let showSubscriptionBanner = false && shopConfig.subscription_enabled
    && shopConfig.planDetails?.planType == "free";

  useEffect(() => {
    // Setting the navbar type
    if (fixedNavbar) {
      setNavbarType("sticky");
    } else {
      setNavbarType("static");
    }

    // A function that sets the transparent state of the navbar.
    function handleTransparentNavbar() {
      setTransparentNavbar(dispatch, (fixedNavbar && window.scrollY === 0) || !fixedNavbar);
    }

    /**
     The event listener that's calling the handleTransparentNavbar function when
     scrolling the window.
    */
    window.addEventListener("scroll", handleTransparentNavbar);

    // Call the handleTransparentNavbar function to set the state with the initial value.
    handleTransparentNavbar();

    // Remove event listener on cleanup
    return () => window.removeEventListener("scroll", handleTransparentNavbar);
  }, [dispatch, fixedNavbar]);

  const handleMiniSidenav = () => setMiniSidenav(dispatch, !miniSidenav);
  const handleConfiguratorOpen = () => setOpenConfigurator(dispatch, !openConfigurator);
  const handleAIInsightsToggle = () => setOpenAIInsights(dispatch, !openAIInsights);

  const handleUserMenuOpen = (event) => setUserMenuAnchor(event.currentTarget);
  const handleUserMenuClose = () => setUserMenuAnchor(null);

  const handleLangMenuOpen = (event) => setLangMenuAnchor(event.currentTarget);
  const handleLangMenuClose = () => setLangMenuAnchor(null);

  const handleLanguageChange = (lang) => {
    i18n.changeLanguage(lang);
    handleLangMenuClose();
  };

  // Styles for the navbar icons
  const iconsStyle = ({ palette: { dark, white, text }, functions: { rgba } }) => ({
    color: () => {
      let colorValue = light || darkMode ? white.main : dark.main;

      if (transparentNavbar && !light) {
        colorValue = darkMode ? rgba(text.main, 0.6) : text.main;
      }

      return colorValue;
    },
  });

  // Render the language menu
  const renderLangMenu = () => (
    <Menu
      anchorEl={langMenuAnchor}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "right",
      }}
      transformOrigin={{
        vertical: "top",
        horizontal: "right",
      }}
      open={Boolean(langMenuAnchor)}
      onClose={handleLangMenuClose}
      PaperProps={{
        sx: {
          mt: 1,
          minWidth: "140px",
          "& .MuiMenuItem-root": {
            py: 1,
            px: 2,
            fontSize: "0.875rem"
          }
        }
      }}
    >
      <MenuItem onClick={() => handleLanguageChange('en')}>English</MenuItem>
      <MenuItem onClick={() => handleLanguageChange('es')}>Español</MenuItem>
      <MenuItem onClick={() => handleLanguageChange('fr')}>Français</MenuItem>
      <MenuItem onClick={() => handleLanguageChange('de')}>Deutsch</MenuItem>
    </Menu>
  );

  // Check if it's a shop flow (no user context)
  const isShopFlow = !loginConfig.userData || !loginConfig.userData.email;
  const navigate = useNavigate();

  // Render the user menu with options based on flow type
  const renderUserMenu = () => (
    <Menu
      anchorEl={userMenuAnchor}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "right",
      }}
      transformOrigin={{
        vertical: "top",
        horizontal: "right",
      }}
      open={Boolean(userMenuAnchor)}
      onClose={handleUserMenuClose}
      PaperProps={{
        sx: {
          mt: 1,
          minWidth: "240px",
          borderRadius: "12px",
          border: "1px solid rgba(0,0,0,0.05)",
          boxShadow: "0 4px 20px rgba(0,0,0,0.08)",
          "& .MuiMenuItem-root": {
            py: 1.25,
            px: 2.5,
            borderRadius: "8px",
            mx: 1,
            my: 0.25,
            "&:hover": {
              backgroundColor: "rgba(0,0,0,0.04)"
            }
          },
          "& .MuiListItemIcon-root": {
            minWidth: "32px"
          },
          "& .MuiTypography-root": {
            fontSize: "0.85rem",
            fontWeight: 500
          }
        }
      }}
    >
      {/* User info header - show in user flow only */}
      {!isShopFlow && loginConfig.userData && (
        <MDBox p={2.5} mb={0.5} display="flex" alignItems="center">
          <Avatar
            sx={{
              width: 44,
              height: 44,
              bgcolor: darkMode ? 'info.main' : 'rgba(26, 115, 232, 0.1)',
              mr: 2,
              border: darkMode ? 'none' : '2px solid rgba(26, 115, 232, 0.2)'
            }}
          >
            <UserIcon size={22} color={darkMode ? 'white' : '#1A73E8'} />
          </Avatar>
          <MDBox>
            <MDTypography variant="subtitle2" fontWeight="600" sx={{ color: darkMode ? 'white' : '#1f2937' }}>
              {loginConfig.userData.onboard_name || "User"}
            </MDTypography>
            <MDTypography variant="caption" sx={{ color: darkMode ? 'rgba(255,255,255,0.7)' : '#6b7280', fontSize: '0.8rem' }}>
              {loginConfig.userData.email}
            </MDTypography>
          </MDBox>
        </MDBox>
      )}

      {
        isShopFlow && (

          <MDBox p={2.5} mb={0.5} display="flex" alignItems="center">
          <Avatar
            sx={{
              width: 44,
              height: 44,
              bgcolor: darkMode ? 'info.main' : 'rgba(26, 115, 232, 0.1)',
              mr: 2,
              border: darkMode ? 'none' : '2px solid rgba(26, 115, 232, 0.2)'
            }}
          >
            <UserIcon size={22} color={darkMode ? 'white' : '#1A73E8'} />
          </Avatar>
                     <MDBox>
             <MDBox 
               display="flex" 
               alignItems="center" 
               sx={{ 
                 cursor: 'pointer',
                 '&:hover': {
                   opacity: 0.8
                 }
               }} 
               onClick={() => {
                 NiceModal.show(IdentificationModal, {});
                 handleUserMenuClose();
               }}
             >
               <MDTypography variant="caption" color="info" sx={{ fontSize: '0.8rem', textAlign: 'left' }}>
                 {t("setup-workspace")}
               </MDTypography>
               <CaretRightIcon size={12} color={darkMode ? '#1A73E8' : '#1A73E8'} style={{ marginLeft: '4px' }} />
             </MDBox>
           </MDBox>
        </MDBox>
        )
      }

      {/* Divider after user/shop info */}
      <Divider sx={{ my: 1, mx: 1, borderColor: 'rgba(0,0,0,0.06)' }} />
      {/* Theme Settings - Admin only */}
      {loginConfig.admin && (
        <MenuItem onClick={() => { handleConfiguratorOpen(); handleUserMenuClose(); }}>
          <ListItemIcon>
            <GearIcon size={18} />
          </ListItemIcon>
          <MDTypography variant="body2">{t("settings")}</MDTypography>
        </MenuItem>
      )}

      {/* User Settings - Show in user flow only */}
      {!isShopFlow && (
        <MenuItem
          onClick={() => {
            NiceModal.show(UserSettingsModal);
            handleUserMenuClose();
          }}
        >
          <ListItemIcon>
            <UserIcon size={18} />
          </ListItemIcon>
          <MDTypography variant="body2">{t("account")}</MDTypography>
        </MenuItem>
      )}

      {/* Book a call */}
      <MenuItem
        component={Link}
        to="/book-call"
        onClick={() => {
          tracker.event("Clicked BookCall", {page: pathname, source: "navbar"});
          handleUserMenuClose();
        }}
      >
        <ListItemIcon>
          <CalendarCheckIcon size={18} />
        </ListItemIcon>
        <MDTypography variant="body2">{t("book-call")}</MDTypography>
      </MenuItem>

      {/* Feature Requests */}
      <MenuItem
        component={Link}
        to="/feature-requests"
        onClick={() => {
          tracker.event("Feature Request Clicked", {page: pathname, source: "navbar"});
          handleUserMenuClose();
        }}
      >
        <ListItemIcon>
          <LightbulbIcon size={18} />
        </ListItemIcon>
        <MDTypography variant="body2">{t("feature-requests")}</MDTypography>
      </MenuItem>

      {/* Help Center */}
      <MenuItem
        component={MUILink}
        href="https://help.datadrew.io/en"
        target="_blank"
        onClick={() => {
          tracker.event("Clicked HelpCenter", {page: pathname, source: "navbar"});
          handleUserMenuClose();
        }}
      >
        <ListItemIcon>
                      <QuestionIcon size={18} />
        </ListItemIcon>
        <MDTypography variant="body2">{t("visit-help-center")}</MDTypography>
      </MenuItem>

      {/* Language Selection */}
      <MenuItem onClick={handleLangMenuOpen}>
        <ListItemIcon>
                      <TranslateIcon size={18} />
        </ListItemIcon>
        <MDTypography variant="body2">{t("translate")}</MDTypography>
      </MenuItem>

      {/* Add Sign Out option to the menu */}
      <MenuItem onClick={() => {
        signOutUser(navigate, isShopFlow);
        handleUserMenuClose();
      }}>
        <ListItemIcon>
                      <SignOutIcon size={18} />
        </ListItemIcon>
        <MDTypography variant="body2">{t("sign-out")}</MDTypography>
      </MenuItem>
    </Menu>
  );

  const renderAIInsightsToggle = () => {
    if (!(shopConfig?.planDetails?.features?.auto_insights ?? false) || !getRouteKeyFromPathname(pathname)) {
      return null;
    }

    return (
      <MDBox color={light ? "white" : "inherit"} mr={1}>
        <MDButton
          size="small"
          variant={openAIInsights ? "contained" : "text"}
          color={openAIInsights ? "primary" : "inherit"}
          sx={{
            px: 3,
            py: 1,
            mr: 3,
            background: openAIInsights ? '#a0a0a0' : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            borderRadius: '12px',
            boxShadow: openAIInsights ? '0 4px 12px rgba(102, 126, 234, 0.4)' : 'none',
            position: 'relative',
            overflow: 'hidden',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            fontWeight: '600',
            textTransform: 'none',
            minWidth: 'auto',
            '&:hover': {
              transform: openAIInsights ? 'none' : 'translateY(-2px)',
              boxShadow: openAIInsights ? 'none' : '0 6px 20px rgba(102, 126, 234, 0.6)',
              background: openAIInsights ? '#a0a0a0' : 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)'
            },
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: '-100%',
              width: '100%',
              height: '100%',
              background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent)',
              transition: 'left 0.5s ease-in-out',
              animation: openAIInsights ? 'none' : 'shimmer 2s infinite'
            },
            '@keyframes shimmer': {
              '0%': { left: '-100%' },
              '100%': { left: '100%' }
            }
          }}
          onClick={handleAIInsightsToggle}
        // startIcon={!openAIInsights ? <Icon sx={{ fontSize: '1.1rem', mr: 0.5 }}>psychology</Icon> : null}
        >
          <MDTypography
            sx={{
              fontSize: '0.875rem',
              fontWeight: 'inherit',
              color: 'inherit'
            }}
          >
            AI Insights
          </MDTypography>
        </MDButton>
      </MDBox>
    );
  };

  return (
    <AppBar
      position={absolute ? "absolute" : navbarType}
      color="inherit"
      sx={(theme) => navbar(theme, { transparentNavbar, absolute, light, darkMode, showSubscriptionBanner })}
    >
      <Toolbar sx={(theme) => navbarContainer(theme)}>
        <MDBox
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          width="100%"
        >
          <MDBox display="flex" alignItems="center">
            <Breadcrumbs
              icon="home"
              title={route[route.length - 1].replace("-", " ")}
              route={route}
              light={light}
              description={routeStaticData?.short_description ?? ""}
            />
          </MDBox>

          {!isMini && (
            <MDBox display="flex" alignItems="center">
              {/* AI Insights Toggle Button */}
              {renderAIInsightsToggle()}

              {sections && sections.length > 0 && <SectionNavigation sections={sections} light={light} />}

              <MDBox color={light ? "white" : "inherit"}>
                <IconButton
                  size="small"
                  disableRipple
                  color="inherit"
                  sx={navbarIconButton}
                  onClick={handleUserMenuOpen}
                >
                  {/* Show settings icon in shop flow, user icon in user flow */}
                  <Avatar sx={{ 
                    width: 32, 
                    height: 32, 
                    bgcolor: darkMode ? 'info.main' : 'rgba(26, 115, 232, 0.1)', 
                    border: darkMode ? 'none' : '1.5px solid rgba(26, 115, 232, 0.2)',
                    transition: 'all 0.2s ease'
                  }}>
                    <UserIcon size={16} color={darkMode ? 'white' : '#1A73E8'} />
                  </Avatar>
                </IconButton>
                {renderUserMenu()}
                {renderLangMenu()}
              </MDBox>
            </MDBox>
          )}
        </MDBox>
      </Toolbar>
    </AppBar>
  );
}

// Setting default values for the props of DashboardNavbar
DashboardNavbar.defaultProps = {
  absolute: false,
  light: false,
  isMini: false,
  sections: [],
};

// Typechecking props for the DashboardNavbar
DashboardNavbar.propTypes = {
  absolute: PropTypes.bool,
  light: PropTypes.bool,
  isMini: PropTypes.bool,
  sections: PropTypes.arrayOf(PropTypes.string),
};

export default DashboardNavbar;