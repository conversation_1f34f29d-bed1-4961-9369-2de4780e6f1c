import {Link} from "react-router-dom";
// @mui material components
import Grid from "@mui/material/Grid";
// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import { toast } from 'react-toastify';
import RocketLaunchIcon from '@mui/icons-material/RocketLaunch';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
// Images
import bgImage from "@/assets/images/feature-block.jpg";
import { useTranslation } from "react-i18next";
import premiumTag from "@/assets/images/premium-tag.png";
import MDButton from "@/components/MDButton";
import NiceModal from '@ebay/nice-modal-react';
import PaywallDialog from '@/components/Paywall';
import {tracker, useCancellableAxios, useMaterialUIController} from "@/context";
import { useState } from "react";
import axios from "axios";

import CircularProgress from "@mui/material/CircularProgress";


let featureBlocks = {
    ltv_cohorts : {
        title: "ltv-cohorts",
        desc: "ltv-cohorts-desc",
        link: "http://help.datadrew.io/en/articles/5239600-cohort-analysis"
    },
    product_cohorts : {
        title: "product-cohorts",
        desc: "product-cohorts-desc",
        link: "https://help.datadrew.io/en/articles/8760982-leveraging-product-cohorts-in-your-ecommerce-business"
    },
    location_cohorts : {
        title: "location-cohorts",
        desc: "location-cohorts-desc",
        link: ""
    },
    custom_cohorts : {
        title: "custom-cohorts",
        desc: "custom-cohorts-desc",
        link: ""
    },
    transaction_frequency : {
        title: "transaction-frequency",
        desc: "transaction-frequency-desc",
        link: "https://help.datadrew.io/en/articles/8760982-leveraging-product-cohorts-in-your-ecommerce-business"
    },
    industry_benchmarks : {
        title: "p-benchmarks",
        desc: "benchmark-intro-2",
        link: "" // TODO
    },
    repurchase_rate : {
        title: "section-product-scatter",
        desc: "product-repurchase-tip-2",
        link: "https://help.datadrew.io/en/articles/5096099-product-repurchase-analysis"
    },
    basket_analysis : {
        title: "basket-analysis",
        desc: "basket-analysis-desc",
        link: "https://help.datadrew.io/en/articles/10026164-product-basket-analysis-with-datadrew"
    },
    rfm_segments : {
        title: "rfm-segments",
        desc: "rfm-segments-desc",
        link: "https://help.datadrew.io/en/articles/8340868-rfm-analysis"
    },
    new_vs_returning : {
        title: "p-new-vs-returning",
        desc: "new-vs-returning-desc",
        link: "" // TODO
    },
    facebook_ads_overview : {
        title: "section-facebook-ads",
        desc: "facebook-overview-desc",
        link : ""
    },
    prashna_ai : {
        title: "ask-ai",
        desc: "ask-ai-desc",
        isBeta: true,
        link: ""
    }
}

const FeatureBlock = ({feature}) => {
    const {t} = useTranslation();
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, shopConfig} = controller;
    const [isRequestAccessLoading, setIsRequestAccessLoading] = useState(false);
    const [featureRequestSuccess, setFeatureRequestSuccess] = useState(false);

    const axiosInstance = useCancellableAxios();

    if (!featureBlocks[feature]) {
        return null;
    }

    // check if upgrade is needed to access the feature
    let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;
    let isSubscriptionInActive = isSubscriptionEnabled && !(shopConfig.planDetails?.features?.[feature] ?? false);


    // check if the feature is beta
    let isFeatureInBeta = featureBlocks[feature].isBeta ?? false;
    
    if (!isSubscriptionInActive) {
        return null;
    }

    const handleUpgrade = () => {
        tracker.event("Paywall", {feature});
        NiceModal.show(PaywallDialog, {feature})
    }

    const handleRequestAccess = () => {
        if (featureRequestSuccess || isRequestAccessLoading) {
            return;
        }

        setIsRequestAccessLoading(true);
        let reqData = {
            feature
        }

        if (selectedShop) {
            reqData.selectedShop = selectedShop;
        }

        axiosInstance.post("/api/feature/request-access", reqData)
        .then((res) => {
            if (res.data.success) {
                toast.success(t("request-access-success"));
                setFeatureRequestSuccess(true);
            } else {
                toast.error(t("something-went-wrong"));
            }
        })
        .catch((err) => {
            !axios.isCancel(err) && toast.error(t("something-went-wrong"));
        })
        .finally(() => {
            setIsRequestAccessLoading(false);
        })
    }


    return (
        <MDBox
            position="relative"
            mt={2}
            mb={3}
            display="flex"
            flexDirection="column"
            justifyContent="center"
            alignItems="center"
            minHeight={isFeatureInBeta ? "70vh" : "23vh"}
            py={3}
            borderRadius="xl"
            sx={{
            backgroundImage: ({ functions: { linearGradient, rgba }, palette: {white, black } }) =>
                `${linearGradient(rgba(black.main, 0.20), rgba(white.main, 0.10))}, url(${bgImage})`,
            backgroundSize: "cover",
            backgroundPosition: "bottom",
            }}
      >
        <Grid
          container
          spacing={3}
          justifyContent="center"
          sx={{ position: "relative", textAlign: "center" }}
        >
          <Grid item xs={11} lg={8} display="flex" flexDirection="column" alignItems="center">
            <MDBox>
                <MDTypography variant="h4" color="white" sx={{fontWeight: "500"}}>
                    {t(featureBlocks[feature].title)}
                </MDTypography>
            </MDBox>
            <MDBox mt={1}>
              <MDTypography variant="button" color="white" fontWeight="regular" sx={{lineHeight:1}}>
                {t(featureBlocks[feature].desc)}
              </MDTypography>
            </MDBox>
            <MDBox mt={2.5} display="flex" flexDirection="row" justifyContent="center" width="100%">
                {featureBlocks[feature].link && <MDBox><MDButton
                    color="info"
                    variant="gradient"
                    size="small"
                    target="_blank"
                    component={Link}
                    to={featureBlocks[feature].link}
                    sx={{textTransform:"capitalize", fontSize: "13px", fontWeight: "500", mr:"10px !important"}}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#ffffff" viewBox="0 0 256 256"><path d="M184,32H72A16,16,0,0,0,56,48V224a8,8,0,0,0,12.24,6.78L128,193.43l59.77,37.35A8,8,0,0,0,200,224V48A16,16,0,0,0,184,32Zm0,16V161.57l-51.77-32.35a8,8,0,0,0-8.48,0L72,161.56V48ZM132.23,177.22a8,8,0,0,0-8.48,0L72,209.57V180.43l56-35,56,35v29.14Z"></path></svg>&nbsp;
                    {t("understand-rfm")}
                </MDButton></MDBox>}

                {isFeatureInBeta && <MDBox><MDButton
                    color={featureRequestSuccess ? "success" : "info"}
                    variant={featureRequestSuccess ? "contained" : "gradient"}
                    size="small"
                    ml={1}
                    onClick={handleRequestAccess}
                    sx={{textTransform:"capitalize", fontSize: "13px", fontWeight: "500", position: "relative"}}>
                    {isRequestAccessLoading && <CircularProgress size={20} color="white" sx={{mr: "0.5rem"}} />}
                    {!isRequestAccessLoading && !featureRequestSuccess && <RocketLaunchIcon sx={{fontSize: "1rem !important", mr: "0.5rem"}} />}
                    {!isRequestAccessLoading && featureRequestSuccess && <CheckCircleIcon sx={{fontSize: "1rem !important", mr: "0.5rem"}} />}
                    {!isRequestAccessLoading && !featureRequestSuccess && t("request-access")}
                    {isRequestAccessLoading && t("requesting-access")}
                    {featureRequestSuccess && t("request-sent")}
                </MDButton></MDBox>}


                {!isFeatureInBeta && <MDBox><MDButton
                    color="info"
                    variant="gradient"
                    size="small"
                    ml={1}
                    onClick={handleUpgrade}
                    sx={{textTransform:"capitalize", fontSize: "13px", fontWeight: "500"}}>
                    <MDBox component="img" src={premiumTag} alt="Brand" width="1rem" mr={0.8} />
                    {t("unlock-feature")}
                </MDButton></MDBox>}
            </MDBox>
          </Grid>
        </Grid>
      </MDBox>
    );
}

export default FeatureBlock;