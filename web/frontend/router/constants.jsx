export const ROUTE_KEY_PRODUCT_PERFORMANCE = 'product-performance';
export const ROUTE_KEY_BASKET_ANALYSIS = 'basket-analysis';
export const ROUTE_KEY_REPURCHASE_RATE = 'repurchase-rate';

export const ROUTE_PATH_TO_KEY_MAP = {
  "/products/performance": ROUTE_KEY_PRODUCT_PERFORMANCE,
  "/acquisition/products/performance": ROUTE_KEY_PRODUCT_PERFORMANCE,
  "/products/basket-analysis": ROUTE_KEY_BASKET_ANALYSIS,
  "/products/repurchase-rate": ROUTE_KEY_REPURCHASE_RATE
};

export const ROUTE_STATIC_DATA = {
  [ROUTE_KEY_PRODUCT_PERFORMANCE]: {
    name: "Product Performance",
    short_description: "Comprehensive dashboard displaying total orders, units sold, revenue, ad impressions, and key product performance metrics for the shop.",
    description: "This dashboard merges product-level ad data from Meta Advantage+ campaigns, Google P-Max campaigns and commerce data from Shopify. The table and insight cards present:\n\- Ad Metrics: Blended ad spend, Impressions, Clicks, CPC, CPM, CTR\n\- Commerce Metrics: Orders, Customers, Units Sold, Revenue, Avg Price\n\- Custom KPIs: ROAS (Return On Ad Spend), LTV, Repeat Rate\n\- Timeframe: All metrics are selectable by custom date range.\n\- Segmentation: Data is grouped by product, showing performance at the product SKU level.\n\- Some useful product insights which can be driven from this report:\n    1. Ad Spend Wasters (Zero Revenue Products): Products with non-zero blended ad spend and zero revenue.\n    2. Hero Products: High ad spend and above-average ROAS.\n    3. Potential Hero Products: Above-average ROAS and below-average ad spend.\n    4. Low ROAS Products: Ad spend higher than average with ROAS lower than average."
  },
  [ROUTE_KEY_BASKET_ANALYSIS]: {
    name: "Basket Analysis",
    short_description: "Basket analysis dashboard shows frequently bought-together products, order counts, sales, average order value, and new vs. returning customers.",
    description: "This dashboard analyzes combinations of products purchased together for a client's D2C brand. The table and insight cards present:\n- Product Pairs: Frequently bought-together product pairs (and optionally larger combinations).\n- Basket Metrics: Number of orders containing the pair, total sales from these orders, % of total orders, and average order value (AOV) for the pair.\n- Customer Segments: Breakout of metrics for new vs. returning customers (e.g., new orders, new sales, returning orders, returning sales).\n- Timeframe: Data is calculated over a selectable date range (e.g., last 3 months).\n- Combination Size: Supports configuration for analysis of pairs, triplets, or higher combinations—but primarily focused on product pairs.\n\nSome useful insights which can be driven from this report:\n    1. Common cross-sell product pairs that drive the most revenue or orders.\n    2. Pairs preferred by new customers vs. returning customers.\n    3. Opportunities to create product bundles or offers targeting frequent combinations.\n    4. Detection of underperforming products that rarely appear in any pair."
  },
  [ROUTE_KEY_REPURCHASE_RATE]: {
    name: "Repurchase Rate",
    short_description: "Repurchase dashboard highlights products driving repeat purchases, repurchase rates, new vs. returning customers, and customer lifetime value.",
    description: "This dashboard analyzes repurchase behavior for a client's D2C brand. The table and insight cards present:\n- First Order Product: The initial product purchased by new customers.\n- New Customers: Count of new customers whose first purchase was each product.\n- Repurchasers: Number and percentage of those customers who returned and bought again (any product).\n- Repurchased Same Product: Percentage of new customers who repurchased the exact same product.\n- Financial Metrics: Average order value (AOV) on first order, 90/180-day, and overall lifetime value (LTV).\n- Product Segments: Metrics can be broken down by individual products or product types.\n- Timeframe: Data is calculated over a selectable date range (e.g., last 3 months).\n\nSome useful insights which can be driven from this report:\n    1. Which first-purchase products drive the most repeat buyers.\n    2. Differences in repurchase rates and LTVs between products or product types.\n    3. Opportunities to promote products that encourage high retention and repeat purchases.\n    4. Identification of products with low repurchase or retention rates, indicating areas to improve."
  }
};

export const getRouteKeyFromPathname = (pathname) => {
  if (!pathname) return null;
  return ROUTE_PATH_TO_KEY_MAP[pathname] ?? null;
};

export const getRouteStaticDataFromPathname = (pathname) => {
  if (!pathname) return null;
  const routeKey = getRouteKeyFromPathname(pathname) ?? null;
  return ROUTE_STATIC_DATA[routeKey] ?? null;
};
