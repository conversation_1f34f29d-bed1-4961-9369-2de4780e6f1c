/** 
  All of the routes for the Material Dashboard 2 PRO React are added here,
  You can add a new route, customize the routes and delete the routes here.

  Once you add a new route on this file it will be visible automatically on
  the Sidenav.

  For adding a new route you can follow the existing routes in the routes array.
  1. The `type` key with the `collapse` value is used for a route.
  2. The `type` key with the `title` value is used for a title inside the Sidenav. 
  3. The `type` key with the `divider` value is used for a divider between Sidenav items.
  4. The `name` key is used for the name of the route on the Sidenav.
  5. The `key` key is used for the key of the route (It will help you with the key prop inside a loop).
  6. The `icon` key is used for the icon of the route on the Sidenav, you have to add a node.
  7. The `collapse` key is used for making a collapsible item on the Sidenav that contains other routes
  inside (nested routes), you need to pass the nested routes inside an array as a value for the `collapse` key.
  8. The `route` key is used to store the route location which is used for the react router.
  9. The `href` key is used to store the external links location.
  10. The `title` key is only for the item with the type of `title` and its used for the title text on the Sidenav.
  10. The `component` key is used to store the component of its route.
*/

// Material Dashboard 2 PRO React layouts
import FacebookAds from "@/layouts/integrations/facebook";
import MetricDashboard from "@/layouts/dashboards/metrics"
import IntegrationsDashboard from "@/layouts/integrations";
import CohortAnalysis from "@/layouts/lifetimevalue/cohortanalysis";
import CohortFrequency from "@/layouts/lifetimevalue/cohortfrequency";
import RepurchaseRate from "@/layouts/product/repurchaserate";
import ProductPerformance from "@/layouts/product/performance";
import CustomerSegments from "@/layouts/customer/customersegments";
import NewVsExistingCustomers from "@/layouts/customer/newvsexisting";
import CustomerRFM from "@/layouts/customer/rfm";
import CartAnalysis from "@/layouts/product/cartanalysis";
import Benchmarks from "@/layouts/benchmarks";
import AskPrashna from "@/layouts/askprashna";
import MDBadge from "@/components/MDBadge";
import FeatureRequests from "@/layouts/featurerequests";
import BlendedAds from "@/layouts/ads/blended";
import {GoogleAds, GoogleAnalytics} from "@/layouts/integrations/google";
import FacebookCampaigns from "@/layouts/integrations/facebookcampaigns";
// import Analytics from "@/layouts/dashboards/analytics";
// import Sales from "@/layouts/dashboards/sales";
// import ProfileOverview from "@/layouts/pages/profile/profile-overview";
// import AllProjects from "@/layouts/pages/profile/all-projects";
// import NewUser from "@/layouts/pages/users/new-user";
import Settings from "@/layouts/pages/account/settings";
// import Billing from "@/layouts/pages/account/billing";
// import Invoice from "@/layouts/pages/account/invoice";
// import Timeline from "@/layouts/pages/projects/timeline";
// import PricingPage from "@/layouts/pages/pricing-page";
// import Widgets from "@/layouts/pages/widgets";
// import RTL from "@/layouts/pages/rtl";
// import Charts from "@/layouts/pages/charts";
// import Notifications from "@/layouts/pages/notifications";
// import Kanban from "@/layouts/applications/kanban";
// import Wizard from "@/layouts/applications/wizard";
// import DataTables from "@/layouts/applications/data-tables";
// import Calendar from "@/layouts/applications/calendar";
// import NewProduct from "@/layouts/ecommerce/products/new-product";
// import EditProduct from "@/layouts/ecommerce/products/edit-product";
// import ProductPage from "@/layouts/ecommerce/products/product-page";
// import OrderList from "@/layouts/ecommerce/orders/order-list";
// import OrderDetails from "@/layouts/ecommerce/orders/order-details";
// import SignInBasic from "@/layouts/authentication/sign-in/basic";
// import SignInCover from "@/layouts/authentication/sign-in/cover";
// import SignInIllustration from "@/layouts/authentication/sign-in/illustration";
// import SignUpCover from "@/layouts/authentication/sign-up/cover";
// import ResetCover from "@/layouts/authentication/reset-password/cover";

// Material Dashboard 2 PRO React components
// import MDAvatar from "@/components/MDAvatar";

// No Material UI icons needed - using Phosphor icons consistently
import AssistantIcon from '@mui/icons-material/Assistant';
// @phosphor-icons/react imports for new category structure
import { 
  HouseIcon,
  RepeatIcon,
  ShoppingBagIcon,
  GearIcon,
  GlobeIcon,
  ChartBarIcon,
  UsersIcon,
  ChartBarHorizontalIcon,
  TargetIcon,
  StorefrontIcon as PhosphorStorefrontIcon,
  ArrowsClockwiseIcon,
  SlidersIcon,
  TrayIcon,
  StarIcon as PhosphorStarIcon,
  HeadsetIcon,
  ChatIcon as PhosphorChatIcon,
  CompassIcon,
  ClockCounterClockwiseIcon,
  PuzzlePieceIcon,
  TrendUpIcon,
  PlusIcon,
  PackageIcon
} from '@phosphor-icons/react';

import { useTranslation } from 'react-i18next';
import WorkspaceDashboard from "@/layouts/workspaces";
import { ROUTE_KEY_PRODUCT_PERFORMANCE, ROUTE_KEY_BASKET_ANALYSIS, ROUTE_KEY_REPURCHASE_RATE } from "@/router/constants";

const GoogleAdsIconBasic = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    x="0px"
    y="0px"
    width="24px"
    height="24px"
    viewBox="0 0 24 24">
    <path fill="#ffffff" d="M11.14 14.42L10 16.36c-.56-2.03-2.43-3.52-4.64-3.52-.37 0-.74.04-1.08.13l3.09-5.24C7.48 8.1 7.63 8.47 7.83 8.82L11.14 14.42zM5.36 14.344000000000001A3.328 3.328 0 1 0 5.36 21 3.328 3.328 0 1 0 5.36 14.344000000000001zM15.79 19.336c.919 1.592 2.994 2.122 4.546 1.218 1.589-.925 2.137-2.954 1.218-4.546L14.898 4.744c-.919-1.592-2.988-2.122-4.546-1.218C8.762 4.448 8.215 6.48 9.134 8.072L15.79 19.336z"></path>
  </svg>
);


const GoogleAdsIconColor = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    x="0px"
    y="0px"
    width="32px"
    height="32px"
    viewBox="0 0 50 50">
    <polygon fill="#ffc107" points="30.129,15.75 18.871,9.25 5.871,31.25 17.129,37.75"></polygon><path fill="#1e88e5" d="M31.871,37.75c1.795,3.109,5.847,4.144,8.879,2.379c3.103-1.806,4.174-5.77,2.379-8.879l-13-22 c-1.795-3.109-5.835-4.144-8.879-2.379c-3.106,1.801-4.174,5.77-2.379,8.879L31.871,37.75z"></path><circle cx="11.5" cy="34.5" r="6.5" fill="#43a047"></circle>
  </svg>
);

const routes = [
  // PRASHNA CATEGORY
  {
    type: "category",
    categoryKey: "prashna",
    key: "prashna",
    name: "ask-ai",
    icon: AssistantIcon,
    route: "/ask-ai",
    component: <AskPrashna initialSelectedOption="chat" />,
    stick: "top",
    items: [
      {
        name: "sidenav.new-chat",
        key: "new-chat",
        icon: PhosphorChatIcon,
        route: "/ask-ai",
        component: <AskPrashna initialSelectedOption="chat" />,
        hasSubItems: false
      },
      {
        name: "sidenav.discover",
        key: "discover",
        icon: CompassIcon,
        route: "/ask-ai/discover",
        component: <AskPrashna initialSelectedOption="discover" />,
        hasSubItems: false
      },
      {
        name: "sidenav.recent-chats",
        key: "recent-chats",
        icon: ClockCounterClockwiseIcon,
        route: "/ask-ai/chats",
        component: <AskPrashna initialSelectedOption="history" />,
        hasSubItems: false
      }
    ]
  },
  
  { type: "divider", stick: "top", key: "divider-2" },

  // DASHBOARD CATEGORY
  {
    type: "category",
    categoryKey: "dashboard",
    key: "dashboard",
    name: "dashboard",
    icon: HouseIcon,
    stick: "top",
    route: "/dashboard",
    component: <MetricDashboard />,
    items: []
  },

  // ACQUISITION CATEGORY
  {
    type: "category",
    categoryKey: "acquisition",
    name: "sidenav.acquisition",
    key: "acquisition",
    icon: TrendUpIcon,
    stick: "top",
    items: [
      {
        name: "sidenav.ads",
        key: "ads",
        icon: TargetIcon,
        hasSubItems: true,
        subItems: [
          { 
            name: "sidenav.blended-summary",
            route: "/acquisition/ads/blended", 
            key: "blended-ads",
            component: <BlendedAds />
          },
          { 
            name: "google-ads",
            route: "/acquisition/ads/google", 
            key: "google-ads",
            component: <GoogleAds />
          },
          { 
            name: "facebook-ads",
            route: "/acquisition/ads/facebook", 
            key: "facebook-ads",
            component: <FacebookAds />
          },
          // { 
          //   name: "facebook-campaigns",
          //   route: "/ads/facebook-campaigns", 
          //   key: "facebook-campaigns",
          //   component: <FacebookCampaigns />
          // },
        ]
      },
      {
        name: "website-traffic",
        key: "website-traffic",
        icon: GlobeIcon,
        route: "/acquisition/traffic",
        component: <GoogleAnalytics />,
        hasSubItems: false,
        feature: "google_analytics_overview"
      },
      {
        name: "product-performance",
        key: ROUTE_KEY_PRODUCT_PERFORMANCE,
        feature: "product_performance",
        icon: PackageIcon,
        route: "/acquisition/products/performance",
        component: <ProductPerformance />,
        hasSubItems: false
      },
    ]
  },

  // RETENTION CATEGORY
  {
    type: "category", 
    categoryKey: "retention",
    name: "sidenav.retention",
    icon: RepeatIcon,
    key: "retention",
    stick: "top",
    items: [
      {
        name: "cohort-analysis",
        key: "cohort-analysis",
        icon: ChartBarHorizontalIcon,
        hasSubItems: true,
        subItems: [
          { 
            name: "ltv-cohorts",
            route: "/retention/cohorts/ltv", 
            key: "ltv-cohorts",
            component: <CohortAnalysis preset="ltv-cohorts" />
          },
          { 
            name: "product-cohorts",
            route: "/retention/cohorts/product",
            key: "product-cohorts-retention",
            component: <CohortAnalysis preset="product-cohorts" />
          },
          {
            name: "location-cohorts",
            route: "/retention/cohorts/location", 
            key: "location-cohorts",
            component: <CohortAnalysis preset="location-cohorts" />
          },
          {
            name: "custom-cohorts",
            route: "/retention/cohorts/custom", 
            key: "custom-cohorts",
            component: <CohortAnalysis preset="custom-cohorts" />
          },
        ]
      },
      {
        name: "customer",
        key: "customer",
        icon: UsersIcon,
        hasSubItems: true,
        subItems: [
          { 
            name: "rfm-analysis",
            route: "/customers/rfm", 
            key: "rfm-segments",
            component: <CustomerRFM />
          },
          {
            name: "new-vs-returning-customers",
            route: "/customers/new-vs-returning",
            key: "new-vs-returning",
            component: <NewVsExistingCustomers />
          },
          {
            name: "customer-segments",
            route: "/customers/segments", 
            key: "customer-segments",
            component: <CustomerSegments />
          },
          { 
            name: "sidenav.purchase-frequency",
            route: "/customers/purchase-frequency", 
            key: "purchase-frequency",
            component: <CohortFrequency />
          },
        ]
      },
      {
        name: "sidenav.industry-benchmarks",
        key: "benchmarks",
        icon: ChartBarIcon,
        route: "/benchmarks",
        component: <Benchmarks />,
        hasSubItems: false
      },
      {
        name: "repurchase-rate",
        key: "repurchase-rate",
        icon: ArrowsClockwiseIcon,
        route: "/retention/products/repurchase-rate",
        component: <RepurchaseRate />,
        hasSubItems: false
      }
    ]
  },
  

  // PRODUCTS CATEGORY
  {
    type: "category",
    categoryKey: "products", 
    key: "products",
    name: "sidenav.products",
    icon: ShoppingBagIcon,
    stick: "top",
    items: [
      {
        name: "product-performance",
        key: ROUTE_KEY_PRODUCT_PERFORMANCE,
        icon: PackageIcon,
        feature: "product_performance",
        route: "/products/performance",
        component: <ProductPerformance />,
        hasSubItems: false
      },
      {
        name: "basket-analysis",
        key: ROUTE_KEY_BASKET_ANALYSIS,
        icon: ShoppingBagIcon,
        route: "/products/basket-analysis",
        component: <CartAnalysis />,
        hasSubItems: false
      },
      {
        name: "repurchase-rate",
        key: ROUTE_KEY_REPURCHASE_RATE,
        icon: ArrowsClockwiseIcon,
        route: "/products/repurchase-rate",
        component: <RepurchaseRate />,
        hasSubItems: false
      }
    ]
  },

  { type: "divider", stick: "bottom", key: "divider-3" },

  // Store element (special handling in NewSidenav)
  {
    type: "store-section",
    stick: "bottom",
    key: "store-section"
  },
  
  // INTEGRATIONS CATEGORY
  {
    type: "category",
    categoryKey: "integrations",
    key: "integrations",
    name: "integrations",
    icon: PuzzlePieceIcon,
    stick: "bottom",
    route: "/integrations",
    component: <IntegrationsDashboard />,
    items: []
  },

    // SETTINGS CATEGORY  
    {
      type: "category",
      categoryKey: "settings",
      key: "settings",
      name: "settings",
      icon: GearIcon,
      stick: "bottom",
      route: "/settings/store",
      component: <Settings selectedTab="store" />,
      items: [
        {
          name: "store",
          key: "store",
          icon: PhosphorStorefrontIcon,
      route: "/settings/store",
      component: <Settings selectedTab="store" />,
          hasSubItems: false
        },
        {
          name: "integrations",
          key: "integrations-settings",
          icon: PuzzlePieceIcon,
      route: "/settings/integrations",
      component: <Settings selectedTab="integrations" />,
          hasSubItems: false
        },
        {
          name: "global_filters.title",
          key: "global-filters",
          icon: SlidersIcon,
          route: "/settings/filters",
          component: <Settings selectedTab="global-filters" />,
          hasSubItems: false
        },
        {
          name: "scheduled-reports",
          key: "scheduled-reports",
          icon: TrayIcon,
          route: "/settings/scheduled-reports",
          component: <Settings selectedTab="scheduled-reports" />,
          hasSubItems: false
        },
        {
          name: "invite-member",
          key: "invite-member",
          icon: PlusIcon,
          route: "/settings/invite-member",
          component: <Settings selectedTab="invite-member" />,
          hasSubItems: false
        },
        {
          name: "subscription",
          key: "subscription",
          icon: PhosphorStarIcon,
          route: "/settings/subscription",
          component: <Settings selectedTab="subscription" />,
          hasSubItems: false
        },
        {
          name: "support-settings-title",
          key: "support",
          icon: HeadsetIcon,
          route: "/settings/support",
          component: <Settings selectedTab="support" />,
          hasSubItems: false
        }
      ]
    },


  // ============================================================================
  // HIDDEN ROUTES - These exist for routing but don't appear in sidebar
  // ============================================================================

  // Workspace settings - accessed by admins through special links/panels
  {
    type: "hidden",
    name: "Workspace Settings",
    key: "workspace-settings",
    route: "/workspace-settings",
    component: <WorkspaceDashboard />,
  },

  // Feature requests - accessed through help menu or special links
  {
    type: "hidden",
    name: "Feature Requests",
    key: "feature-requests",
    route: "/feature-requests",
    component: <FeatureRequests />,
  }
];

export default routes;
