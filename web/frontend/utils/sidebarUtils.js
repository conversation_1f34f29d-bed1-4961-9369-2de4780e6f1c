/**
 * Utility functions for sidebar width calculation and route management
 * This allows the layout to calculate the correct initial sidebar width before rendering
 */

export const SIDEBAR_WIDTH = 90; // Increased sidebar width to prevent text overflow
export const EXPANDED_PANEL_WIDTH = 256;

// Import routes to dynamically build route-to-category mapping
import routes from "@/router";

// Cache for route-to-category mapping to avoid rebuilding on every call
let routeToCategoryMapCache = null;

// Function to build route-to-category mapping from routes structure
const buildRouteToCategoryMap = () => {
  // Return cached map if already built
  if (routeToCategoryMapCache) {
    return routeToCategoryMapCache;
  }

  const routeToCategoryMap = {};
  
  routes.forEach(route => {
    // Handle category routes
    if (route.type === "category") {
      const categoryKey = route.categoryKey;
      
      // Map category-level routes (e.g., "/dashboard", "/integrations", "/settings/store")
      if (route.route) {
        routeToCategoryMap[route.route] = categoryKey;
      }
      
      // Map item routes if category has items
      if (route.items) {
        route.items.forEach(item => {
          // Map direct item routes
          if (item.route) {
            routeToCategoryMap[item.route] = categoryKey;
          }
          
          // Map sub-item routes
          if (item.subItems) {
            item.subItems.forEach(subItem => {
              if (subItem.route) {
                routeToCategoryMap[subItem.route] = categoryKey;
              }
            });
          }
        });
      }
    }
  });
  
  // Cache the result
  routeToCategoryMapCache = routeToCategoryMap;
  return routeToCategoryMap;
};

// Function to clear the cache (useful for testing or route updates)
export const clearRouteToCategoryMapCache = () => {
  routeToCategoryMapCache = null;
};

// Function to get the active category based on the current route
export const getActiveCategoryFromRoute = (pathname) => {
  const routeToCategoryMap = buildRouteToCategoryMap();

  // Direct route match
  if (routeToCategoryMap[pathname]) {
    return routeToCategoryMap[pathname];
  }
  
  // Check for partial matches (for nested routes)
  for (const [route, category] of Object.entries(routeToCategoryMap)) {
    if (pathname.startsWith(route)) {
      return category;
    }
  }
  
  return null;
};

// Function to calculate sidebar width based on route
export const calculateSidebarWidth = (pathname) => {
  const activeCategory = getActiveCategoryFromRoute(pathname);
  return activeCategory ? SIDEBAR_WIDTH + EXPANDED_PANEL_WIDTH : SIDEBAR_WIDTH;
};
