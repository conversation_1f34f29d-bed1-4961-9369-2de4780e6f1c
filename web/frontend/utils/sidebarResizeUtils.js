import { useState, useCallback, useRef, useEffect } from 'react';

/**
 * Custom hook for AI Insights sidebar resize functionality
 * @param {boolean} enabled - Whether resize functionality is enabled
 * @param {number} defaultWidth - Default width when no saved width exists
 * @param {number} minWidth - Minimum allowed width
 * @param {number} maxWidth - Maximum allowed width
 * @param {number} rightMargin - Right margin from screen edge
 * @returns {Object} Resize utilities and state
 */
export const useSidebarResize = ({
  enabled = true,
  defaultWidth = 400,
  minWidth = 300,
  maxWidth = 800,
  rightMargin = 24,
  storageKey = 'aiSidebarWidth'
} = {}) => {
  // Load initial width from localStorage or use default
  const [sidebarWidth, setSidebarWidth] = useState(() => {
    if (!enabled) return defaultWidth;
    const savedWidth = localStorage.getItem(storageKey);
    return savedWidth ? parseInt(savedWidth, 10) : defaultWidth;
  });

  const [isResizing, setIsResizing] = useState(false);
  const resizeRef = useRef(null);

  // Save width to localStorage
  const saveWidth = useCallback((width) => {
    if (enabled) {
      localStorage.setItem(storageKey, width.toString());
    }
  }, [enabled, storageKey]);

  // Mouse down handler - start resize
  const handleMouseDown = useCallback((e) => {
    if (!enabled) return;
    e.preventDefault();
    setIsResizing(true);
  }, [enabled]);

  // Mouse move handler - update width during resize
  const handleMouseMove = useCallback((e) => {
    if (!enabled || !isResizing) return;

    const newWidth = window.innerWidth - e.clientX - rightMargin;
    
    if (newWidth >= minWidth && newWidth <= maxWidth) {
      setSidebarWidth(newWidth);
      saveWidth(newWidth);
    }
  }, [enabled, isResizing, rightMargin, minWidth, maxWidth, saveWidth]);

  // Mouse up handler - end resize
  const handleMouseUp = useCallback(() => {
    if (!enabled) return;
    setIsResizing(false);
  }, [enabled]);

  // Add global mouse event listeners for resize
  useEffect(() => {
    if (!enabled) return;

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };
  }, [enabled, isResizing, handleMouseMove, handleMouseUp]);

  return {
    sidebarWidth,
    isResizing,
    resizeRef,
    handleMouseDown,
    // Expose setter for external control if needed
    setSidebarWidth: (width) => {
      setSidebarWidth(width);
      saveWidth(width);
    }
  };
};

/**
 * Resize handle component props generator
 * @param {Object} resizeUtils - Return value from useSidebarResize hook
 * @returns {Object} Props for the resize handle component
 */
export const getResizeHandleProps = (resizeUtils) => ({
  ref: resizeUtils.resizeRef,
  onMouseDown: resizeUtils.handleMouseDown,
  sx: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: '4px',
    cursor: 'col-resize',
    backgroundColor: 'transparent',
    zIndex: 1,
    '&:hover': {
      backgroundColor: 'rgba(0, 0, 0, 0.1)',
    },
    '&:active': {
      backgroundColor: 'rgba(0, 0, 0, 0.2)',
    },
  }
});
