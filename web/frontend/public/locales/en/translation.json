{"ltv-trend-title": "LTV Summary", "ltv-trend-tip": "Tip: Increase time period to see more accurate LTV", "explore-by-usecase": "Explore by usecase", "ltv-trend-tip-p1": "Total revenue from acquired customers in selected duration by total no. of orders", "ltv-trend-tip-p3": "Customers lifetime value by Month {{months}} after first order, from acquired customers in selected duration", "cust-quartile-name-1": "Checkout my Top customer Quartile by revenue", "cust-quartile-desc-1": "Run lookalike campaigns to target similar customers", "cust-segment-name-1": "New vs Returning Customers: Who’s More Important?", "cust-segment-desc-1": "with New vs Returning Customers Analysis", "rfm-name-1": "Find out customers who are most likely to churn?", "rfm-desc-1": "with RFM Analysis", "product-repurchase-name-1": "Export products that bring back most customers after first purchase?", "product-repurchase-desc-1": "Promote the products with a higher repeat rate", "m-cumrevpc-desc-subtitle": "See accumulated revenue/customer grow over the lifetime of a customer, after their first order.", "m-cumrevpc-desc": "Total revenue (including taxes) per customer for a cohort.", "m-cumrevpc-label": "Cumulative Revenue per Customer (LTV)", "m-cumorpc-label": "Cumulative Orders per customer", "m-cumorpc-desc": "Total orders per customer for a cohort.", "m-cumorpc-desc-subtitle": "See total orders/customer grow over the lifetime of a customer, after their first order.", "m-cust-label": "Customer Retention", "m-cust-desc": "The number of unique customers returning from the original cohort of new customers.", "m-orders-label": "First order and repeat orders", "m-orders-desc": "Total orders per cohort over time.", "m-rev-label": "Revenue Retention", "m-rev-desc": "Total sales (including taxes) per cohort over time.", "cust": "Customers", "m-newvse-label": "New vs. Returning Customers", "orders": "Orders", "m-newvse-o-label": "Orders by New Customers vs. Returning Customers", "revenue": "Revenue", "m-newvserev-label": "Revenue by New Customers vs. Returning Customers", "aov": "AOV", "m-newvseaov-label": "Average order value by New Customers vs. Returning Customers", "new": "New", "existing": "Returning", "cohort-analysis": "Cohort Analysis", "customer-ltv": "Customer LTV", "product": "Product", "repurchase-rate": "Repurchase Rate", "customer": "Customer", "rfm-analysis": "RFM Analysis", "customer-segments": "Customer Segments", "book-call": "Book a call", "champion-title": "Champion", "champion-desc": "Buy often, spend a lot, and made a purchase recently", "champion-tooltip": "Reward them. Ask them for reviews. They can be early adopters for new products and collections", "loyal-title": "Loyal", "loyal-desc": "Spend often and in good amounts. They are also engaged to relevant promotions. ", "loyal-tooltip": "Up-seller higher value products. Ask for reviews. Ask for referrals and engage with them; send free gift cards, pizzas, hand-written notes, etc. ", "promising-title": "Promising", "promising-desc": "Recent customers who spend decent money and have bought more than once. ", "promising-tooltip": "Offer subscription and loyalty programs. Provide recommendations. Ask for reviews. Send gifts, handwritten cards,etc. Make one-on-one personalized phone calls", "new_customers-title": "Recent Frugal Buyers", "new_customers-desc": "Recent buyers but most likely just one time buyers.", "new_customers-tooltip": "Provide post-sale support. Give them “early success”, offer free gift cards. Start a one-on-one relationship.", "need_attention-title": "Need Attention", "need_attention-description": "Customers who have average to below average RFM scores.", "need_attention-tooltip": "Make limited time offers. Recommend new products or services based on their passion/problem", "should_not_loose-title": "Should Not Lose", "should_not_loose-description": "Made large and often purchases but have not purchased in a long time.", "should_not_loose-tooltip": "Win back through special offers. Talk to them, survey them, don’t lose them to competition.", "sleepers-title": "Sleepers", "sleepers-description": "Spent good money but have not purchased in a long time.", "sleepers-tooltip": "Send emails and messages to reconnect. Provide helpful resources.", "lost-title": "Lost", "lost-description": "Lowest RFM scores, bought a small amount a long time ago", "lost-tooltip": "Try to revive interest with reach-out campaigns, otherwise ignore.", "abandoned_checkouts-title": "Abandoned Checkouts", "abandoned_checkouts-description": "New customers who initiated a checkout but did not purchase.", "abandoned_checkouts-tooltip": "Provide pre-sale support. Start building a relationship. Learn their wants/needs. Get feedback for future visitors also don’t abandon for the same reasons.", "callback_requests-title": "Callback Requests", "callback_requests-description": "Potential new customers who requested a callback via website.", "callback_requests-tooltip": "Call them back immediately. Learn their concerns and preferences.", "warm_leads-title": "Warm Leads", "warm_leads-description": "Bought once or twice fairly recently buy have not spent much.", "warm_leads-tooltip": "Reach out personally and provide proactive support. Learn about them and build relationships.", "cold_leads-title": "Cold Leads", "cold_leads-description": "Customer at risk of being Lost, with below average RFM scores.", "cold_leads-tooltip": "Reach out through SMS or email to revive interest. Get feedback.", "export-csv": "Export CSV", "cust-txn-freq": "Customer Purchase Frequency", "first-order-product": "First order product", "product-id": "Product ID", "new-cust": "New Customers", "repurchasers": "Repurchasers", "repurchase-perc": "Repurchased anything (%age)", "repurchase-same-perc": "Repurchased same product (%age)", "product-type": "Product Type", "1m": "1 Month", "2m": "2 Months", "3m": "3 Months", "6m": "6 Months", "1y": "1 Year", "all-time": "All Time", "repurchase-rate-desc": "Which products (as a first purchase) brought the most customers back to my store?", "last-3m": "Last 3 months", "last-6m": "Last 6 months", "wtd": "This Week", "ytd": "This Year", "qtd": "This Quarter", "last-1y": "Last 1 year", "last-2y": "Last 2 years", "last-3y": "Last 3 years", "last-5y": "Last 5 years", "daily": "Day", "weekly": "Week", "monthly": "Month", "quarterly": "Quarter", "yearly": "Year", "book-call-title": "<PERSON><PERSON>, Head of Growth at Datadrew", "book-call-cta": "Book a call with <PERSON><PERSON>", "book-call-desc": "Book a 30 min call to understand your customer lifetime value and other key metrics like retention rate through cohort analysis. We can also discuss how you can scale up online revenues profitably by building/adjusting your acquisition strategy", "later": "Later", "order-tag": "Order Tags", "sales-chan": "Sales Channels", "sales-chan-tooltip": "Include only these sales channels", "countries": "Countries", "states": "States", "filters": "Filters", "filters-column-filter-condition": "Filter Condition", "filters-column-value": "Value", "apply-filters": "Apply filters", "reset": "Reset", "first-purchase-period": "First purchase period", "breakdown-by": "Breakdown by", "repurchased-within": "Repurchased within", "product-repurchase-tip-1": "Products by repurchase rate", "product-repurchase-tip-2": "Find out which products turn your customers into repeat buyers", "product-perf-repurchase": "Product Performance by repurchase rate", "repurchased": "Repurchased", "same-product": "same product", "anything": "anything", "more": "more", "search": "Search", "ok": "Ok", "start-trial": "Start Free Trial", "count": "Count", "name": "Name", "customer-id": "Customer ID", "email": "Email", "phone": "Phone", "last-order-on": "Last Order On", "total-orders": "Total Orders", "rfm-score": "RFM Score", "rfm-segment": "RFM Segment", "p-when-annual": "when billed yearly", "p-product-repurchase": "Product Repurchase Behavior", "p-cust-seg": "Basic Customer Segments", "p-onboarding": "1-on-1 onboarding with actionable insights", "p-fb-insights": "Facebook Ads Insights", "p-data-unlimited": "Unlimited historical data", "p-rfm": "RFM Segments", "p-multi-store": "Multi-Store Dashboard", "p-support": "Priority support", "p-limited-data": "Data limited to last 3 months", "p-basic-plus": "Everything from Basic plan, plus", "p-fb-ads": "Facebook and Google Ads management", "p-creative-auto": "Creative Automation", "p-audience-launch": "Audience Launcher", "p-multi-chan-report": "Multi-channel reporting", "p-ads-strategy": "Ads Strategy ", "p-am": "Dedicated certified Account Manager", "p-faq": "Frequently Asked Questions", "p-contact-us": "We're here to help. If you have any questions, please contact us at", "p-partner-plan": "Partner-led, $399/mo", "p-faq-q1": "What does 'Fixed Pricing' mean?", "p-faq-a1": "Fixed pricing means that regardless of how much your ecommerce brand grows, your monthly subscription cost will remain constant. You won't experience any price hikes or surprises as your business expands.", "p-faq-q2": "How can you offer fixed pricing when others charge based on usage or monthly orders?", "p-faq-a2": "We believe in providing our customers with transparency and predictability. Our unique pricing model allows us to offer a consistent and affordable rate, no matter how successful your business becomes. We prioritize your growth without penalizing it with higher costs.", "p-faq-q3": "Can I cancel or change my subscription at any time?", "p-faq-a3": "Absolutely! You have the flexibility to modify or cancel your subscription at any time. We understand that business needs can change, and we aim to provide you with a hassle-free experience.", "p-faq-q4": "Is there a trial period to test your product before committing to a subscription?", "p-faq-q5": "Do you offer discounts for annual subscriptions?", "p-faq-a5": "Yes, we do offer discounts for customers who choose to pay yearly. You can save even more when you opt for an annual billing cycle.", "p-faq-q6": "How can I get in touch with your support team if I have questions or encounter issues?", "p-faq-a6": "You can reach our dedicated support team through our live in-app chat, or email - <EMAIL>. We're here to assist you with any inquiries or challenges you may face.", "contact-us": "Contact Us", "free": "Free", "p-ecom-growth": "Fixed Pricing. Grow Limitlessly without increasing costs.", "annual": "Yearly", "cust-order-freq": "Customers By Order Frequency", "cust-percentile": "Customer Percentiles", "cust-percentile-tooltip": "Your top {{perc}} of customers spent {{value}} or more", "cust-quartile-rev": "Customer Quartiles By Total Revenue", "quartile": "Quartile", "order-freq": "Order Frequency", "cust-quartile-rev-tip": "Export Top Quartile(Top 25%) and plug that audience in Meta as Lookalike, in Google as similar audiences and get a better RoAS. Engage top quartile customers through email sequences with new collections and upcoming offers etc.", "pro-tip": "Pro Tip", "new-customers": "New Customers", "existing-customers": "Returning Customers", "m-cumrevpc-tooltip-1": "This cohort purchased <b>{{display_value}}</b> per customer in their first {{tf_name}}.", "m-cumrevpc-tooltip-2": "This cohort purchased <b>{{display_value}}</b> per customer in their first order.", "m-cumrevpc-tooltip-3": "This cohort purchased <b>{{display_value}}</b> per customer (<b>{{display_cumulative_total}}</b> in total) {{index}} {{tf_name_pl}} later.", "m-cumrevpc-tooltip-4": "This cohort purchased <b>{{display_value}}</b> per customer (<b>{{display_cumulative_total}}</b> in total) {{index}} {{tf_name_pl}} later.", "m-cumorpc-tooltip-1": "This cohort purchased <b>{{display_value}}</b> orders per customer in their first {{tf_name}}", "m-cumorpc-tooltip-2": "This cohort purchased <b>{{display_value}}</b> order per customer in their first order", "m-cumorpc-tooltip-3": "This cohort purchased <b>{{display_value}}</b> orders per customer (<b>{{display_cumulative_total}}</b> in total), <b>{{index}}</b>  {{tf_name_pl}} later.", "m-cust-tooltip-1": "<b>{{display_value}}</b> customers from this cohort made a purchase in their first {{tf_name}}", "m-cust-tooltip-2": "<b>{{display_value}}</b> customers from this cohort made a purchase in their first order", "m-cust-tooltip-3": "<b>{{display_value}}</b> customers from this cohort made an additional purchase, {{index}} {{tf_name_pl}} later.", "m-orders-tooltip-1": "This cohort made <b>{{display_value}}</b> orders in their first {{tf_name}}", "m-orders-tooltip-2": "This cohort made <b>{{display_value}}</b> orders in total as their first purchase", "m-orders-tooltip-3": "This cohort made <b>{{display_value}}</b> additional orders, {{index}} {{tf_name_pl}} later.", "m-rev-tooltip-1": "This cohort purchased <b>{{display_value}}</b> in their first {{tf_name}}", "m-rev-tooltip-2": "This cohort purchased <b>{{display_value}}</b> in their first order.", "m-rev-tooltip-3": "This cohort purchased an additional <b>{{display_value}}</b>, {{index}} {{tf_name_pl}} later.", "cohort-analysis-cohort": "Cohort : {{cohort_size}} people who were acquired in <b>{{cohort_name}}</b>", "no-orders-found": "There are no orders matching the criteria. Please try changing the duration or relaxing any filters.", "something-went-wrong": "Something went wrong while processing your request. Please try again.", "acq-cust-time": "# of acquired customers in current {{time_frame}}", "repeat-cust-tooltip": "How many of acquired customers made a repurchase in the selected duration", "repeat-perc": "Repeat %", "acquired-in": "Acquired In", "acquired-tooltip": "Every customer who made their first purchase in this {{time_frame}} are grouped together as cohort", "cohort-freq-tooltip-1": "<b>{{display_value}}</b> customers acquired in this cohort (<b>{{display_percentage}}</b>) made {{index}}", "cohort-freq-tooltip-2": "Cohort : {{cohort_size}} people who were acquired in <b>{{cohort_name}}</b>", "txn-freq": "Transaction Category", "empty-chart": "No data available", "cohort-orders": "Cohort Orders", "cohort-revenue": "Cohort Revenue", "time-first-order": "{{tf_full}}(s) after first order", "see-table": "See Table", "total": "Total", "repeat-cust": "Repeat Customers", "perc-cust-repurchased": "%age customers repurchased", "aov-long": "Avg order value", "cust-order-1-title": "1 timers", "cust-order-2-title": "2 timers", "cust-order-3-title": "3 timers", "cust-order-4-title": "4 timers", "cust-order-4+-title": "4+ timers", "cust-order-1-desc": "# Customers who have purchased exactly 1 order", "cust-order-2-desc": "# Customers who have purchased exactly 2 orders", "cust-order-3-desc": "# Customers who have purchased exactly 3 orders", "cust-order-4-desc": "# Customers who have purchased exactly 4 orders", "cust-order-4+-desc": "# Customers who have purchased more than 4 orders", "benchmarks": "Benchmarks", "feature-requests": "Feature Requests", "benchmark-ltv-desc": "This chart shows how your customer lifetime value increases from first order to subsequent orders over 1 month, 3-months, 9 months and beyond.The different color band shows different competitor profiles. The green band shows the LTV values of top 25% competitors and so on.", "benchmark-ltv-title": "Lifetime value", "benchmark-repeat-perc-title": "Repeat percentage", "benchmark-repeat-perc-desc": "These KPIs represent the cumulative repeat behavior of your customers compared with the competitors.", "1m-ltv": "1 month LTV", "3m-ltv": "3 month LTV", "6m-ltv": "6 month LTV", "1y-ltv": "1 year LTV", "1m-repeat": "1 month Repeat %", "3m-repeat": "3 month Repeat %", "6m-repeat": "6 month Repeat %", "1y-repeat": "1 year Repeat %", "benchmark-footer": "*Industry benchmarks are determined by evaluating the performance of direct-to-consumer (D2C) brands within the same vertical over the course of the preceding year.", "start-trial-get-access": "Start free trial to get access", "benchmark-intro": "Introducing our new benchmarking feature.", "benchmark-intro-2": "Compare your store’s performance with the competing brands from same industry.", "benchmark-nodata": "We are gathering data for your Benchmarks", "benchmark-nodata-desc": "We are in the process of collecting information about your industry, which may take some time as Benchmarks need participation from a certain number of businesses in your cohort to provide accurate results. Once your Benchmarks are ready, we will promptly inform you.", "benchmark-frequency-title": "Frequency", "benchmark-frequency-desc": "Compare the percentage of customers who are 1 timers, 2 timers, 3 timers and so on with the competition.", "benchmark-growth-title": "Overall Growth", "benchmark-growth-desc": "These are the growth rate KPIs for your store across different growth parameters. Helps you understand how the competition has grown last month vs previous month.Get insights on which of these KPIs need your focus and intervention.", "revenue-growth-title": "Revenue Growth Rate", "new-customer-growth-title": "New Customer Growth Rate", "order-growth-title": "Order Growth Rate", "your-trend": "Your Trend", "you": "Your store", "industry": "Industry", "metric": "Metric", "top-25": "Top 25% stores", "avg": "Average stores", "bottom-25": "Bottom 25% stores", "b-1-timers": "1 timers", "b-2-timers": "2 timers", "b-3-timers": "3 timers", "b-4-5-timers": "4-5 timers", "b-5plus-timers": "5+ timers", "p-benchmarks": "Industry Benchmarks", "new-vs-returning-customers": "New vs Returning", "cancel-subs-alert-confirm": "Yes", "cancel-subs-alert-cancel": "No, Go Back!", "subscription-bar-title": "Hey {{firstName}}, Analyze all historical data, export RFM customer segments and a lot more", "subscription-bar-btn": "Upgrade today!", "integrations": "Integrations", "ltv-trend-insufficient-data": "*Insufficient data for this metric. Try increasing the time range.", "product-repurchase": "Product Repurchases", "time-period": "Time Period", "group-by": "Group By", "first-tf-tooltip": "{{tf_full}} 0 represents the same {{time_frame}} as the first purchase", "last-7d": "Last 7 days", "last-30d": "Last 30 days", "facebook-ads": "Facebook Ads", "source-disconnected-success": "Disconnected successfully.", "disconnecting-facebook-marketing": "Disconnecting Account will result in data not being synced. You will not be able to see any data in the dashboard.", "are-you-sure": "Are you Sure?", "back-btn": "back", "next-btn": "next", "disconnect-btn": "Disconnect", "connect": "Connect", "connected": "Connected", "configure": "Configure", "sync": "Sync", "sync-step-title": "Syncing Data and Creating Reports", "sync-step-description": "Your Facebook Ads account is now connected. We will start syncing data in a few minutes.", "account-id": "Ad Account ID", "status": "Status", "started-at": "Started At", "source-already-connected": "Already connected.", "source-already-configured": "Already configured.", "connect-step-title": "Connect Facebook Ads", "connect-step-description": "Connect to see Marketing performance metrics in your dashboard.", "source-configured-success": "Configured successfully.", "configure-step-title": "Configure Facebook Account", "configure-step-description": "Add Facebook Ad Account you want to sync. The Ad account ID number is in the account dropdown menu or in your browser's address bar.", "find-fb-account": "Check How to Find your Facebook ad account ID number", "saved": "Saved", "save": "Save", "compared-with": "Compared with", "no-data-time-period": "No Data available for comparison dates", "n-a": "n/a", "spend": "Spend", "spend-description": "Total Spend on Facebook Ads for the selected time period", "revenue-description": "Total Revenue from ads for the selected time period", "clicks-link": "<PERSON><PERSON><PERSON> (Link)", "clicks-link-description": "Total number of link clicks on your ads", "ctr-all": "CTR (All)", "ctr-all-description": "Percentage of times people saw your ad and performed a click", "ctr-link": "CTR (Link)", "ctr-link-description": "Percentage of times people saw your ad and performed a link click", "cpc-link": "CPC (Link)", "cpc-link-description": "Average cost per link click", "cost-per-purchase": "Cost Per Purchase", "cost-per-purchase-description": "Average cost per purchase on your website", "cost-per-add-to-cart": "Cost Per <PERSON> To Cart", "roas": "ROAS", "roas-description": "Return on Ad Spend", "website-purchases": "Website Purchases", "website-purchases-description": "Total number of purchases on your website", "impressions": "Impressions", "impressions-description": "Total number of times someone saw your ad", "campaign-name": "Campaign", "adset": "Adset", "select-metric": "Select Metric", "account-summary": "Account Summary", "dashboard": "Dashboard", "total-revenue": "Revenue", "total-revenue-description": "Total revenue generated by the store", "new-customer-orders": "New Customer Orders", "new-customer-orders-description": "Number of orders placed by new customers", "new-customer-revenue": "New Customer Revenue", "new-customer-revenue-description": "Revenue generated by new customers", "returning-customer-revenue": "Returning Customer Revenue", "returning-customer-revenue-description": "Revenue generated by returning customers", "new-customer-revenue-pct": "New Customer Revenue %", "new-customer-revenue-pct-description": "Percentage of revenue generated by new customers", "returning-customer-revenue-pct": "Returning Customer Revenue %", "returning-customer-revenue-pct-description": "Percentage of revenue generated by returning customers", "new-customer-count": "New Customers", "new-customer-count-description": "Number of new customers", "customer-count": "Customers", "customer-count-description": "Number of customers", "returning-customer-count": "Returning Customers", "returning-customer-count-description": "Number of returning customers", "new-customer-count-pct": "New Customers %", "new-customer-count-pct-description": "Percentage of customers who are new", "returning-customer-count-pct": "Returning Customers %", "returning-customer-count-pct-description": "Percentage of customers who are returning", "order-count": "Orders", "order-count-description": "Number of orders placed", "returning-customer-order-count": "Returning Customer Orders", "returning-customer-order-count-description": "Number of orders placed by returning customers", "new-customer-order-count-pct": "New Customer Orders %", "new-customer-order-count-pct-description": "Percentage of orders placed by new customers", "returning-customer-order-count-pct": "Returning Customer Orders %", "returning-customer-order-count-pct-description": "Percentage of orders placed by returning customers", "aov-description": "Average order value", "new-customer-aov": "New Customer AOV", "new-customer-aov-description": "Average order value of new customers", "returning-customer-aov": "Returning Customer AOV", "returning-customer-aov-description": "Average order value of returning customers", "revenue-last-1m": "Revenue Last 1 Month", "revenue-last-1m-description": "Revenue generated in the last 1 month", "revenue-last-3m": "Revenue Last 3 Months", "revenue-last-3m-description": "Revenue generated in the last 3 months", "revenue-last-6m": "Revenue Last 6 Months", "revenue-last-6m-description": "Revenue generated in the last 6 months", "revenue-last-1y": "Revenue Last 1 Year", "revenue-last-1y-description": "Revenue generated in the last 1 year", "sales": "Sales", "show-chart": "Show Chart", "hide-chart": "Hide Chart", "deepdive-btn": "See Full Report", "rfm-intro-card-title": "Recency Frequency Monetary (RFM) Analysis", "rfm-intro-card-description": "The R-F-M analysis is a framework for determining customer segments for marketing and retention campaigns. It’s a function of three factors:", "rfm-intro-card-description-1": "Recency: When the customer last made a purchase", "rfm-intro-card-description-2": "Frequency: How often a customer would make a purchase", "rfm-intro-card-description-3": "Monetary: How much does a customer spend", "understand-rfm": "Understand How it works", "rfm-summary": "RFM Summary", "cohort-analysis-fixed-time": "Changing the time period will not affect this chart. It requires a minimum time period. See Full Report to change the time period.", "cohort-analysis-fixed-time-desc": "Last 3 months", "welcome-aboard": "Welcome aboard! 🎉", "data-sync-status-subtitle": "Let's get started with your Ecommerce Growth Platform.", "generating-insights": "Creating reports", "data-sync-status-desc-1": "It usually takes an hour. Once done, you'll be directly taken to the the dashboard.", "data-sync-status-desc-2": "If you have any questions, please reach out to us through in-app chat or email us at ", "cohort-analysis-blog-title": "How Cohort Analysis can help grow your business?", "product-repurchase-blog-title": "Identify products with high repeat purchases", "rfm-analysis-blog-title": "All your customers are not created equal.", "m-aov-tooltip-1": "This cohort spent <b>{{display_value}}</b> on average in their first {{tf_name}}", "m-aov-tooltip-2": "This cohort spent <b>{{display_value}}</b> on average in their first order.", "m-aov-tooltip-3": "This cohort spent <b>{{display_value}}</b> on average, {{index}} {{tf_name_pl}} later.", "avg-order-value": "Average Order Value", "m-aov-desc": "Average order value per cohort over time.", "view-cust": "view customers", "cohort-size": "Cohort Size", "translate": "Choose a different language", "p-monthly": "Monthly", "mo": "mo", "see-other-plans": "see other plans", "show-first-order": "Show first order", "report-feedback-text": "Rate the usefulness of this report", "benchmark-rfm-desc": "Compare the percentage of customers (who made a purchase in last 12 months) for each RFM segment for your store vs similar stores in your industry", "last-m": "Last Month", "b-tooltip-top-25-metric": "Your stores falls in <strong style=\"color:#4CAF50\">Top 25% stores</strong> in your industry for this metric", "b-tooltip-avg-metric": "Your stores falls in <strong style=\"color:#fb8c00\">Average stores</strong> in your industry for this metric", "b-tooltip-bottom-25-metric": "Your stores falls in <strong style=\"color:#F44335\">Bottom 25% stores</strong> in your industry for this metric", "loyal-tip-title": "Percentage of Loyal Customers", "b-2-timers-tip-title": "Percentage of 2 timers", "revenue-growth-tooltip": "How much did the revenue of your store grow in the last month from the previous month", "new-customer-growth-tooltip": "How much did the new customer count of your store grow in the last month from the previous month", "order-growth-tooltip": "How much did the order count of your store grow in the last month from the previous month", "visit-help-center": "Visit Help Center", "rfm-benchmarks": "RFM Benchmarks", "shopify-store": "Shopify Store", "settings": "Settings", "user-settings": "User Settings", "basic-info": "Basic Info", "full-name": "Full Name", "work-email": "Work Email", "what-best-describes-you": "What best describes you?", "time-zone": "Timezone", "phone-number": "Phone Number", "art-photography": "Art & Photography", "animals-pet-supplies": "Animals & Pet Supplies", "baby-toddler": "Baby & Toddler", "clothing-fashion": "Clothing & Fashion", "jewelry-accessories": "Jewelry & Accessories", "electronics": "Electronics", "food-drink": "Food & Drink", "home-garden": "Home & Garden", "furniture": "Furniture", "hardware": "Hardware", "health-beauty": "Health & Beauty", "sports-recreation": "Sports & Recreation", "toys-games": "Toys & Games", "stationary": "Stationary", "other": "Other", "support-settings-title": "Support & Feedback", "help-center": "Help Center", "help-center-description": "Learn more about Datadrew", "book-a-call-description": "Schedule a call with our team", "feature-requests-description": "Submit & track your feature requests", "support-description": "We are here to help. If you have any questions or need any help, please reach out to us.", "see-more": "See more", "write-to-us": "Write to us", "support": "Support", "email-reports-title": "Email Reports", "email-reports-description": "Here you can update your automated reports settings.", "weekly-report": "Weekly Performance Report", "weekly-report-desc": "Performance report with important KPIs incl. Sales, New Customers, FB Ad Spend, and more.", "report-emails": "Send report to these emails (comma-separated)", "connected-accounts": "Connected Accounts", "manage-integ": "Here you can setup and manage your integration settings.", "google-ads": "Google Ads", "measure-google": "Measure your Google Ads performance", "coming-soon": "Coming Soon", "ad-account-id": "Ad Account ID", "connected-account": "Connected account", "connect-your-account": "Connect your account", "finish-fb-connection": "Finish connecting your Account", "view-dash": "View Dashboard", "store": "Store", "subscription": "Subscription", "shopify-merchant": "Shopify Merchant", "agency": "Freelancer or agency Professional", "saved-successfully": "Saved successfully", "onboarding-title": "👋  Help us know you better! ", "full-name-validation": "Please add your full name!", "enter-valid-email": "Please add a valid email", "enter-valid-phone": "Please add a valid phone", "select-industry": "Please choose your industry!", "user-type": "What best describes you?", "submit": "Submit", "user-type-label": "Tell us about yourself", "inactive": "Inactive", "active": "Active", "subscription-description": "Here you can manage your subscription and billing information.", "data-sync": "Data Sync Status", "data-sync-description": "Data is refreshed every 12 hours. For the first time, it may take up to 24 hours.", "last-synced": "Last synced", "location": "Location", "order-type-tooltip": "Include customers having these product type in their first order", "store-settings": "Store settings", "store-website": "Store website", "remove-from-workspace": "Remove from workspace", "theme-settings": "Theme settings", "stores-in-your-workspace": "Stores in your workspace", "my-workspace": "My workspace", "manage-workspaces": "Manage workspaces", "switch-workspace": "Switch workspace", "select-workspace": "Select a workspace", "workspace-stores": "Workspace stores", "workspace-members": "Workspace members", "workspace-management": "Workspace management", "no-shop-selected": "No shop selected", "add-new-store": "Add a new store", "frequency-upto-x": "Frequency upto {{count}}", "p-trusted-title": "Trusted by 1000+ D2C companies around the world", "coupon-code": "Coupon code", "apply": "Apply", "apply-changes": "Apply changes", "column-visibility": "Column visibility", "quick-actions": "Quick actions", "columns": "Columns", "search-columns": "Search columns", "column-visibility-tooltip": "Choose which columns to display in the table. You can search for specific columns and organize them by category.", "select-all": "Select all", "select-none": "Select None", "product-information": "Product information", "revenue-sales": "Revenue & sales", "ad-spend": "Ad spend", "meta-ads-metrics": "Meta ads metrics", "google-ads-metrics": "Google ads metrics", "quick-toggles-by-category": "Quick toggles by category", "required": "required", "remove": "Remove", "i-hav-coupon": "I have a coupon code", "coupon-applied": "Coupon Applied 🎉", "modify-subscription": "Modify Subscription", "price-50-off-1m": "$19.5 (50% Off) for 1st Month", "fo-aov": "First order AOV", "ltv": "LTV", "ltv-90d": "90 day LTV", "ltv-180d": "180 day LTV", "need-help": "Need Help?", "acquisition_period": "Acquisition Period", "product_title": "Product", "product_type": "Product Type", "product_vendor": "Product Vendor", "sku": "SKU", "product_tags": "Product Tag", "order_tags": "Order Tag", "customer_tags": "Customer Tag", "shipping_address_country": "Country", "shipping_address_province": "State", "shipping_address_city": "City", "ltv-cohorts": "LTV Cohorts", "product-cohorts": "Product Cohorts", "location-cohorts": "Location Cohorts", "custom-cohorts": "Custom Cohorts", "first-order-tooltip": "Every customer who made their first purchase with this {{breakdown}} are grouped together as cohort", "first-order-breakdown": "First Order {{breakdown}}", "cities": "Cities", "acq-cust-cohort": "# of acquired customers in current cohort", "single-product-order": "Single Product Orders", "single-product-orders-tooltip": "Include customers who purchased only one product in their first order. By default, Only single product orders are included.", "filters-tooltip": "Use filters to include or exclude customers from the cohort", "enabled": "Enabled", "disabled": "Disabled", "trend-more-cohorts": "Showing first 5 cohorts. Select specific cohorts for comparison", "show-all-cohorts": "Show all cohorts", "refine-cohorts": "Refine cohorts", "missing-or-empty": "Missing/ Empty", "missing-data-tooltip": "All customers with missing, empty or uncategorized breakdown are grouped together as a cohort", "transaction-frequency": "Purchase Frequency", "verif-email-sent": "Please check your email for verification link", "verify-your-email": "Verify your email", "resend-verif-email": "Resend Verification Email", "check-verify": "Please check your email to verify your account and get started.", "sign-in-diff-acc": "Sign In with a different account", "email-sent": "<PERSON><PERSON>", "add-new-account": "Add a new account", "add-store-desc": "Authenticate with Shopify to link a new store to your account.", "store-address": "Store address", "store-linking-success": "Store linked successfully", "store-linking-failed": "Store linking failed", "linking-store": "Linking store", "multi-store-view": "Add multiple stores to view them in a single dashboard", "store-unlinking-failed": "Store unlinking failed", "store-unlinking-success": "Store unlinked successfully", "multi-store-dialog-desc": "Create a Datadrew Account, and link multiple stores to a single account. Connect various stores for effortless switching between them.", "email-already-verified": "Email already verified", "cancel": "Cancel", "create-acc-login": "Create Account / SignIn", "multi-store-dialog": "Connect multiple Shopify stores", "multi-store-sign-in": "Multi-store Sign In", "add-team-member": "Invite team member", "change-password": "Change Password", "workspace": "Workspace", "sign-out": "Sign Out", "connected-stores": "Connected Stores", "manage-connected-stores": "Here you can manage your connected Shopify stores and add new stores.", "managing-multi-stores": "Managing multiple Shopify stores?", "current-password": "Current Password", "new-password": "New Password", "confirm-new-password": "Confirm New Password", "password-requirements": "Password requirements", "please-follow-guide": "Please follow this guide for a strong password", "update-password": "update password", "one-special-characters": "One special characters", "min-6-characters": "Min 6 characters", "one-number": "One number (2 are recommended)", "change-it-often": "Change it often", "user": "User", "plan": "Plan", "switch-or-add-store": "You can switch to another store or add a new one while you wait.", "switch-to-free": "Downgrade to Free Plan", "unlink": "Unlink", "store-unlinking-warning": "Unlinking will result in removing this store from this account. You will need to re-authenticate to link this store again.", "confirm": "Confirm", "passwords-dont-match": "Passwords don't match", "password-updated": "Password changed successfully", "weak-password": "Password is too weak. Follow the password requirements.", "requires-recent-login": "This action requires recent login to your account. Please logout & login again and try again.", "beta": "Beta", "section-na": "Section not available", "login-shopify": "Login with Shopify", "multi-store-sign-up": "Add stores", "section-shopify-metrics": "Store Sales", "section-new-vs-existing": "New vs Returning", "section-ltv-cohort-analysis": "LTV Cohort Analysis", "section-ltv-growth": "LTV Summary", "section-facebook-ads": "Facebook Ads", "section-growth-benchmarks": "Growth Benchmarks", "section-explore-by-usecase": "Explore by Usecase", "section-ltv-benchmarks": "LTV Benchmarks", "section-repeat-benchmarks": "Repeat Benchmarks", "section-frequency-benchmarks": "Frequency Benchmarks", "section-rfm-benchmarks": "RFM Benchmarks", "section-product-scatter": "Product Repurchase Rate", "section-product-performance": "Product Performance", "product-performance": "Product Performance", "section-cust-quartiles": "Revenue Quartiles", "section-cust-order-freq": "Order Frequency", "section-cust-percentiles": "Percentiles", "section-txn-freq": "Purchase Frequency", "section-rfm-segments": "RFM Segments", "section-rfm-customers": "Segment Customers", "section-cohort-analysis": "Cohorts Table", "section-retention-charts": "Retention Charts", "section-facebook-overview": "Account Summary", "section-facebook-breakdowns": "Metric Explorer", "section-facebook-campaigns": "Campaign Performance", "average": "Average", "upgrade-to-annual": "Upgrade to Annual", "switch-to-monthly": "Switch to Monthly", "p-ltv-cohorts": "LTV Cohort Analysis", "p-breakdown-cohorts": "Product & Location Cohorts", "basket-analysis": "Basket Analysis", "acquisition-period": "Acquisition Period", "product-comb": "Products bought together", "total_sales": "Total Sales", "order_percentage": "% Orders", "new_orders": "New Orders", "new_total_sales": "New Sales", "repeat_orders": "Returning Orders", "repeat_total_sales": "Returning Sales", "min-product-count": "Min. Product", "section-basket-analysis": "Basket Analysis", "add": "Add", "op_is": "is", "op_is_not": "is not", "op_contains": "contains", "op_contains_not": "does not contain", "op_starts": "starts with", "op_ends": "ends with", "op_any": "is any", "op_any_not": "is not any", "first-order": "First Order", "filter-section-tooltip": "Include or exclude selected values", "f-placeholder": "All values", "source_name": "Source Name", "order-tag-tooltip": "Include or exclude customers having these tags in their first order", "countries-tooltip": "Include or exclude customers from these countries only", "states-tooltip": "Include or exclude customers from these states only", "cities-tooltip": "Include or exclude customers from these cities only", "product-title-tooltip": "Include or exclude customers who purchased these products in their first order", "beta-alert": "This report is currently in its Beta phase. We are actively seeking feedback for improvements. If you have any suggestions or comments, please let us know.", "price-20-off-3m": "$31.2 (20% Off) for 3 Months", "ltvxm": "Avg. {{months}} month LTV", "new-cust-tootltip": "Total number of new customers acquired in the selected duration", "fb-ads-report-tooltip": "To check or add a different Ad account, See Full Report", "tag-klaviyo": "Tag in Klaviyo", "rfm-segments": "RFM Segments", "tag-klaviyo-description": "Tag Klaviyo Profiles with Datadrew RFM Segments", "disconnecting-klaviyo": "Disconnecting your account will pause data synchronization. You can reconnect at any time.", "configure-klaviyo-title": "<PERSON><PERSON>", "configure-klaviyo-description": "Connect your Klaviyo account to tag profiles in Klaviyo based on RFM segmentation.", "new-klaviyo-key": "Create a new <b>'Full Access Key'</b> from the <b>Settings > API Keys</b> section in your Klaviyo account.", "create-klaviyo-key": "Generate a new private API Key", "klaviyo-api-key": "Klaviyo API Key", "jobs-history": "History", "rfm-sync-disclaimer": "Note: If a profile does not exist for a user, <PERSON><PERSON><PERSON><PERSON> will automatically create a new profile.", "sync-submitted": "Your request has been submitted. Klaviyo profile sync will start shortly.", "running": "In Progress", "finished": "Completed", "failed": "Unsuccessful", "pending": "Pending", "learn-more": "Learn More", "r-axis-title": "Recency (days)", "fm-axis-title": "Frequency + Monetary (orders + revenue)", "no-compare": "No comparison", "prev-period": "Previous Period", "prev-year": "Previous Year", "compare-period-tooltip": "Choose a time period for comparison", "slack-connect": "Connect Slack to receive weekly reports on a Slack channel", "scheduled-reports": "Scheduled Reports", "report-emails-required": "Please add at least one email to receive reports", "disconnecting-slack": "Disconnecting slack will stop sending reports to your slack channel", "channel": "Channel", "live-chat": "Live Chat", "live-chat-description": "Chat with us for any queries or feedback", "chat-now": "Chat now", "tag-segment": "Tag {{segment}} customers in Klaviyo", "tag-all-segments": "Update all segments in Klaviyo every week", "segment-details": "Segment details", "rfm-intro-desc": "RFM stands for <b>Recency</b>, <b>Frequency</b>, and <b>Monetary value</b>, representing key customer attributes: days since the last order, total number of orders, and Lifetime Value. Customers are categorized into five groups for each attribute and placed on the map below, each associated with a specific customer segment.", "last-update": "Last Update", "next-update": "Next Update", "update-schedule": "Update Schedule", "update-now": "Update Now", "setting-conn": "Setting up connection", "google-analytics": "Google Analytics", "connect-google-ads": "Connect Google Ads", "connect-google-analytics": "Connect GA4", "connect-desc-google-analytics": "Connect to see your website’s analytics metrics in your dashboard", "connect-desc-google-ads": "Connect Google Ads to view daily ad performance and campaign details", "configure-google-analytics": "Configure GA4", "configure-google-ads": "Configure Google Ads", "configure-desc-google-analytics": "Select GA4 property IDs you want to sync. The property ID is in the admin section of your GA4 account", "configure-desc-google-ads": "Select Google Ads account(s) you want to sync. The Ad account ID number is in the account dropdown menu or in your browser's address bar", "sync-desc-google-analytics": "Your GA4 account is now connected. We will start syncing data in a few minutes", "sync-desc-google-ads": "Your Google Ads account is now connected. We will start syncing data in a few minutes", "disconnecting-google-analytics-data-api": "Disconnecting GA4 will stop syncing data from your GA4 account", "disconnecting-google-ads": "Disconnecting Google Ads will stop syncing data from your Google Ads account", "select-option": "Please select at least one option", "properties": "Properties", "google-customer-ids": "Customer IDs", "arpu": "ARPU", "arpu-description": "Average revenue per customer", "new-customer-arpu": "New Customer ARPU", "new-customer-arpu-description": "Average revenue per new customer", "returning-customer-arpu": "Returning Customer ARPU", "returning-customer-arpu-description": "Average revenue per returning customer", "exact-basket-size": "Use exact basket size of {{size}}", "p-date-block-title-3-m": "You can only view data from the past 3 months with your current plan", "p-date-block-title-1-yr": "You can only view data from the past year with your current plan", "p-date-block-subtitle": "Upgrade to access additional historical data", "p-automated-reports": "Automated Email and Slack Reports", "upgrade": "Upgrade", "p-limited-data-1y": "Data limited to the last 1 year", "p-basket-analysis": "Basket Analysis", "p-new-vs-returning": "New vs Returning Customers", "p-plus-growth": "Everything from Growth plan, plus", "p-rfm-tagging": "RFM Tagging in Klaviyo", "p-multi-ads-account": "Multiple accounts for Facebook, Google and GA4", "p-paywall-title-default": "Unlock all features", "ltv-cohorts-desc": "Track customer cohorts based on their first purchase and analyze their lifetime value (LTV) and repeat purchase behavior. Use this report to assess the impact of your marketing efforts or product launches", "product-cohorts-desc": "Segment customers by the product of their first purchase, uncovering which product types drive higher lifetime value and retention, enabling more informed inventory and marketing strategies", "location-cohorts-desc": "Analyze customer behavior by location, helping you tailor marketing strategies, optimize regional inventory, and identify geographical trends that impact customer retention and sales", "custom-cohorts-desc": "Leverage custom cohorts based on order or customer tags, such as VIP customers or first-time buyers, to gain deeper insights into behavior, enabling highly personalized marketing and customer engagement strategies", "unlock-feature": "Unlock this feature", "transaction-frequency-desc": "Track how many purchases each customer cohort generates to measure the effectiveness of your acquisition strategy and optimize efforts to boost repeat purchases", "basket-analysis-desc": "Discover which products customers frequently purchase together, helping you identify upsell and cross-sell opportunities across your product pages, cart, and post-purchase offers", "rfm-segments-desc": "Identify loyal customers, promising buyers, and those at risk of churning, enabling targeted marketing and personalized engagement to boost retention and sales", "new-vs-returning-desc": "Track how new and returning customers contribute to your business, enabling you to allocate resources effectively by identifying which group drives more revenue and profitability and guiding strategies for both acquisition and retention", "p-paywall-title-feature-plan": "Unlock this feature with {{plan}} plan", "p-paywall-title-plan": "Upgrade to {{plan}} plan today", "p-paywall-no-recommed": "Please reach out to us via in-app chat for more information", "p-ltv-cohort-filters": "LTV Cohorts with Filters", "subscription-disclaimer": "Your app subscription is managed through Shopify and can be viewed in your Shopify admin", "free-plan-active": "You are currently on the Free plan", "free-plan-upgrade": "Unlock all reports, features and integrations by upgrading to a paid plan", "see-all-plans": "See all plans", "p-drive-growth": "Data That Drives Growth", "paid-plan-active": "You are currently on the {{planName}} plan", "upgrade-now": "Upgrade Now!", "remove-coupon": "Remove applied coupon", "coupon-removed": "Coupon removed", "invalid-coupon": "Invalid coupon code", "p-faq-a4": "Yes, we offer a trial period so you can explore our product and its features before making a commitment. It's a risk-free way to ensure our solution aligns with your business requirements. If you need an extension to your free trial, please contact <NAME_EMAIL>.", "upgrade-to-see": "Upgrade to view this report", "upgrade-rfm": "Upgrade to view & export customers", "facebook-overview-desc": "Get a quick snapshot of your Facebook Ads account performance, including total spend, revenue, and key metrics like CTR, CPC, and ROAS. Use this report to track the effectiveness of your ad campaigns and optimize your ad spend", "p-data-export-unlimited": "Unlimited Data Export", "plan-active": "Your plan is now active!", "plan-active-desc": "Welcome aboard! Your plan is now active, and you’re all set to explore Datadrew Analytics", "plan-active-help": "Need help? Our team is here to assist you anytime.", "start-exploring": "Start Exploring", "rfm-benchmarks-desc": "See how your store’s customer distribution across RFM segments compares to top stores in your industry", "export": "Export", "export-to-csv": "Export to CSV", "export-to-xlsx": "Export to XLSX", "google_ads": {"cost": "Spend", "cost-description": "Total spend on Google Ads for the selected time period", "clicks": "<PERSON>licks", "clicks-description": "The total number of clicks your ads received", "ctr": "CTR", "ctr-description": "Click-through rate (CTR), calculated as the percentage of people who clicked on your ad after seeing it. CTR = Clicks / Impressions", "cost_per_conversion_all": "Cost per Conversion (All)", "cost_per_conversion_all-description": "The average cost of each conversion across all conversion types", "conversions": "Conversions", "conversions-description": "Total number of conversions resulting from ad interactions", "cost_per_conversion": "Cost per Conversion", "cost_per_conversion-description": "The average cost per conversion for specific tracked conversion actions", "conversions_value": "Conversion Value", "conversions_value-description": "Total value generated from conversions", "conversions_value_all": "Conversion Value (All)", "conversions_value_all-description": "Total value generated from all types of conversions", "conversions_all": "Conversions (All)", "conversions_all-description": "Total number of conversions, including all tracked conversion actions", "cpc": "CPC", "cpc-description": "Cost per Click (CPC), calculated as the average amount you pay for each click on your ad", "cpm": "CPM", "cpm-description": "Cost per thousand impressions (CPM), the average cost for 1,000 ad impressions", "engagements": "Engagements", "engagements-description": "Total number of engagements, including any user interactions with your ads", "impressions": "Impressions", "impressions-description": "Total number of times your ad was displayed to users", "order_rate": "Order Rate", "order_rate-description": "The percentage of interactions that resulted in an order", "order_rate_all": "Order Rate (All)", "order_rate_all-description": "Order rate for all conversions", "revenue_per_click": "Revenue per Click", "revenue_per_click-description": "Average revenue generated from each click", "revenue_per_click_all": "Revenue per Click (All)", "revenue_per_click_all-description": "Revenue generated per click across all conversions", "roas": "ROAS", "roas-description": "Return on Ad Spend (ROAS), calculated as the total revenue divided by the total cost of ad interactions", "roas_all": "ROAS (All)", "roas_all-description": "ROAS for all conversions, including all types of conversions", "rpm": "RPM", "rpm-description": "Revenue per thousand impressions (RPM), a metric showing how much revenue your ads generate per 1,000 impressions", "rpm_all": "RPM (All)", "rpm_all-description": "Revenue per thousand impressions for all conversions", "value_per_conversion": "Value per Conversion", "value_per_conversion-description": "The average value of each conversion", "video_views": "Video Views", "video_views-description": "Total number of times your video ads were viewed", "view_through_conversions": "View Through Conversions", "view_through_conversions-description": "Conversions where a user viewed an ad but did not click, and later completed a conversion on your site", "avg_conversion_value_all": "Average Conversion Value (All)", "avg_conversion_value_all-description": "The average value of all conversions", "avg_conversion_value": "Average Conversion Value", "avg_conversion_value-description": "The average value of tracked conversions", "section-google-overview": "Account Summary"}, "google_analytics": {"total_revenue": "Total Revenue", "total_revenue-description": "The sum of revenue from purchases, subscriptions, and advertising (Purchase revenue plus Subscription revenue plus Ad revenue) minus refunded transaction revenue", "total_users": "Total Users", "total_users-description": "The number of distinct users who have logged at least one event, regardless of whether the site or app was in use when that event was logged.", "bounce_rate": "Bounce Rate", "bounce_rate-description": "The percentage of sessions that were not engaged. (Sessions minus Engaged Sessions) divided by Sessions.", "new_users": "New Users", "new_users-description": "The number of users who interacted with your site or launched your app for the first time.", "daily_active_users": "Daily Active Users", "daily_active_users-description": "The number of distinct active users on your site or app within a 1-day period.", "sessions": "Sessions", "sessions-description": "The number of sessions that began on your site or app (event triggered: session_start).", "engaged_sessions": "Engaged Sessions", "engaged_sessions-description": "The number of sessions that lasted longer than 10 seconds, had a key event, or had 2 or more screen views.", "screen_page_views": "Screen/Page Views", "screen_page_views-description": "The number of app screens or web pages viewed. Repeated views of a single page or screen are counted.", "total_items_added_to_cart": "Total Items Added to Cart", "total_items_added_to_cart-description": "The total number of units added to the cart across all users and items.", "ecommerce_purchases": "Ecommerce Purchases", "ecommerce_purchases-description": "The total number of purchase events that occurred.", "users_non_unique": "Non-Unique Users", "users_non_unique-description": "The total number of user interactions, including repeated visits by the same user.", "user_engagement_duration_secs": "User Engagement Duration (secs)", "user_engagement_duration_secs-description": "The total amount of time (in seconds) your website or app was in the foreground of users' devices.", "conversions": "Conversions", "conversions-description": "The number of completed actions that are counted as conversions (e.g., purchases, form submissions).", "transactions": "Transactions", "transactions-description": "The total number of completed purchase transactions.", "avg_order_value": "Average Order Value", "avg_order_value-description": "The average amount of revenue per transaction.", "conversion_rate": "Conversion Rate", "conversion_rate-description": "The percentage of sessions that resulted in a conversion.", "revenue_per_user": "Revenue per User", "revenue_per_user-description": "The average revenue generated per user.", "engagement_rate": "Engagement Rate", "engagement_rate-description": "The percentage of sessions that were engaged (Engaged Sessions divided by Total Sessions).", "new_customer_rate": "New Customer Rate", "new_customer_rate-description": "The percentage of users who were new customers (New Users divided by Total Users).", "revenue_per_session": "Revenue per Session", "revenue_per_session-description": "The average revenue generated per session.", "add_to_cart_rate": "Add to Cart Rate", "add_to_cart_rate-description": "The percentage of sessions where items were added to the cart (Total Items Added to Cart divided by Total Sessions).", "cart_abandonment_rate": "Cart Abandonment Rate", "cart_abandonment_rate-description": "The percentage of initiated shopping carts that were abandoned (Sessions with Cart but No Purchase divided by Total Sessions with Cart).", "engaged_conversion_rate": "Engaged Conversion Rate", "engaged_conversion_rate-description": "The percentage of engaged sessions that resulted in a conversion (Conversions divided by Engaged Sessions).", "purchase_frequency": "Purchase Frequency", "purchase_frequency-description": "The average number of purchases per user (Total Purchases divided by Total Users).", "user_engagement_duration_mins": "User Engagement Duration (Minutes)", "user_engagement_duration_mins-description": "The total time users spent interacting with your website or app, measured in minutes (User Engagement Duration divided by 60).", "average_engagement_time": "Average Engagement Time", "average_engagement_time-description": "The average time a user spent engaged with the site or app during a session (User Engagement Duration divided by Total Sessions).", "average_purchase_revenue": "Average Purchase Revenue", "average_purchase_revenue-description": "The average revenue generated per purchase (Total Revenue divided by Total Purchases).", "page_views_per_session": "Page Views per Session", "page_views_per_session-description": "The average number of pages viewed per session (Total Page Views divided by Total Sessions).", "purchase_conversion_rate": "Purchase Conversion Rate", "purchase_conversion_rate-description": "The percentage of sessions that resulted in a purchase (Purchases divided by Total Sessions)."}, "facebook": {"invalid-account-id": "Please enter a valid Ad Account ID"}, "website-traffic": "Website Traffic", "include-basket-size": "Include baskets with", "x-items-or-more": "{{x}} items or more", "exactly-x-items": "Exactly {{x}} items", "gen-combinations": "Generate combinations of size", "global_filters": {"title": "Global Filters", "desc": "Apply filters to all reports in your account", "excl-zero-value": "Exclude zero value orders", "excl-zero-value-desc": "Enable to exclude orders with zero value", "excl-cancelled": "Exclude cancelled orders", "excl-cancelled-desc": "Enable to exclude cancelled orders", "excl-unshipped": "Exclude unshipped orders", "excl-unshipped-desc": "Enable to exclude orders that have not been shipped", "excl-unfulfilled": "Exclude unfulfilled orders", "excl-unfulfilled-desc": "Enable to exclude orders with fulfillment status unfulfilled", "excl-pending": "Exclude unpaid orders", "excl-pending-desc": "Enable to exclude orders with financial status pending", "excl-authorized": "Exclude authorized orders", "excl-authorized-desc": "Enable to exclude orders with financial status authorized", "excl-order-tags": "Exclude order tags", "excl-order-tags-desc": "Exclude orders with these tags. To add multiple tags, separate them with a comma", "excl-customer-tags": "Exclude customer tags", "excl-customer-tags-desc": "Exclude customers with these tags. To add multiple tags, separate them with a comma"}, "ads": "Advertising", "view": "View", "view-details": "View Details", "succeeded": "Succeeded", "blended-summary": "Blended Summary", "campaign-performance": "Campaign performance", "entries-per-page": "entries per page", "campaign": "Campaign", "account": "Account", "ad-network-type": "Ad Network", "objective": "Objective", "currency": "<PERSON><PERSON><PERSON><PERSON>", "integration": {"setup": "Setup", "invalid-account-id": "Please enter a valid Ad Account ID", "finish-account-setup": "Finish account setup", "fb-desc": "Easily track your ad performance and audience reach in one place", "ga-desc": "View core metrics on visitor behavior and site activity", "google-ads-desc": "Monitor daily ad metrics to understand reach and effectiveness", "ga4": "Google Analytics (GA4)", "connection-status": "Connection status", "last-sync": "Last sync", "dt-connected": "Date connected", "disconnect-integration": "Disconnect integration", "disconnect-title": "Are you sure you want to disconnect it?", "disconnect-desc": "By disconnecting this integration, you will lose all the data and this action can't be undone", "disconnect-yes": "Yes, Disconnect", "unable-to-sync": "Unable to sync", "importing": "Importing", "sync-failed": "It seems the connection is currently not syncing. Please disconnect and reconnect the source or contact support", "auth-success": "Connected successfully"}, "blended": {"spend": "Blended Ad Spend", "spend-desc": "Total ad spend across all connected ad accounts", "catalog-spend": "Blended Catalog Ad Spend", "catalog-spend-desc": "Total ad spend across all connected ad accounts attributed to products in your catalog (Meta + Google)", "impressions": "Blended Impressions", "impressions-desc": "Total number of times your ads were displayed to users", "clicks": "Blended <PERSON>s", "clicks-desc": "Total number of clicks your ads received", "ctr": "Blended CTR", "ctr-desc": "Click-through rate (CTR), calculated as the percentage of people who clicked on your ad after seeing it. CTR = Clicks / Impressions", "cpc": "Blended CPC", "cpc-desc": "Cost per Click (CPC), calculated as the average amount you pay for each click on your ad", "roas": "Blended ROAS", "roas-desc": "Return on Ad Spend (ROAS), calculated as the total revenue in Shopify divided by the total Ad Spend", "catalog-roas": "Blended Catalog ROAS", "catalog-roas-desc": "Return on Ad Spend (ROAS) for your catalog products, calculated as total Shopify revenue / blended catalog ad spend", "cpm": "Blended CPM", "cpm-desc": "Cost per thousand impressions (CPM), the average cost for 1,000 ad impressions", "cac": "Blended CAC", "cac-desc": "Customer Acquisition Cost (CAC), calculated as the total ad spend divided by the total number of new customers acquired"}, "facebook_marketing": {"spend": "Spend", "spend-description": "Total spend on Facebook Ads for the selected time period", "clicks": "<PERSON>licks", "clicks-description": "The total number of clicks your ads received", "impressions": "Impressions", "impressions-description": "Total number of times your ad was displayed to users", "link_click": "<PERSON>", "link_click-description": "The total number of link clicks on your ad", "add_to_cart": "Add to Cart", "add_to_cart-description": "Number of times products were added to the cart", "purchase": "Purchases", "purchase-description": "Number of purchases made as a result of your ad", "landing_page_view": "Landing Page Views", "landing_page_view-description": "Number of times users viewed your landing page after clicking your ad", "website_lead": "Website Leads", "website_lead-description": "Number of leads generated on the website", "website_view_content": "Website Content Views", "website_view_content-description": "Total views of the website content", "website_add_to_cart": "Website Add to Cart", "website_add_to_cart-description": "Number of add-to-cart events from the website", "website_initiate_checkout": "Website Initiate Checkout", "website_initiate_checkout-description": "Number of checkout initiations on the website", "website_purchase": "Website Purchases", "website_purchase-description": "Number of purchases made on the website", "app_view_content": "App Content Views", "app_view_content-description": "Total views of app content", "app_add_to_cart": "App Add to Cart", "app_add_to_cart-description": "Add-to-cart events from the app", "app_initiate_checkout": "App Initiate Checkout", "app_initiate_checkout-description": "Checkout initiations in the app", "app_purchase": "App Purchases", "app_purchase-description": "Number of purchases made in the app", "add_to_cart_value": "Add to Cart Value", "add_to_cart_value-description": "Total value of products added to the cart", "website_purchase_value": "Website Purchase Value", "website_purchase_value-description": "Total value of website purchases", "app_purchase_value": "App Purchase Value", "app_purchase_value-description": "Total value of app purchases", "roas": "ROAS", "roas-description": "Return on Ad Spend (ROAS) for Facebook Ads", "cpc_link": "CPC (Link)", "cpc_link-description": "Cost per <PERSON>", "cpc_all": "CPC (All)", "cpc_all-description": "Cost per click across all interactions", "ctr_link": "CTR (Link)", "ctr_link-description": "Percentage of times people saw your ad and performed a link click", "ctr_all": "CTR (All)", "ctr_all-description": "Percentage of times people saw your ad and performed a click", "cost_per_purchase": "Cost per Purchase", "cost_per_purchase-description": "Average cost per purchase", "cost_per_website_purchase": "Cost per Website Purchase", "cost_per_website_purchase-description": "Average cost per purchase made on the website", "cost_per_add_to_cart": "Cost per Add to Cart", "cost_per_add_to_cart-description": "Average cost per add-to-cart event", "cpc": "CPC", "cpc-description": "Cost per Click (CPC), calculated as the average amount you pay for each click on your ad", "cpm": "CPM", "cpm-description": "Cost per thousand impressions (CPM), the average cost for 1,000 ad impressions", "ctr": "CTR", "ctr-description": "Click-through rate (CTR), calculated as the percentage of people who clicked on your ad after seeing it. CTR = Clicks / Impressions"}, "section-blended-ads": "Blended Ad Performance", "section-blended-ads-desc": "Track the performance of all your ad accounts in one place", "ad": {"spend": "Ad Spend", "spend-desc": "Total ad spend across all connected ad accounts", "impressions": "Impressions", "impressions-desc": "Total number of times your ads were displayed to users", "clicks": "<PERSON>licks", "clicks-desc": "Total number of clicks your ads received", "ctr": "CTR", "ctr-desc": "Click-through rate (CTR), calculated as the percentage of people who clicked on your ad after seeing it. CTR = Clicks / Impressions", "cpc": "CPC", "cpc-desc": "Cost per Click (CPC), calculated as the average amount you pay for each click on your ad", "roas": "ROAS", "roas-desc": "Return on Ad Spend (ROAS), calculated as the total conversion value divided by the total Ad Spend", "cpm": "CPM", "cpm-desc": "Cost per thousand impressions (CPM), the average cost for 1,000 ad impressions", "cac": "CAC", "cac-desc": "Customer Acquisition Cost (CAC), calculated as the total ad spend divided by total conversions", "order_rate": "Order Rate", "order_rate-desc": "The percentage of interactions that resulted in an order", "conversions_value": "Conversion Value", "conversions_value-desc": "Total value generated from conversions", "conversions": "Conversions", "conversions-desc": "Total number of conversions resulting from ad interactions", "aov": "AOV", "aov-desc": "Average Order Value (AOV), calculated as the total conversions value divided by the total number of conversions"}, "month": "month", "year": "year", "pricing": {"limited-orders-2000": "Up to 2000 active orders/month", "start-trial-7d": "Start your 7-day free trial", "unlimited-orders": "Unlimited orders", "tagline": "Powerful Insights, Unbeatable pricing", "integration-summary": "Facebook Ads, Google Ads and GA4 integration", "multiple-ad-accounts": "Multiple Ad Accounts Support", "automated-reports": "KPI summary on Email and Slack", "annual-offer": "Get 2 months free on yearly billing", "limited-data-3m": "Historical Data - Last 3 months", "data-unlimited": "Historical Data - All time", "new-vs-returning": "In Depth - New vs Returning", "includes": "Includes: ", "basic-plus": "Everything in Basic, plus: ", "growth-plus": "Everything in Growth, plus: ", "current-plan": "Current Plan"}, "something-went-wrong-di2": "Something went wrong while processing your request.", "something-went-wrong-head": "Here’s what you can do:", "something-went-wrong-li1": "Verify your payment details and try again.", "something-went-wrong-li2": "Reach out to support if the issue persists.", "plan-active-desc2": "You’re all set to explore Datadrew! 🚀", "plan-active-desc-heading": "Kickstart your journey with a free onboarding call with our founder:", "plan-active-desc-li1": "Get personalized guidance.", "plan-active-desc-li2": "Learn platform best practices.", "book-my-call": "Book my onboarding call", "ask-prashna": "<PERSON>", "prashna": {"welcome-message": "Ask me a question about your store", "composer-placeholder": "Type your question...", "suggestion-1": "How many orders were placed in last 3 months?", "suggestion-2": "What are my top 5 products by sales last quarter?", "suggestion-3": "What is my total revenue for the last 6 months month over month?", "new-question": "New Question", "library": "Library", "history": "History", "chats": "Chats", "prompt-library.title": "Prompt Library", "prompt-library.prompt-1": "Show me the top-performing countries based on total sales over the last quarter?", "prompt-library.prompt-2": "How does revenue from first-time buyers compare to repeat customers in the past year?", "prompt-library.prompt-3": "What is the average time between the second and third order of new customers?", "prompt-library.prompt-4": "Give me a cohort analysis of customers acquired during the previous calendar year.", "prompt-library.prompt-5": "Show me the top-selling product in the last 4 weeks across both new and returning users", "prompt-library.prompt-6": "Which products are most likely to be reordered within 30 days?", "prompt-library.prompt-7": "Which products are most often purchased together over the last year?", "prompt-library.prompt-8": "What percentage of my new customer orders for the last 30 days used a discount code?", "prompt-library.prompt-9": "What are the most used discount codes over the last 30 days on Shopify? Show percentage too.", "prompt-library.prompt-10": "What is the value of refunds for the last 30 days?", "threads.all-conversations": "All Conversations", "threads.view-all": "View All", "close": "Close"}, "total_price": "Order Total", "discount_percentage": "Discount Percentage", "discount_codes": "Discount Code", "op_is_greater_than": "is greater than", "op_is_equal_to": "is equal to", "op_is_not_equal_to": "is not equal to", "op_is_less_than": "is less than", "op_is_greater_than_equal": "is greater than or equal to", "op_is_less_than_equal": "is less than or equal to", "op_is_between": "is between", "total-price-tooltip": "Include or exclude customers based on their first order total compared to the specified value", "discount-percentage-tooltip": "Include or exclude customers based on the discount percentage of their first order compared to the specified value", "discount-codes-tooltip": "Include or exclude customers based on whether their first order discount code matches the specified value", "role": "Role", "stores": "Stores", "invite-sent": "<PERSON><PERSON><PERSON>", "revoke": "Revoke", "members-settings-title": "Members", "members-settings-description": "Here you can manage your workspace members and their permissions.", "revoke-invitation-failed": "Revoking invitation failed", "revoke-invitation-success": "Invitation revoked successfully", "revoke-invitation-warning": "Revoking will remove this invitation permanently. You will need to re-invite this member again.", "send-invitation-failed": "Sending invitation failed", "send-invitation-success": "Invitation sent successfully", "invite-team-member-heading": "Invite Team Member", "invite-team-member-description": "Invite a team member to join your workspace. They will receive an email with a link to sign up.", "invite-team-member": "Invite Team Member", "invite-member": "Invite Member", "invitation-title": "You've been invited to join a Datadrew 🎉", "send-invite": "Send Invite", "all-stores": "All Stores", "access": "Access", "select-shops": "Select Shops", "admin": "Admin", "member": "Member", "name-email": "Name / Email", "edit-team-member-heading": "Edit Team Member", "edit-team-member-description": "Edit the permissions and details of a team member in your workspace.", "update-team-member-failed": "Updating team member failed", "update-team-member-success": "Updated team member successfully", "invite-admin-disclaimer": "Admins will get access to all shops in the workspace and will be able to invite other members.", "ask-ai": "Ask AI", "mtd": "This Month", "data-coverage": "Data Coverage", "analyze-by": "Analyze by", "product-name": "Product Name", "ask-ai-desc": "Ask AI to get instant answers and actionable insights from your store’s data—without digging through dashboards.", "showing-entries": "Showing {{start}} to {{end}} of {{total}} entries", "no-entries-found": "No entries found", "units-sold": "Units Sold", "units-sold-description": "Total number of units sold", "avg-price": "Avg Price", "avg-price-description": "Average price of the product", "request-access": "Request Access", "request-access-success": "Successfully requested access to the feature. You will be notified once the feature is enabled.", "requesting-access": "Requesting access...", "request-sent": "Request sent", "facebook-campaigns": "Facebook Campaigns", "please-enter-email-first": "Please enter your email address first", "password-reset-email-sent": "Password reset email sent! Check your inbox.", "finish-setting-up-workspace": "Finish Setting up your Workspace", "setup-workspace": "Setup Workspace", "connect-email-invite-team-description": "Connect your email to invite team, access multi store dashboard and more!", "email-placeholder": "<EMAIL>", "continue": "Continue", "do-it-later": "Do it later", "forgot-password": "Forgot your password?", "google-ads-products-info-disclaimer": "Google Ads provides information on product level for specific types of campaigns, like 'SHOPPING'. If you don't see metrics on product level, please refer Google Ads docs and update your campaigns accordingly.", "sidenav": {"acquisition": "Acquisition", "retention": "Retention", "products": "Products", "ads": "Ads", "blended-summary": "Blended Summary", "industry-benchmarks": "Industry Benchmarks", "new-chat": "New Chat", "discover": "Discover", "recent-chats": "Recent Chats", "search-stores": "Search stores...", "no-stores-found": "No stores found", "pinned": "Pinned", "new": "New", "start-free-trial": "Start Free Trial", "purchase-frequency": "Purchase Frequency"}, "performance": {"top-budget-wasting-products": "Ad Spend Wasters", "top-budget-wasting-tooltip": "High spend, weak performance: ROAS below 50% of shop average. Showing the 5 biggest spenders.", "top-hero-products": "Hero Products", "top-hero-tooltip": "High investment, high return: best ROAS out of the 20 highest-spend products.", "top-potential-products": "Potential Products", "top-potential-tooltip": "Scaling candidates: products spending less than average but performing well. Top 5 by spend then ROAS.", "product-performance-scatterplot": "Spend vs ROAS (by Product)", "ad-spend": "Ad Spend", "roas": "ROAS", "revenue": "Revenue", "orders": "Orders", "min-spend": "<PERSON>d", "max-spend": "<PERSON>d", "min-roas": "<PERSON>", "max-roas": "Max ROAS", "reset": "Reset", "no-products-match": "No products match the current filters", "adjust-range-inputs": "Adjust the range inputs above or reset to see data.", "reset-filters": "Reset Filters", "blended-catalog-ad-spend": "Blended Catalog Ad Spend", "total-ad-spend": "Total Ad Spend", "blended-catalog-roas": "Blended Catalog ROAS", "calculated-roas": "Calculated ROAS", "average-roas": "Average ROAS", "average-spend": "Average Spend", "no-image-available": "No image available", "product": "Product", "unknown-product": "Unknown Product", "roas-label": "ROAS:", "ad-spend-label": "Ad Spend:", "revenue-label": "Revenue:", "close": "Close", "no-ad-spend": "No Ad Spend", "vs-avg": "vs avg", "spend-label": "Spend:", "no-data-available": "No data available", "product-name-text": "Product Name Text", "search-by": "Search by", "product-name-id": "product name/ID...", "product-type": "product type...", "vendor": "vendor...", "active-column-filters": "Active Column Filters", "clear-all-filters": "Clear All Filters", "catalog-summary": "Catalog Summary", "no-products-match-current": "No products match the current", "filters-and-search": "filters and search", "filters": "filters", "search": "search", "try-adjusting-column-filters": "Try adjusting the column filters above or", "try-different-search-term": "Try a different search term or", "clear-search": "clear search"}, "profile": "Profile", "clear": "Clear", "value": "Value"}